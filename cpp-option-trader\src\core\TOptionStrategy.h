#ifndef TOPTIONSTRATEGY_H
#define TOPTIONSTRATEGY_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <memory>
#include <vector>
#include <deque>
#include <map>
#include "../data/DataTypes.h"

// 기술적 지표 구조체
struct TStochasticData {
    double k_percent;
    double d_percent;
    bool is_golden_cross;
    bool is_dead_cross;
    
    TStochasticData() : k_percent(0.0), d_percent(0.0), 
                       is_golden_cross(false), is_dead_cross(false) {}
};

struct TCCIData {
    double cci_value;
    bool is_buy_signal;
    bool is_sell_signal;
    
    TCCIData() : cci_value(0.0), is_buy_signal(false), is_sell_signal(false) {}
};

struct TDMIData {
    double di_plus;
    double di_minus;
    double adx;
    bool trend_strong;
    bool bullish_trend;
    
    TDMIData() : di_plus(0.0), di_minus(0.0), adx(0.0), 
                trend_strong(false), bullish_trend(false) {}
};

// 캔들 데이터 구조체
struct TCandleData {
    TDateTime timestamp;
    double open;
    double high;
    double low;
    double close;
    int volume;
    
    TCandleData() : open(0.0), high(0.0), low(0.0), close(0.0), volume(0) {}
};

/**
 * TOptionStrategy
 * 옵션 거래 전략 엔진 - 기술적 지표 기반 매매 신호 생성
 * Python의 OptionStrategy 클래스를 C++Builder로 포팅
 */
class TOptionStrategy : public TObject {
private:
    // 전략 파라미터
    TStrategyParams FParams;
    
    // 가격 데이터 저장 (링 버퍼)
    std::map<AnsiString, std::deque<TCandleData>> FPriceData;
    std::map<AnsiString, std::deque<double>> FClosePrices;
    
    // 지표 데이터 캐시
    std::map<AnsiString, TStochasticData> FStochasticCache;
    std::map<AnsiString, TCCIData> FCCICache;
    std::map<AnsiString, TDMIData> FDMICache;
    
    // 설정
    int FMaxDataPoints;           // 최대 데이터 포인트 수
    bool FIsEnabled;
    
    // 크리티컬 섹션
    TCriticalSection* FCriticalSection;
    
    // 기술적 지표 계산 메서드
    TStochasticData CalculateStochastic(const AnsiString& code);
    TCCIData CalculateCCI(const AnsiString& code);
    TDMIData CalculateDMI(const AnsiString& code);
    
    // 보조 계산 함수들
    double CalculateTypicalPrice(const TCandleData& candle);
    double CalculateTrueRange(const TCandleData& current, const TCandleData& previous);
    double CalculateDirectionalMovement(const TCandleData& current, const TCandleData& previous, bool isPlus);
    double CalculateEMA(const std::vector<double>& data, int period);
    double CalculateSMA(const std::vector<double>& data, int period);
    double CalculateStandardDeviation(const std::vector<double>& data, double mean);
    
    // 신호 생성 로직
    bool CheckBuyConditions(const AnsiString& code);
    bool CheckSellConditions(const AnsiString& code);
    bool IsTrendFavorable(const AnsiString& code);
    bool IsVolatilityAcceptable(const AnsiString& code);
    
    // 데이터 관리
    void AddPriceData(const AnsiString& code, const TCandleData& candle);
    void CleanOldData(const AnsiString& code);
    bool HasSufficientData(const AnsiString& code, int requiredPeriod);
    
public:
    // 생성자/소멸자
    __fastcall TOptionStrategy();
    __fastcall ~TOptionStrategy();
    
    // 초기화
    void Initialize(const TStrategyParams& params);
    void SetStrategyParams(const TStrategyParams& params);
    TStrategyParams GetStrategyParams() const { return FParams; }
    
    // 전략 제어
    void Enable() { FIsEnabled = true; }
    void Disable() { FIsEnabled = false; }
    bool IsEnabled() const { return FIsEnabled; }
    
    // 가격 데이터 입력
    void AddCandle(const AnsiString& code, const TCandleData& candle);
    void AddPrice(const AnsiString& code, double price, int volume = 0);
    void UpdateCurrentCandle(const AnsiString& code, double price, int volume);
    
    // 매매 신호 생성
    bool GenerateBuySignal(const AnsiString& code);
    bool GenerateSellSignal(const AnsiString& code);
    bool ShouldEnterPosition(const AnsiString& code);
    bool ShouldExitPosition(const AnsiString& code);
    
    // 지표 조회
    TStochasticData GetStochastic(const AnsiString& code);
    TCCIData GetCCI(const AnsiString& code);
    TDMIData GetDMI(const AnsiString& code);
    
    // 지표 강제 계산
    void CalculateAllIndicators(const AnsiString& code);
    void RefreshIndicators();
    
    // 데이터 조회
    std::vector<TCandleData> GetPriceData(const AnsiString& code, int count = 50);
    TCandleData GetLastCandle(const AnsiString& code);
    double GetLastPrice(const AnsiString& code);
    
    // 통계 및 분석
    double GetAverageVolatility(const AnsiString& code, int period = 20);
    double GetPriceChange(const AnsiString& code, int period = 1);
    double GetPriceChangePercent(const AnsiString& code, int period = 1);
    
    // 전략 상태
    int GetDataPointCount(const AnsiString& code);
    bool IsDataSufficient(const AnsiString& code);
    TDateTime GetLastUpdateTime(const AnsiString& code);
    
    // 파라미터 동적 조정
    void AdjustStochasticParams(int kPeriod, int dPeriod, 
                               double buyThreshold, double sellThreshold);
    void AdjustCCIParams(int period, double buyThreshold, double sellThreshold);
    void AdjustDMIParams(int period, double adxThreshold);
    
    // 백테스팅 지원
    bool BacktestStrategy(const AnsiString& code, 
                         const std::vector<TCandleData>& historicalData);
    double CalculateStrategyPerformance(const AnsiString& code, 
                                       const std::vector<TCandleData>& data);
    
    // 데이터 저장/로드
    bool SaveStrategyData(const AnsiString& filename);
    bool LoadStrategyData(const AnsiString& filename);
    void ClearAllData();
    void ClearCodeData(const AnsiString& code);
    
    // 디버깅 및 로깅
    AnsiString GetIndicatorStatus(const AnsiString& code);
    void WriteLog(TLogLevel level, const AnsiString& message);
    
    // 실시간 분석
    void OnHogaReceived(const THogaData& hoga);
    void OnJeobsuReceived(const TJeobsuData& jeobsu);
    void OnMinuteCandle(const AnsiString& code, const TCandleData& candle);
    
    // 이벤트 선언
    __property TNotifyEvent OnBuySignal = { read = FOnBuySignal, write = FOnBuySignal };
    __property TNotifyEvent OnSellSignal = { read = FOnSellSignal, write = FOnSellSignal };
    __property TNotifyEvent OnIndicatorUpdated = { read = FOnIndicatorUpdated, write = FOnIndicatorUpdated };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnBuySignal;
    TNotifyEvent FOnSellSignal;
    TNotifyEvent FOnIndicatorUpdated;
    
    // 내부 상태 추적
    std::map<AnsiString, TDateTime> FLastUpdateTimes;
    std::map<AnsiString, bool> FLastBuySignals;
    std::map<AnsiString, bool> FLastSellSignals;
};

#endif // TOPTIONSTRATEGY_H