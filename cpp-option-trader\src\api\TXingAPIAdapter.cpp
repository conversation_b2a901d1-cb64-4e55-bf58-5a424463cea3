#include "TXingAPIAdapter.h"

// 생성자
__fastcall TXingAPIAdapter::TXingAPIAdapter() : TXingAPIWrapper() {
    FLSXingAPI = std::make_unique<TLSXingAPI>();
    
    // TLSXingAPI 이벤트 핸들러 연결
    FLSXingAPI->OnConnected = OnLSConnected;
    FLSXingAPI->OnDisconnected = OnLSDisconnected;
    FLSXingAPI->OnLoginResult = OnLSLoginResult;
    FLSXingAPI->OnOptionHoga = OnLSOptionHoga;
    FLSXingAPI->OnOptionExecution = OnLSOptionExecution;
    FLSXingAPI->OnOrderResult = OnLSOrderResult;
    FLSXingAPI->OnQueryReceived = OnLSQueryReceived;
    FLSXingAPI->OnRealDataReceived = OnLSRealDataReceived;
}

// 소멸자
__fastcall TXingAPIAdapter::~TXingAPIAdapter() {
    if (FLSXingAPI) {
        FLSXingAPI.reset();
    }
}

// 연결
bool TXingAPIAdapter::Connect(bool isReal) {
    try {
        return FLSXingAPI->ConnectServer(isReal);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "연결 실패: " + e.Message);
        }
        return false;
    }
}

// 연결 해제
void TXingAPIAdapter::Disconnect() {
    try {
        FLSXingAPI->DisconnectServer();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "연결 해제 실패: " + e.Message);
        }
    }
}

// 로그인
bool TXingAPIAdapter::Login(const AnsiString& userId, const AnsiString& password, 
                           const AnsiString& certPassword) {
    try {
        return FLSXingAPI->Login(userId, password, certPassword);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "로그인 실패: " + e.Message);
        }
        return false;
    }
}

// 로그아웃
void TXingAPIAdapter::Logout() {
    try {
        FLSXingAPI->Logout();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "로그아웃 실패: " + e.Message);
        }
    }
}

// 실서버 여부
bool TXingAPIAdapter::IsRealServer() const {
    // TLSXingAPI에서 실서버 상태 조회 (구현 필요)
    return FLSXingAPI->GetConnectionState() == TConnectionState::LOGGED_IN;
}

// 계좌 목록 조회
std::vector<AnsiString> TXingAPIAdapter::GetAccountList() {
    try {
        return FLSXingAPI->GetAccountList();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "계좌 목록 조회 실패: " + e.Message);
        }
        return std::vector<AnsiString>();
    }
}

// 계좌 설정
bool TXingAPIAdapter::SetAccount(const AnsiString& accountNumber) {
    try {
        return FLSXingAPI->SetAccount(accountNumber);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "계좌 설정 실패: " + e.Message);
        }
        return false;
    }
}

// 현재 계좌 조회
AnsiString TXingAPIAdapter::GetCurrentAccount() const {
    return FLSXingAPI->GetCurrentAccount();
}

// 옵션 종목 코드 조회
std::vector<AnsiString> TXingAPIAdapter::GetOptionCodes() {
    try {
        // TLSXingAPI의 RequestOptionCodeList 사용
        AnsiString requestId = FLSXingAPI->RequestOptionCodeList();
        
        // 결과는 OnQueryReceived 이벤트에서 처리됨
        // 임시로 빈 벡터 반환 (실제로는 캐시된 데이터 반환해야 함)
        return std::vector<AnsiString>();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "옵션 종목 조회 실패: " + e.Message);
        }
        return std::vector<AnsiString>();
    }
}

// 옵션 호가 구독
bool TXingAPIAdapter::SubscribeOptionHoga(const AnsiString& code) {
    try {
        return FLSXingAPI->SubscribeOptionHoga(code);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "옵션 호가 구독 실패: " + e.Message);
        }
        return false;
    }
}

// 옵션 체결 구독
bool TXingAPIAdapter::SubscribeOptionJeobsu(const AnsiString& code) {
    try {
        return FLSXingAPI->SubscribeOptionJeobsu(code);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "옵션 체결 구독 실패: " + e.Message);
        }
        return false;
    }
}

// 실시간 데이터 구독 해제
bool TXingAPIAdapter::UnsubscribeRealData(const AnsiString& code) {
    try {
        return FLSXingAPI->UnsubscribeRealData(code);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "구독 해제 실패: " + e.Message);
        }
        return false;
    }
}

// 주문 전송
AnsiString TXingAPIAdapter::SendOrder(const AnsiString& code, TTradeType tradeType, 
                                     int quantity, double price, TOrderType orderType) {
    try {
        return FLSXingAPI->SendOrder(code, tradeType, quantity, price, orderType);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "주문 전송 실패: " + e.Message);
        }
        return "";
    }
}

// 주문 취소
bool TXingAPIAdapter::CancelOrder(const AnsiString& orderId) {
    try {
        AnsiString requestId = FLSXingAPI->CancelOrder(orderId);
        return !requestId.IsEmpty();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "주문 취소 실패: " + e.Message);
        }
        return false;
    }
}

// 주문 정정
bool TXingAPIAdapter::ModifyOrder(const AnsiString& orderId, double newPrice, int newQuantity) {
    try {
        AnsiString requestId = FLSXingAPI->ModifyOrder(orderId, newPrice, newQuantity);
        return !requestId.IsEmpty();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "주문 정정 실패: " + e.Message);
        }
        return false;
    }
}

// 계좌 정보 조회
std::shared_ptr<TAccountInfo> TXingAPIAdapter::GetAccountInfo() {
    try {
        // TLSXingAPI의 RequestAccountInfo 사용
        AnsiString requestId = FLSXingAPI->RequestAccountInfo();
        
        // 결과는 이벤트 핸들러에서 처리
        // 임시로 nullptr 반환 (실제로는 캐시된 데이터 반환해야 함)
        return nullptr;
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "계좌 정보 조회 실패: " + e.Message);
        }
        return nullptr;
    }
}

// 잔고 정보 조회
TPositionMap TXingAPIAdapter::GetPositions() {
    try {
        // TLSXingAPI의 RequestPositionInfo 사용
        AnsiString requestId = FLSXingAPI->RequestPositionInfo();
        
        // 결과는 이벤트 핸들러에서 처리
        // 임시로 빈 맵 반환
        return TPositionMap();
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "잔고 정보 조회 실패: " + e.Message);
        }
        return TPositionMap();
    }
}

// 미체결 주문 조회
std::vector<TOrderInfo> TXingAPIAdapter::GetPendingOrders() {
    // 미구현 - 필요시 TLSXingAPI에 추가 메서드 구현
    return std::vector<TOrderInfo>();
}

// 자동 재연결 설정
void TXingAPIAdapter::SetAutoReconnect(bool enable) {
    FLSXingAPI->SetAutoReconnect(enable);
}

// 설정 파일 경로 설정
void TXingAPIAdapter::SetConfigPath(const AnsiString& path) {
    FLSXingAPI->SetConfigPath(path);
}

// 추가 최적화된 메서드들
AnsiString TXingAPIAdapter::RequestOptionCurrentPrice(const AnsiString& code) {
    try {
        return FLSXingAPI->RequestCurrentPrice(code);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "옵션 현재가 요청 실패: " + e.Message);
        }
        return "";
    }
}

AnsiString TXingAPIAdapter::RequestOptionChartData(const AnsiString& code, 
                                                  const AnsiString& period, int count) {
    try {
        return FLSXingAPI->RequestChartData(code, period, count);
    }
    catch (const Exception& e) {
        if (FOnError) {
            FOnError(this, "차트 데이터 요청 실패: " + e.Message);
        }
        return "";
    }
}

bool TXingAPIAdapter::SubscribeOptionCurrentPrice(const AnsiString& code) {
    // 현재가 실시간 구독은 TLSXingAPI에 추가 구현 필요
    return true;
}

// 진단 및 모니터링 메서드들
TConnectionState TXingAPIAdapter::GetDetailedConnectionState() const {
    return FLSXingAPI->GetConnectionState();
}

AnsiString TXingAPIAdapter::GetDetailedStateDescription() const {
    return FLSXingAPI->GetStateDescription();
}

TDateTime TXingAPIAdapter::GetLastConnectTime() const {
    return FLSXingAPI->GetLastConnectTime();
}

AnsiString TXingAPIAdapter::GetLastError() const {
    return FLSXingAPI->GetLastError();
}

// TLSXingAPI 이벤트 핸들러들
void __fastcall TXingAPIAdapter::OnLSConnected(TObject* Sender, bool connected) {
    // TXingAPIWrapper의 기존 이벤트로 변환
    if (connected && FOnLogin) {
        FOnLogin(this, true, "서버 연결 완료");
    }
}

void __fastcall TXingAPIAdapter::OnLSDisconnected(TObject* Sender, bool connected) {
    if (FOnError) {
        FOnError(this, "서버 연결이 끊어졌습니다");
    }
}

void __fastcall TXingAPIAdapter::OnLSLoginResult(TObject* Sender, bool success, 
                                                const AnsiString& message) {
    if (FOnLogin) {
        FOnLogin(this, success, message);
    }
}

void __fastcall TXingAPIAdapter::OnLSOptionHoga(const TOptionHoga& data) {
    if (FOnHoga) {
        THogaData hogaData = ConvertToHogaData(data);
        FOnHoga(this, hogaData);
    }
}

void __fastcall TXingAPIAdapter::OnLSOptionExecution(const TOptionExecution& data) {
    if (FOnJeobsu) {
        TJeobsuData jeobsuData = ConvertToJeobsuData(data);
        FOnJeobsu(this, jeobsuData);
    }
}

void __fastcall TXingAPIAdapter::OnLSOrderResult(const TOrderResult& data) {
    if (FOnOrder) {
        FOnOrder(this, data.orderId, data.result, data.message);
    }
}

void __fastcall TXingAPIAdapter::OnLSQueryReceived(const AnsiString& trCode, int blockCount) {
    // TR 조회 결과 처리 - 필요시 추가 이벤트 생성
}

void __fastcall TXingAPIAdapter::OnLSRealDataReceived(const AnsiString& trCode) {
    // 실시간 데이터 수신 로깅 등
}

// 데이터 변환 메서드들
THogaData TXingAPIAdapter::ConvertToHogaData(const TOptionHoga& lsHoga) {
    THogaData hogaData;
    hogaData.code = lsHoga.code;
    
    // 호가 데이터 변환
    for (int i = 0; i < 10; i++) {
        hogaData.sellPrices[i] = lsHoga.sellPrices[i];
        hogaData.sellQuantities[i] = lsHoga.sellQuantities[i];
        hogaData.buyPrices[i] = lsHoga.buyPrices[i];
        hogaData.buyQuantities[i] = lsHoga.buyQuantities[i];
    }
    
    hogaData.totalSellQuantity = lsHoga.totalSellQuantity;
    hogaData.totalBuyQuantity = lsHoga.totalBuyQuantity;
    hogaData.timestamp = Now();
    
    return hogaData;
}

TJeobsuData TXingAPIAdapter::ConvertToJeobsuData(const TOptionExecution& lsExecution) {
    TJeobsuData jeobsuData;
    jeobsuData.code = lsExecution.code;
    jeobsuData.price = lsExecution.price;
    jeobsuData.quantity = lsExecution.quantity;
    jeobsuData.change = lsExecution.change;
    jeobsuData.volume = lsExecution.volume;
    jeobsuData.timestamp = Now();
    
    // 시간 문자열 파싱 (필요시)
    if (!lsExecution.time.IsEmpty()) {
        // lsExecution.time을 TDateTime으로 변환하는 로직 추가
    }
    
    return jeobsuData;
}