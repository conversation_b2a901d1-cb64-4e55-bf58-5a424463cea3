#ifndef TRSTRUCTURES_H
#define TRSTRUCTURES_H

#pragma pack(push, 1)

// =============================================================================
// LS증권 XingAPI TR 구조체 정의
// reference/코드 폴더의 실제 TR 정의를 기반으로 구성
// =============================================================================

// 선물옵션 정상주문 (CFOAT00100)
typedef struct _CFOAT00100InBlock1 {
    char AcntNo[20];           // 계좌번호
    char Pwd[8];               // 비밀번호  
    char FnoIsuNo[12];         // 선물옵션종목번호
    char BnsTpCode[1];         // 매매구분 (1:매도, 2:매수)
    char FnoOrdprcPtnCode[2];  // 선물옵션호가유형코드
    char FnoOrdPrc[27];        // 선물옵션주문가격
    char OrdQty[16];           // 주문수량
} CFOAT00100InBlock1;

typedef struct _CFOAT00100OutBlock2 {
    char RecCnt[5];            // 레코드갯수
    char OrdNo[10];            // 주문번호
    char BrnNm[40];            // 지점명
    char AcntNm[40];           // 계좌명
    char IsuNm[50];            // 종목명
    char OrdAbleAmt[16];       // 주문가능금액
    char MnyOrdAbleAmt[16];    // 현금주문가능금액
    char OrdMgn[16];           // 주문증거금
    char MnyOrdMgn[16];        // 현금주문증거금
    char OrdAbleQty[16];       // 주문가능수량
} CFOAT00100OutBlock2;

// 선물옵션 정정주문 (CFOAT00200)
typedef struct _CFOAT00200InBlock1 {
    char AcntNo[20];           // 계좌번호
    char Pwd[8];               // 비밀번호
    char FnoIsuNo[12];         // 선물옵션종목번호
    char OrgOrdNo[10];         // 원주문번호
    char FnoOrdprcPtnCode[2];  // 선물옵션호가유형코드
    char FnoOrdPrc[27];        // 선물옵션주문가격
    char MdfyQty[16];          // 정정수량
} CFOAT00200InBlock1;

typedef struct _CFOAT00200OutBlock2 {
    char RecCnt[5];            // 레코드갯수
    char OrdNo[10];            // 주문번호
    char BrnNm[40];            // 지점명
    char AcntNm[40];           // 계좌명
    char IsuNm[50];            // 종목명
    char OrdAbleAmt[16];       // 주문가능금액
    char MnyOrdAbleAmt[16];    // 현금주문가능금액
    char OrdMgn[16];           // 주문증거금액
    char MnyOrdMgn[16];        // 현금주문증거금액
    char OrdAbleQty[16];       // 주문가능수량
} CFOAT00200OutBlock2;

// 선물옵션 취소주문 (CFOAT00300)
typedef struct _CFOAT00300InBlock1 {
    char AcntNo[20];           // 계좌번호
    char Pwd[8];               // 비밀번호
    char FnoIsuNo[12];         // 선물옵션종목번호
    char OrgOrdNo[10];         // 원주문번호
    char CancQty[16];          // 취소수량
} CFOAT00300InBlock1;

typedef struct _CFOAT00300OutBlock2 {
    char RecCnt[5];            // 레코드갯수
    char OrdNo[10];            // 주문번호
    char BrnNm[40];            // 지점명
    char AcntNm[40];           // 계좌명
    char IsuNm[50];            // 종목명
    char OrdAbleAmt[16];       // 주문가능금액
    char MnyOrdAbleAmt[16];    // 현금주문가능금액
    char OrdMgn[16];           // 주문증거금액
    char MnyOrdMgn[16];        // 현금주문증거금액
    char OrdAbleQty[16];       // 주문가능수량
} CFOAT00300OutBlock2;

// KOSPI200옵션호가 실시간 (OH0)
typedef struct _OH0_InBlock {
    char optcode[8];           // 단축코드
} OH0_InBlock;

typedef struct _OH0_OutBlock {
    char hotime[6];            // 호가시간
    char offerho1[6];          // 매도호가1
    char bidho1[6];            // 매수호가1
    char offerrem1[7];         // 매도호가수량1
    char bidrem1[7];           // 매수호가수량1
    char offercnt1[5];         // 매도호가건수1
    char bidcnt1[5];           // 매수호가건수1
    char offerho2[6];          // 매도호가2
    char bidho2[6];            // 매수호가2
    char offerrem2[7];         // 매도호가수량2
    char bidrem2[7];           // 매수호가수량2
    char offercnt2[5];         // 매도호가건수2
    char bidcnt2[5];           // 매수호가건수2
    char offerho3[6];          // 매도호가3
    char bidho3[6];            // 매수호가3
    char offerrem3[7];         // 매도호가수량3
    char bidrem3[7];           // 매수호가수량3
    char offercnt3[5];         // 매도호가건수3
    char bidcnt3[5];           // 매수호가건수3
    char offerho4[6];          // 매도호가4
    char bidho4[6];            // 매수호가4
    char offerrem4[7];         // 매도호가수량4
    char bidrem4[7];           // 매수호가수량4
    char offercnt4[5];         // 매도호가건수4
    char bidcnt4[5];           // 매수호가건수4
    char offerho5[6];          // 매도호가5
    char bidho5[6];            // 매수호가5
    char offerrem5[7];         // 매도호가수량5
    char bidrem5[7];           // 매수호가수량5
    char offercnt5[5];         // 매도호가건수5
    char bidcnt5[5];           // 매수호가건수5
    char totofferrem[7];       // 매도호가총수량
    char totbidrem[7];         // 매수호가총수량
    char totoffercnt[5];       // 매도호가총건수
    char totbidcnt[5];         // 매수호가총건수
    char optcode[8];           // 단축코드
    char danhochk[1];          // 단일가호가여부
    char alloc_gubun[1];       // 배분적용구분
} OH0_OutBlock;

// KOSPI200옵션체결 실시간 (OC0)
typedef struct _OC0_InBlock {
    char optcode[8];           // 단축코드
} OC0_InBlock;

typedef struct _OC0_OutBlock {
    char chetime[6];           // 체결시간
    char sign[1];              // 전일대비구분
    char change[6];            // 전일대비
    char drate[6];             // 등락율
    char price[6];             // 현재가
    char open[6];              // 시가
    char high[6];              // 고가
    char low[6];               // 저가
    char cgubun[1];            // 체결구분
    char cvolume[6];           // 체결량
    char volume[12];           // 누적거래량
    char value[12];            // 누적거래대금
    char mdvolume[12];         // 매도누적체결량
    char mdchecnt[8];          // 매도누적체결건수
    char msvolume[12];         // 매수누적체결량
    char mschecnt[8];          // 매수누적체결건수
    char cpower[9];            // 체결강도
    char offerho1[6];          // 매도호가1
    char bidho1[6];            // 매수호가1
    char openyak[8];           // 미결제약정수량
    char k200jisu[6];          // KOSPI200지수
    char eqva[7];              // KOSPI등가
    char theoryprice[6];       // 이론가
    char impv[6];              // 내재변동성
    char openyakcha[8];        // 미결제약정증감
    char timevalue[6];         // 시간가치
    char jgubun[2];            // 장운영정보
    char jnilvolume[12];       // 전일동시간대거래량
    char optcode[8];           // 단축코드
} OC0_OutBlock;

// 선물/옵션 체결/미체결 조회 (t0434)
typedef struct _t0434InBlock {
    char accno[11];            // 계좌번호
    char passwd[8];            // 비밀번호
    char expcode[8];           // 종목번호
    char chegb[1];             // 체결구분
    char sortgb[1];            // 정렬순서
    char cts_ordno[7];         // CTS_주문번호
} t0434InBlock;

typedef struct _t0434OutBlock {
    char cts_ordno[7];         // CTS_주문번호
} t0434OutBlock;

typedef struct _t0434OutBlock1 {
    char ordno[7];             // 주문번호
    char orgordno[7];          // 원주문번호
    char medosu[10];           // 구분
    char ordgb[20];            // 유형
    char qty[9];               // 주문수량
    char price[9];             // 주문가격
    char cheqty[9];            // 체결수량
    char cheprice[9];          // 체결가격
    char ordrem[9];            // 미체결잔량
    char status[10];           // 상태
    char ordtime[8];           // 주문시간
    char ordermtd[10];         // 주문매체
    char expcode[8];           // 종목번호
    char rtcode[3];            // 사유코드
    char sysprocseq[10];       // 처리순번
    char hogatype[1];          // 호가타입
} t0434OutBlock1;

// 선물/옵션 잔고평가 (t0441)
typedef struct _t0441InBlock {
    char accno[11];            // 계좌번호
    char passwd[8];            // 비밀번호
    char cts_expcode[8];       // CTS_종목번호
    char cts_medocd[1];        // CTS_매매구분
} t0441InBlock;

typedef struct _t0441OutBlock {
    char tdtsunik[18];         // 매매손익합계
    char cts_expcode[8];       // CTS_종목번호
    char cts_medocd[1];        // CTS_매매구분
    char tappamt[18];          // 평가금액
    char tsunik[18];           // 평가손익
} t0441OutBlock;

typedef struct _t0441OutBlock1 {
    char expcode[8];           // 종목번호
    char medosu[4];            // 구분
    char jqty[10];             // 잔고수량
    char cqty[10];             // 청산가능수량
    char pamt[10];             // 평균단가
    char mamt[18];             // 총매입금액
    char medocd[1];            // 매매구분
    char dtsunik[18];          // 매매손익
    char sysprocseq[10];       // 처리순번
    char price[9];             // 현재가
    char appamt[18];           // 평가금액
    char dtsunik1[18];         // 평가손익
    char sunikrt[10];          // 수익율
} t0441OutBlock1;

// 옵션 전광판 (t2301)
typedef struct _t2301InBlock {
    char yyyymm[6];            // 월물
    char gubun[1];             // 미니구분(M:미니G:정규)
} t2301InBlock;

typedef struct _t2301OutBlock {
    char histimpv[4];          // 역사적변동성
    char jandatecnt[4];        // 옵션잔존일
    char cimpv[6];             // 콜옵션대표IV
    char pimpv[6];             // 풋옵션대표IV
    char gmprice[6];           // 근월물현재가
    char gmsign[1];            // 근월물전일대비구분
    char gmchange[6];          // 근월물전일대비
    char gmdiff[6];            // 근월물등락율
    char gmvolume[12];         // 근월물거래량
    char gmshcode[8];          // 근월물선물코드
} t2301OutBlock;

typedef struct _t2301OutBlock1 {
    char actprice[6];          // 행사가
    char optcode[8];           // 콜옵션코드
    char price[6];             // 현재가
    char sign[1];              // 전일대비구분
    char change[6];            // 전일대비
    char diff[6];              // 등락율
    char volume[12];           // 거래량
    char iv[6];                // IV
    char mgjv[12];             // 미결제약정
    char mgjvupdn[12];         // 미결제약정증감
    char offerho1[6];          // 매도호가
    char bidho1[6];            // 매수호가
    char cvolume[12];          // 체결량
    char delt[6];              // 델타
    char gama[6];              // 감마
    char vega[6];              // 베가
    char ceta[6];              // 쎄타
    char rhox[6];              // 로우
    char theoryprice[6];       // 이론가
    char impv[6];              // 내재가치
    char timevl[6];            // 시간가치
    char jvolume[12];          // 잔고수량
    char parpl[12];            // 평가손익
    char jngo[6];              // 청산가능수량
    char offerrem1[12];        // 매도잔량
    char bidrem1[12];          // 매수잔량
    char open[6];              // 시가
    char high[6];              // 고가
    char low[6];               // 저가
    char atmgubun[1];          // ATM구분
    char jisuconv[6];          // 지수환산
    char value[12];            // 거래대금
} t2301OutBlock1;

typedef struct _t2301OutBlock2 {
    char actprice[6];          // 행사가
    char optcode[8];           // 풋옵션코드
    char price[6];             // 현재가
    char sign[1];              // 전일대비구분
    char change[6];            // 전일대비
    char diff[6];              // 등락율
    char volume[12];           // 거래량
    char iv[6];                // IV
    char mgjv[12];             // 미결제약정
    char mgjvupdn[12];         // 미결제약정증감
    char offerho1[6];          // 매도호가
    char bidho1[6];            // 매수호가
    char cvolume[12];          // 체결량
    char delt[6];              // 델타
    char gama[6];              // 감마
    char vega[6];              // 베가
    char ceta[6];              // 쎄타
    char rhox[6];              // 로우
    char theoryprice[6];       // 이론가
    char impv[6];              // 내재가치
    char timevl[6];            // 시간가치
    char jvolume[12];          // 잔고수량
    char parpl[12];            // 평가손익
    char jngo[6];              // 청산가능수량
    char offerrem1[12];        // 매도잔량
    char bidrem1[12];          // 매수잔량
    char open[6];              // 시가
    char high[6];              // 고가
    char low[6];               // 저가
    char atmgubun[1];          // ATM구분
    char jisuconv[6];          // 지수환산
    char value[12];            // 거래대금
} t2301OutBlock2;

// 선물/옵션차트(N분) (t8415)
typedef struct _t8415InBlock {
    char shcode[8];            // 단축코드
    char ncnt[4];              // 단위(n분)
    char qrycnt[4];            // 요청건수
    char nday[1];              // 조회영업일수
    char sdate[8];             // 시작일자
    char stime[6];             // 시작시간
    char edate[8];             // 종료일자
    char etime[6];             // 종료시간
    char cts_date[8];          // 연속일자
    char cts_time[10];         // 연속시간
    char comp_yn[1];           // 압축여부
} t8415InBlock;

typedef struct _t8415OutBlock {
    char shcode[8];            // 단축코드
    char jisiga[6];            // 전일시가
    char jihigh[6];            // 전일고가
    char jilow[6];             // 전일저가
    char jiclose[6];           // 전일종가
    char jivolume[12];         // 전일거래량
    char disiga[6];            // 당일시가
    char dihigh[6];            // 당일고가
    char dilow[6];             // 당일저가
    char diclose[6];           // 당일종가
    char highend[6];           // 상한가
    char lowend[6];            // 하한가
    char cts_date[8];          // 연속일자
    char cts_time[10];         // 연속시간
    char s_time[6];            // 장시작시간
    char e_time[6];            // 장종료시간
    char dshmin[2];            // 동시호가처리시간
    char rec_count[7];         // 레코드카운트
} t8415OutBlock;

typedef struct _t8415OutBlock1 {
    char date[8];              // 날짜
    char time[10];             // 시간
    char open[6];              // 시가
    char high[6];              // 고가
    char low[6];               // 저가
    char close[6];             // 종가
    char jdiff_vol[12];        // 누적거래량
    char value[12];            // 거래대금
    char openyak[12];          // 미결제약정
} t8415OutBlock1;

#pragma pack(pop)

#endif // TRSTRUCTURES_H