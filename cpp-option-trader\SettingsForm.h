#ifndef SettingsFormH
#define SettingsFormH

#include <System.Classes.hpp>
#include <Vcl.Controls.hpp>
#include <Vcl.StdCtrls.hpp>
#include <Vcl.Forms.hpp>
#include <Vcl.ExtCtrls.hpp>
#include <Vcl.ComCtrls.hpp>
#include "TUtils.h"

class TSettingsForm : public TForm
{
__published:
    // Panels
    TPanel* PanelButtons;
    TButton* ButtonOK;
    TButton* ButtonCancel;
    TButton* ButtonApply;

    // Page Control
    TPageControl* PageControl1;
    TTabSheet* TabSheetConnection;
    TTabSheet* TabSheetTrading;
    TTabSheet* TabSheetStrategy;
    TTabSheet* TabSheetRisk;

    // Connection Settings
    TGroupBox* GroupBoxXingAPI;
    TLabel* LabelServerIP;
    TEdit* EditServerIP;
    TLabel* LabelServerPort;
    TEdit* EditServerPort;
    TLabel* LabelUserID;
    TEdit* EditUserID;
    TLabel* LabelPassword;
    TEdit* EditPassword;
    TLabel* LabelCertPassword;
    TEdit* EditCertPassword;
    TCheckBox* CheckBoxAutoConnect;

    // Trading Settings
    TGroupBox* GroupBoxTradingSettings;
    TLabel* LabelDefaultQuantity;
    TEdit* EditDefaultQuantity;
    TLabel* LabelMaxPosition;
    TEdit* EditMaxPosition;
    TLabel* LabelSlippage;
    TEdit* EditSlippage;
    TCheckBox* CheckBoxAutoTrading;
    TCheckBox* CheckBoxOrderConfirm;

    // Strategy Settings
    TGroupBox* GroupBoxStrategy;
    TLabel* LabelStochK;
    TEdit* EditStochK;
    TLabel* LabelStochD;
    TEdit* EditStochD;
    TLabel* LabelStochSlowing;
    TEdit* EditStochSlowing;
    TLabel* LabelCCIPeriod;
    TEdit* EditCCIPeriod;
    TLabel* LabelDMIPeriod;
    TEdit* EditDMIPeriod;
    TLabel* LabelBarInterval;
    TComboBox* ComboBoxBarInterval;

    // Risk Management
    TGroupBox* GroupBoxRisk;
    TLabel* LabelMaxLoss;
    TEdit* EditMaxLoss;
    TLabel* LabelMaxProfit;
    TEdit* EditMaxProfit;
    TLabel* LabelStopLoss;
    TEdit* EditStopLoss;
    TLabel* LabelTakeProfit;
    TEdit* EditTakeProfit;
    TCheckBox* CheckBoxRiskManagement;

    // Event Handlers
    void __fastcall FormCreate(TObject* Sender);
    void __fastcall FormShow(TObject* Sender);
    void __fastcall ButtonOKClick(TObject* Sender);
    void __fastcall ButtonCancelClick(TObject* Sender);
    void __fastcall ButtonApplyClick(TObject* Sender);

private:
    std::unique_ptr<TUtils> FUtils;
    bool FModified;

    void LoadSettings();
    void SaveSettings();
    void ApplySettings();
    void SetModified(bool modified);

public:
    __fastcall TSettingsForm(TComponent* Owner);
    __fastcall ~TSettingsForm();
};

extern PACKAGE TSettingsForm* SettingsForm;

#endif