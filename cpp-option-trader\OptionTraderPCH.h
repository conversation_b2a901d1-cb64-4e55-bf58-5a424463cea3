#ifndef OptionTraderPCHH
#define OptionTraderPCHH

// Disable problematic warnings for C++Builder 11
#pragma option push
#pragma warn -8057  // Parameter never used
#pragma warn -8012  // Comparing signed and unsigned values

// VCL headers first (to avoid STL conflicts)
#include <vcl.h>
#include <System.Classes.hpp>
#include <Vcl.Controls.hpp>
#include <Vcl.StdCtrls.hpp>
#include <Vcl.Forms.hpp>
#include <Vcl.ExtCtrls.hpp>
#include <Vcl.Grids.hpp>
#include <Vcl.ComCtrls.hpp>
#include <Vcl.Menus.hpp>

// Windows headers
#include <windows.h>
#include <comdef.h>

// Standard C++ headers (after VCL to avoid conflicts)
#ifdef __BORLANDC__
  // Use STLport for C++Builder
  #include <memory>
  #include <vector>
  #include <map>
  #include <string>
  #include <queue>
  #include <algorithm>
  #include <functional>
  #include <exception>
#else
  // Standard STL for other compilers
  #include <memory>
  #include <vector>
  #include <map>
  #include <string>
  #include <queue>
  #include <mutex>
  #include <thread>
  #include <chrono>
  #include <algorithm>
  #include <functional>
  #include <exception>
#endif

// Project headers
#include "DataTypes.h"

#pragma option pop

#endif