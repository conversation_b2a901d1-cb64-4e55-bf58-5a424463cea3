object MainForm: TMainForm
  Left = 0
  Top = 0
  Caption = #50741#49496#53944#47808#51060#45716' '#49884#49828#53596' v1.0'
  ClientHeight = 800
  ClientWidth = 1200
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Menu = MainMenu1
  Position = poScreenCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  PixelsPerInch = 96
  TextHeight = 15
  object PanelTop: TPanel
    Left = 0
    Top = 0
    Width = 1200
    Height = 80
    Align = alTop
    TabOrder = 0
    object LabelConnectionStatus: TLabel
      Left = 16
      Top = 16
      Width = 73
      Height = 15
      Caption = #50672#44208#50504#46356
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LabelTradingStatus: TLabel
      Left = 16
      Top = 48
      Width = 60
      Height = 15
      Caption = #44144#47000' '#51473#51648
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ButtonConnect: TButton
      Left = 120
      Top = 12
      Width = 75
      Height = 25
      Caption = #50672#44208
      TabOrder = 0
      OnClick = ButtonConnectClick
    end
    object ButtonDisconnect: TButton
      Left = 201
      Top = 12
      Width = 75
      Height = 25
      Caption = #50672#44208#54644#51228
      Enabled = False
      TabOrder = 1
      OnClick = ButtonDisconnectClick
    end
    object GroupBoxTrading: TGroupBox
      Left = 300
      Top = 8
      Width = 500
      Height = 65
      Caption = #44144#47000' '#51228#50612
      TabOrder = 2
      object LabelOptionCode: TLabel
        Left = 16
        Top = 20
        Width = 60
        Height = 15
        Caption = #50741#49496#53076#46300
      end
      object LabelQuantity: TLabel
        Left = 200
        Top = 20
        Width = 24
        Height = 15
        Caption = #49688#47049
      end
      object EditOptionCode: TEdit
        Left = 16
        Top = 36
        Width = 150
        Height = 23
        TabOrder = 0
        Text = '201XXXX'
      end
      object EditQuantity: TEdit
        Left = 200
        Top = 36
        Width = 80
        Height = 23
        TabOrder = 1
        Text = '1'
      end
      object ButtonStartTrading: TButton
        Left = 300
        Top = 16
        Width = 80
        Height = 25
        Caption = #44144#47000#49884#51089
        Enabled = False
        TabOrder = 2
        OnClick = ButtonStartTradingClick
      end
      object ButtonStopTrading: TButton
        Left = 300
        Top = 42
        Width = 80
        Height = 25
        Caption = #44144#47000#51473#51648
        Enabled = False
        TabOrder = 3
        OnClick = ButtonStopTradingClick
      end
      object CheckBoxAutoTrading: TCheckBox
        Left = 400
        Top = 20
        Width = 80
        Height = 17
        Caption = #51088#46041#44144#47000
        TabOrder = 4
      end
    end
  end
  object PanelBottom: TPanel
    Left = 0
    Top = 770
    Width = 1200
    Height = 30
    Align = alBottom
    TabOrder = 1
    object StatusBar1: TStatusBar
      Left = 1
      Top = 1
      Width = 1198
      Height = 28
      Panels = <
        item
          Width = 50
        end>
    end
  end
  object PanelLeft: TPanel
    Left = 0
    Top = 80
    Width = 400
    Height = 690
    Align = alLeft
    TabOrder = 2
    object GroupBoxMarketData: TGroupBox
      Left = 8
      Top = 8
      Width = 384
      Height = 200
      Caption = #49884#51109#51221#48372
      TabOrder = 0
      object GridMarketData: TStringGrid
        Left = 8
        Top = 20
        Width = 368
        Height = 172
        ColCount = 5
        DefaultRowHeight = 20
        FixedCols = 0
        RowCount = 1
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        TabOrder = 0
      end
    end
    object GroupBoxPositions: TGroupBox
      Left = 8
      Top = 220
      Width = 384
      Height = 200
      Caption = #54252#51648#49496
      TabOrder = 1
      object GridPositions: TStringGrid
        Left = 8
        Top = 20
        Width = 368
        Height = 172
        ColCount = 5
        DefaultRowHeight = 20
        FixedCols = 0
        RowCount = 1
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        TabOrder = 0
      end
    end
    object GroupBoxOrders: TGroupBox
      Left = 8
      Top = 432
      Width = 384
      Height = 200
      Caption = #51452#47928#45236#50669
      TabOrder = 2
      object GridOrders: TStringGrid
        Left = 8
        Top = 20
        Width = 368
        Height = 172
        ColCount = 7
        DefaultRowHeight = 20
        FixedCols = 0
        RowCount = 1
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        TabOrder = 0
      end
    end
  end
  object PanelCenter: TPanel
    Left = 400
    Top = 80
    Width = 800
    Height = 690
    Align = alClient
    TabOrder = 3
    object GroupBoxLog: TGroupBox
      Left = 8
      Top = 8
      Width = 784
      Height = 674
      Caption = #47196#44536
      TabOrder = 0
      object MemoLog: TMemo
        Left = 8
        Top = 20
        Width = 768
        Height = 646
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Consolas'
        Font.Style = []
        ParentFont = False
        ReadOnly = True
        ScrollBars = ssVertical
        TabOrder = 0
      end
    end
  end
  object MainMenu1: TMainMenu
    Left = 856
    Top = 8
    object MenuFile: TMenuItem
      Caption = #54028#51068'(&F)'
      object MenuConnect: TMenuItem
        Caption = #50672#44208'(&C)'
        OnClick = MenuConnectClick
      end
      object MenuDisconnect: TMenuItem
        Caption = #50672#44208#54644#51228'(&D)'
        Enabled = False
        OnClick = MenuDisconnectClick
      end
      object MenuSettings: TMenuItem
        Caption = #49444#51221'(&S)'
        OnClick = MenuSettingsClick
      end
      object MenuExit: TMenuItem
        Caption = #51333#47308'(&X)'
        OnClick = MenuExitClick
      end
    end
    object MenuTrading: TMenuItem
      Caption = #44144#47000'(&T)'
      object MenuStartTrading: TMenuItem
        Caption = #44144#47000' '#49884#51089'(&S)'
        Enabled = False
        OnClick = MenuStartTradingClick
      end
      object MenuStopTrading: TMenuItem
        Caption = #44144#47000' '#51473#51648'(&T)'
        Enabled = False
        OnClick = MenuStopTradingClick
      end
    end
    object MenuHelp: TMenuItem
      Caption = #46020#50880#47568'(&H)'
      object MenuAbout: TMenuItem
        Caption = #51221#48372'(&A)'
        OnClick = MenuAboutClick
      end
    end
  end
  object TimerUpdate: TTimer
    Enabled = False
    OnTimer = TimerUpdateTimer
    Left = 920
    Top = 8
  end
end