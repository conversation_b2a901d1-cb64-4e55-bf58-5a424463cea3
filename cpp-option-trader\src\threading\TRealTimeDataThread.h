#ifndef TREALTIMEDATATHREAD_H
#define TREALTIMEDATATHREAD_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <memory>
#include <queue>
#include <vector>
#include "../data/DataTypes.h"

// 전방 선언
class TXingAPIWrapper;
class TDataLogger;

// 데이터 메시지 타입
enum class TDataMessageType {
    HOGA_DATA,
    JEOBSU_DATA,
    ORDER_STATUS,
    ACCOUNT_UPDATE,
    ERROR_MESSAGE
};

// 데이터 메시지 구조체
struct TDataMessage {
    TDataMessageType type;
    TDateTime timestamp;
    AnsiString code;
    Variant data;           // THogaData, TJeobsuData 등을 담는 범용 데이터
    
    TDataMessage() : type(TDataMessageType::HOGA_DATA), timestamp(Now()) {}
    TDataMessage(TDataMessageType t, const AnsiString& c, const Variant& d) :
        type(t), timestamp(Now()), code(c), data(d) {}
};

/**
 * TRealTimeDataThread
 * 실시간 데이터 수신 및 처리 스레드
 * 메인 UI 스레드와 분리하여 데이터 처리 성능 향상
 */
class TRealTimeDataThread : public TThread {
private:
    // API 및 로거 참조
    TXingAPIWrapper* FXingAPI;
    TDataLogger* FDataLogger;
    
    // 데이터 큐
    std::queue<std::shared_ptr<TDataMessage>> FDataQueue;
    TCriticalSection* FQueueLock;
    
    // 스레드 제어
    bool FIsRunning;
    bool FIsPaused;
    TEvent* FPauseEvent;
    TEvent* FResumeEvent;
    TEvent* FShutdownEvent;
    
    // 처리 통계
    int FProcessedMessageCount;
    int FErrorCount;
    TDateTime FLastProcessTime;
    
    // 설정
    int FProcessInterval;           // 처리 간격 (ms)
    int FMaxQueueSize;             // 최대 큐 크기
    bool FLogProcessing;           // 처리 로그 여부
    
    // 내부 메서드
    void ProcessDataQueue();
    void ProcessHogaMessage(const std::shared_ptr<TDataMessage>& message);
    void ProcessJeobsuMessage(const std::shared_ptr<TDataMessage>& message);
    void ProcessOrderStatusMessage(const std::shared_ptr<TDataMessage>& message);
    void ProcessAccountUpdateMessage(const std::shared_ptr<TDataMessage>& message);
    void ProcessErrorMessage(const std::shared_ptr<TDataMessage>& message);
    
    void SendToMainThread(const std::shared_ptr<TDataMessage>& message);
    void LogMessage(TLogLevel level, const AnsiString& message);
    void UpdateStatistics();
    
protected:
    // TThread 오버라이드
    void __fastcall Execute() override;
    
public:
    // 생성자/소멸자
    __fastcall TRealTimeDataThread(TXingAPIWrapper* xingAPI, TDataLogger* dataLogger);
    __fastcall ~TRealTimeDataThread();
    
    // 스레드 제어
    void Start();
    void Stop();
    void Pause();
    void Resume();
    bool IsRunning() const { return FIsRunning && !Suspended; }
    bool IsPaused() const { return FIsPaused; }
    
    // 데이터 큐 관리
    void AddMessage(const std::shared_ptr<TDataMessage>& message);
    void AddHogaData(const AnsiString& code, const THogaData& hoga);
    void AddJeobsuData(const AnsiString& code, const TJeobsuData& jeobsu);
    void AddOrderStatus(const AnsiString& orderId, const AnsiString& status);
    void AddAccountUpdate();
    void AddErrorMessage(const AnsiString& error);
    
    // 큐 상태
    int GetQueueSize();
    void ClearQueue();
    bool IsQueueFull();
    
    // 설정
    void SetProcessInterval(int milliseconds) { FProcessInterval = milliseconds; }
    void SetMaxQueueSize(int size) { FMaxQueueSize = size; }
    void SetLogging(bool enable) { FLogProcessing = enable; }
    
    // 통계
    int GetProcessedMessageCount() const { return FProcessedMessageCount; }
    int GetErrorCount() const { return FErrorCount; }
    TDateTime GetLastProcessTime() const { return FLastProcessTime; }
    double GetProcessingRate();     // 초당 처리 메시지 수
    
    // 이벤트 선언 (Synchronize를 통해 메인 스레드에서 실행)
    __property TNotifyEvent OnDataProcessed = { read = FOnDataProcessed, write = FOnDataProcessed };
    __property TNotifyEvent OnQueueFull = { read = FOnQueueFull, write = FOnQueueFull };
    __property TNotifyEvent OnThreadError = { read = FOnThreadError, write = FOnThreadError };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnDataProcessed;
    TNotifyEvent FOnQueueFull;
    TNotifyEvent FOnThreadError;
    
    // Synchronize 메서드들 (메인 스레드에서 실행)
    void __fastcall SyncDataProcessed();
    void __fastcall SyncQueueFull();
    void __fastcall SyncThreadError();
    
    // 현재 처리 중인 메시지 (동기화용)
    std::shared_ptr<TDataMessage> FCurrentMessage;
    AnsiString FCurrentError;
};

/**
 * TOrderProcessThread
 * 주문 처리 전용 스레드
 * 주문 요청과 상태 업데이트를 별도 스레드에서 처리
 */
class TOrderProcessThread : public TThread {
private:
    // 컴포넌트 참조
    TXingAPIWrapper* FXingAPI;
    
    // 주문 큐
    std::queue<std::shared_ptr<TOrderInfo>> FOrderQueue;
    TCriticalSection* FOrderLock;
    
    // 스레드 제어
    bool FIsRunning;
    TEvent* FShutdownEvent;
    
    // 설정
    int FProcessInterval;
    
    void ProcessOrderQueue();
    void ProcessOrder(const std::shared_ptr<TOrderInfo>& order);
    
protected:
    void __fastcall Execute() override;
    
public:
    __fastcall TOrderProcessThread(TXingAPIWrapper* xingAPI);
    __fastcall ~TOrderProcessThread();
    
    void Start();
    void Stop();
    void AddOrder(const std::shared_ptr<TOrderInfo>& order);
    int GetQueueSize();
    void ClearQueue();
    
    __property TNotifyEvent OnOrderProcessed = { read = FOnOrderProcessed, write = FOnOrderProcessed };
    
private:
    TNotifyEvent FOnOrderProcessed;
    std::shared_ptr<TOrderInfo> FCurrentOrder;
    
    void __fastcall SyncOrderProcessed();
};

/**
 * TUIUpdateThread
 * UI 업데이트 전용 스레드
 * 백그라운드에서 UI 데이터를 준비하고 메인 스레드에 동기화
 */
class TUIUpdateThread : public TThread {
private:
    // 업데이트 큐
    std::queue<AnsiString> FUpdateQueue;
    TCriticalSection* FUpdateLock;
    
    // 스레드 제어
    bool FIsRunning;
    TEvent* FShutdownEvent;
    
    // 설정
    int FUpdateInterval;
    
    void ProcessUpdates();
    
protected:
    void __fastcall Execute() override;
    
public:
    __fastcall TUIUpdateThread();
    __fastcall ~TUIUpdateThread();
    
    void Start();
    void Stop();
    void RequestUpdate(const AnsiString& updateType);
    
    __property TNotifyEvent OnUIUpdateReady = { read = FOnUIUpdateReady, write = FOnUIUpdateReady };
    
private:
    TNotifyEvent FOnUIUpdateReady;
    AnsiString FCurrentUpdate;
    
    void __fastcall SyncUIUpdate();
};

#endif // TREALTIMEDATATHREAD_H