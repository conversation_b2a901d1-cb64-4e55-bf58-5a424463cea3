#include "TTrader.h"
#include "TOrderManager.h"
#include "../api/TXingAPIWrapper.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>
#include <algorithm>

// 생성자
__fastcall TTrader::TTrader(TXingAPIWrapper* xingAPI, TOrderManager* orderManager) 
    : TObject(), FXingAPI(xingAPI), FOrderManager(orderManager) {
    
    FIsEnabled = false;
    FTotalInvestment = 0.0;
    FAvailableCash = 0.0;
    FTotalAsset = 0.0;
    FTotalProfitLoss = 0.0;
    FTotalTradeCount = 0;
    FWinCount = 0;
    FLossCount = 0;
    
    // 크리티컬 섹션 생성
    FCriticalSection = new TCriticalSection();
    
    // 타이머 생성
    FPositionUpdateTimer = new TTimer(NULL);
    FPositionUpdateTimer->Interval = 5000;  // 5초
    FPositionUpdateTimer->OnTimer = OnPositionUpdateTimer;
    FPositionUpdateTimer->Enabled = false;
    
    FRiskCheckTimer = new TTimer(NULL);
    FRiskCheckTimer->Interval = 1000;   // 1초
    FRiskCheckTimer->OnTimer = OnRiskCheckTimer;
    FRiskCheckTimer->Enabled = false;
    
    WriteLog(TLogLevel::INFO, "트레이더 생성");
}

// 소멸자
__fastcall TTrader::~TTrader() {
    Stop();
    
    delete FPositionUpdateTimer;
    delete FRiskCheckTimer;
    delete FCriticalSection;
}

// 초기화
void TTrader::Initialize(const TStrategyParams& params) {
    TLockGuard lock(FCriticalSection);
    
    FStrategyParams = params;
    FTotalInvestment = params.initial_capital;
    FAvailableCash = params.initial_capital;
    FTotalAsset = params.initial_capital;
    
    WriteLog(TLogLevel::INFO, 
        AnsiString().sprintf("트레이더 초기화: 초기자본 %.0f", FTotalInvestment));
}

// 시작
void TTrader::Start() {
    if (!FIsEnabled) {
        FIsEnabled = true;
        FPositionUpdateTimer->Enabled = true;
        FRiskCheckTimer->Enabled = true;
        
        // 초기 포지션 동기화
        SyncPositionsWithBroker();
        
        WriteLog(TLogLevel::INFO, "트레이더 시작");
    }
}

// 중지
void TTrader::Stop() {
    if (FIsEnabled) {
        FIsEnabled = false;
        FPositionUpdateTimer->Enabled = false;
        FRiskCheckTimer->Enabled = false;
        
        WriteLog(TLogLevel::INFO, "트레이더 중지");
    }
}

// 주문 체결 처리
void TTrader::OnOrderFilled(const TOrderInfo& order) {
    try {
        TLockGuard lock(FCriticalSection);
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문 체결: %s, %s %d주, 가격: %.0f", 
                order.code.c_str(), 
                TUtils::TradeTypeToString(order.trade_type).c_str(),
                order.filled_quantity, order.average_price));
        
        // 포지션 업데이트
        UpdatePosition(order);
        
        // 거래 통계 업데이트
        UpdateTradeStatistics(order);
        
        // 체결 알림 이벤트
        if (FOnOrderFilled) {
            FOnOrderFilled(this, order);
        }
        
        // 포지션 변경 알림
        auto position = GetPosition(order.code);
        if (position && FOnPositionUpdated) {
            FOnPositionUpdated(this, *position);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 체결 처리 중 오류: " + e.Message);
    }
}

// 포지션 업데이트
void TTrader::UpdatePosition(const TOrderInfo& order) {
    if (order.filled_quantity == 0) {
        return;
    }
    
    AnsiString code = order.code;
    auto it = FPositions.find(code);
    
    if (it == FPositions.end()) {
        // 새 포지션 생성
        auto position = std::make_shared<TPositionInfo>();
        position->code = code;
        position->quantity = 0;
        position->average_price = 0.0;
        position->total_cost = 0.0;
        position->current_price = order.average_price;
        position->unrealized_pnl = 0.0;
        position->realized_pnl = 0.0;
        position->open_time = order.filled_time;
        position->last_update = Now();
        
        FPositions[code] = position;
        it = FPositions.find(code);
    }
    
    auto position = it->second;
    
    if (order.trade_type == TTradeType::BUY) {
        // 매수 처리
        if (position->quantity >= 0) {
            // 기존 매수 포지션 확대 또는 신규 매수
            double totalCost = position->total_cost + (order.filled_quantity * order.average_price);
            int totalQuantity = position->quantity + order.filled_quantity;
            
            position->average_price = totalCost / totalQuantity;
            position->quantity = totalQuantity;
            position->total_cost = totalCost;
        }
        else {
            // 매도 포지션 축소
            int remainingQuantity = position->quantity + order.filled_quantity;
            
            if (remainingQuantity >= 0) {
                // 포지션 청산 또는 역전
                double realizedPnl = order.filled_quantity * (position->average_price - order.average_price);
                position->realized_pnl += realizedPnl;
                FTotalProfitLoss += realizedPnl;
                
                if (remainingQuantity == 0) {
                    // 완전 청산
                    position->quantity = 0;
                    position->total_cost = 0.0;
                }
                else {
                    // 역전 포지션
                    position->quantity = remainingQuantity;
                    position->average_price = order.average_price;
                    position->total_cost = remainingQuantity * order.average_price;
                }
            }
            else {
                // 부분 청산
                position->quantity = remainingQuantity;
                double realizedPnl = order.filled_quantity * (position->average_price - order.average_price);
                position->realized_pnl += realizedPnl;
                FTotalProfitLoss += realizedPnl;
            }
        }
    }
    else { // TTradeType::SELL
        // 매도 처리
        if (position->quantity <= 0) {
            // 기존 매도 포지션 확대 또는 신규 매도
            double totalCost = position->total_cost + (order.filled_quantity * order.average_price);
            int totalQuantity = position->quantity - order.filled_quantity;
            
            position->average_price = totalCost / abs(totalQuantity);
            position->quantity = totalQuantity;
            position->total_cost = totalCost;
        }
        else {
            // 매수 포지션 축소
            int remainingQuantity = position->quantity - order.filled_quantity;
            
            if (remainingQuantity <= 0) {
                // 포지션 청산 또는 역전
                double realizedPnl = order.filled_quantity * (order.average_price - position->average_price);
                position->realized_pnl += realizedPnl;
                FTotalProfitLoss += realizedPnl;
                
                if (remainingQuantity == 0) {
                    // 완전 청산
                    position->quantity = 0;
                    position->total_cost = 0.0;
                }
                else {
                    // 역전 포지션
                    position->quantity = remainingQuantity;
                    position->average_price = order.average_price;
                    position->total_cost = abs(remainingQuantity) * order.average_price;
                }
            }
            else {
                // 부분 청산
                position->quantity = remainingQuantity;
                double realizedPnl = order.filled_quantity * (order.average_price - position->average_price);
                position->realized_pnl += realizedPnl;
                FTotalProfitLoss += realizedPnl;
            }
        }
    }
    
    position->current_price = order.average_price;
    position->last_update = Now();
    
    // 손익 재계산
    CalculateUnrealizedPnL(position);
    
    // 빈 포지션 제거
    if (position->quantity == 0 && abs(position->unrealized_pnl) < 0.01) {
        FPositions.erase(code);
    }
}

// 미실현 손익 계산
void TTrader::CalculateUnrealizedPnL(std::shared_ptr<TPositionInfo> position) {
    if (!position || position->quantity == 0) {
        return;
    }
    
    double priceDiff = position->current_price - position->average_price;
    
    if (position->quantity > 0) {
        // 매수 포지션
        position->unrealized_pnl = position->quantity * priceDiff;
    }
    else {
        // 매도 포지션
        position->unrealized_pnl = abs(position->quantity) * (-priceDiff);
    }
}

// 포지션 조회
std::vector<std::shared_ptr<TPositionInfo>> TTrader::GetPositions() {
    TLockGuard lock(FCriticalSection);
    
    std::vector<std::shared_ptr<TPositionInfo>> result;
    for (const auto& pair : FPositions) {
        if (pair.second->quantity != 0) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

// 특정 포지션 조회
std::shared_ptr<TPositionInfo> TTrader::GetPosition(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FPositions.find(code);
    if (it != FPositions.end() && it->second->quantity != 0) {
        return it->second;
    }
    
    return nullptr;
}

// 포지션 청산
bool TTrader::ClosePosition(const AnsiString& code) {
    try {
        auto position = GetPosition(code);
        if (!position) {
            WriteLog(TLogLevel::WARNING, "청산할 포지션이 없음: " + code);
            return false;
        }
        
        TTradeType closeType = (position->quantity > 0) ? TTradeType::SELL : TTradeType::BUY;
        int closeQuantity = abs(position->quantity);
        
        if (FOrderManager) {
            AnsiString orderId = FOrderManager->PlaceOrder(code, closeType, closeQuantity, 0.0, TOrderType::MARKET);
            
            if (!orderId.IsEmpty()) {
                WriteLog(TLogLevel::INFO, 
                    AnsiString().sprintf("포지션 청산 주문: %s, %d주", 
                        code.c_str(), closeQuantity));
                return true;
            }
        }
        
        return false;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "포지션 청산 중 오류: " + e.Message);
        return false;
    }
}

// 모든 포지션 청산
bool TTrader::CloseAllPositions() {
    try {
        auto positions = GetPositions();
        bool success = true;
        
        for (const auto& position : positions) {
            if (!ClosePosition(position->code)) {
                success = false;
            }
        }
        
        if (success) {
            WriteLog(TLogLevel::INFO, "모든 포지션 청산 요청 완료");
        }
        
        return success;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "전체 포지션 청산 중 오류: " + e.Message);
        return false;
    }
}

// 현재가 업데이트
void TTrader::UpdateCurrentPrice(const AnsiString& code, double price) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FPositions.find(code);
    if (it != FPositions.end()) {
        auto position = it->second;
        position->current_price = price;
        position->last_update = Now();
        
        // 미실현 손익 재계산
        CalculateUnrealizedPnL(position);
    }
}

// 리스크 체크
void TTrader::CheckRiskLimits() {
    try {
        TLockGuard lock(FCriticalSection);
        
        // 총 자산 계산
        UpdateTotalAsset();
        
        // 손실 한도 체크
        double totalLoss = FTotalInvestment - FTotalAsset;
        double lossRate = totalLoss / FTotalInvestment;
        
        if (lossRate > FStrategyParams.max_loss_rate) {
            WriteLog(TLogLevel::CRITICAL, 
                AnsiString().sprintf("손실 한도 초과: %.2f%% (한도: %.2f%%)", 
                    lossRate * 100, FStrategyParams.max_loss_rate * 100));
            
            // 긴급 전체 청산
            CloseAllPositions();
            
            // 거래 중지
            Stop();
            
            if (FOnRiskLimitExceeded) {
                FOnRiskLimitExceeded(this, "손실 한도 초과");
            }
        }
        
        // 개별 포지션 리스크 체크
        for (const auto& pair : FPositions) {
            auto position = pair.second;
            if (position->quantity == 0) continue;
            
            double positionValue = abs(position->quantity) * position->current_price;
            double positionLoss = -position->unrealized_pnl;
            double positionLossRate = positionLoss / positionValue;
            
            if (positionLossRate > FStrategyParams.stop_loss) {
                WriteLog(TLogLevel::WARNING, 
                    AnsiString().sprintf("개별 손절: %s, 손실률: %.2f%%", 
                        position->code.c_str(), positionLossRate * 100));
                
                ClosePosition(position->code);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "리스크 체크 중 오류: " + e.Message);
    }
}

// 총 자산 업데이트
void TTrader::UpdateTotalAsset() {
    double totalPositionValue = 0.0;
    double totalUnrealizedPnL = 0.0;
    
    for (const auto& pair : FPositions) {
        auto position = pair.second;
        if (position->quantity != 0) {
            totalPositionValue += abs(position->quantity) * position->current_price;
            totalUnrealizedPnL += position->unrealized_pnl;
        }
    }
    
    FTotalAsset = FAvailableCash + totalPositionValue + totalUnrealizedPnL;
}

// 브로커 포지션 동기화
void TTrader::SyncPositionsWithBroker() {
    try {
        if (FXingAPI && FXingAPI->IsLoggedIn()) {
            // 브로커에서 포지션 정보 조회
            TPositionMap brokerPositions = FXingAPI->GetPositions();
            
            TLockGuard lock(FCriticalSection);
            
            for (const auto& pair : brokerPositions) {
                AnsiString code = pair.first;
                auto brokerPos = pair.second;
                
                auto localPos = std::make_shared<TPositionInfo>();
                localPos->code = code;
                localPos->quantity = brokerPos->quantity;
                localPos->average_price = brokerPos->average_price;
                localPos->current_price = brokerPos->current_price;
                localPos->total_cost = brokerPos->total_cost;
                localPos->unrealized_pnl = brokerPos->unrealized_pnl;
                localPos->last_update = Now();
                
                FPositions[code] = localPos;
            }
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("브로커 포지션 동기화 완료: %d건", brokerPositions.size()));
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "브로커 포지션 동기화 중 오류: " + e.Message);
    }
}

// 거래 통계 업데이트
void TTrader::UpdateTradeStatistics(const TOrderInfo& order) {
    if (order.filled_quantity == 0) {
        return;
    }
    
    FTotalTradeCount++;
    
    // 청산 거래인지 확인하여 승률 계산
    auto position = GetPosition(order.code);
    if (position && position->realized_pnl != 0) {
        if (position->realized_pnl > 0) {
            FWinCount++;
        }
        else {
            FLossCount++;
        }
    }
}

// 통계 조회 메서드들
double TTrader::GetTotalProfitLoss() {
    TLockGuard lock(FCriticalSection);
    
    double totalUnrealizedPnL = 0.0;
    for (const auto& pair : FPositions) {
        totalUnrealizedPnL += pair.second->unrealized_pnl;
    }
    
    return FTotalProfitLoss + totalUnrealizedPnL;
}

double TTrader::GetTotalProfitRate() {
    if (FTotalInvestment == 0) {
        return 0.0;
    }
    
    return GetTotalProfitLoss() / FTotalInvestment;
}

double TTrader::GetTotalAsset() {
    TLockGuard lock(FCriticalSection);
    UpdateTotalAsset();
    return FTotalAsset;
}

int TTrader::GetTotalTradeCount() {
    return FTotalTradeCount;
}

int TTrader::GetWinRate() {
    int totalTrades = FWinCount + FLossCount;
    if (totalTrades == 0) {
        return 0;
    }
    
    return (FWinCount * 100) / totalTrades;
}

// 타이머 이벤트 핸들러들
void __fastcall TTrader::OnPositionUpdateTimer(TObject* Sender) {
    if (!FIsEnabled) return;
    
    try {
        // 주기적으로 브로커와 포지션 동기화
        SyncPositionsWithBroker();
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "포지션 업데이트 타이머 오류: " + e.Message);
    }
}

void __fastcall TTrader::OnRiskCheckTimer(TObject* Sender) {
    if (!FIsEnabled) return;
    
    try {
        CheckRiskLimits();
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "리스크 체크 타이머 오류: " + e.Message);
    }
}

// 로깅
void TTrader::WriteLog(TLogLevel level, const AnsiString& message) {
    try {
        AnsiString logMsg = AnsiString().sprintf("[Trader] %s", message.c_str());
        
        #ifdef _DEBUG
        OutputDebugStringA(logMsg.c_str());
        #endif
    }
    catch (...) {
        // 로깅 실패는 무시
    }
}