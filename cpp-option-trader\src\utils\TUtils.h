#ifndef TUTILS_H
#define TUTILS_H

#include <vcl.h>
#include <System.hpp>
#include <vector>
#include <algorithm>
#include <cmath>
#include "../data/DataTypes.h"

/**
 * TUtils
 * 유틸리티 함수 모음 클래스
 */
class TUtils {
public:
    // 문자열 유틸리티
    static AnsiString FormatNumber(double value, int decimals = 2);
    static AnsiString FormatCurrency(double value);
    static AnsiString FormatPercent(double value, int decimals = 2);
    static AnsiString FormatDateTime(TDateTime dt, const AnsiString& format = "yyyy-mm-dd hh:nn:ss");
    static AnsiString Trim(const AnsiString& str);
    static std::vector<AnsiString> Split(const AnsiString& str, const AnsiString& delimiter);
    
    // 수학 유틸리티
    static double RoundTo(double value, int decimals);
    static double CalculatePercentChange(double oldValue, double newValue);
    static bool IsZero(double value, double epsilon = 1e-9);
    static double SafeDivide(double numerator, double denominator, double defaultValue = 0.0);
    
    // 날짜/시간 유틸리티
    static bool IsMarketOpen(TDateTime dateTime = 0.0);
    static bool IsWeekday(TDateTime dateTime = 0.0);
    static TDateTime GetMarketOpenTime(TDateTime date = 0.0);
    static TDateTime GetMarketCloseTime(TDateTime date = 0.0);
    static int GetTradingDaysBetween(TDateTime startDate, TDateTime endDate);
    
    // 옵션 관련 유틸리티
    static bool IsOptionCode(const AnsiString& code);
    static bool IsCallOption(const AnsiString& code);
    static bool IsPutOption(const AnsiString& code);
    static double ExtractStrikePrice(const AnsiString& code);
    static TDateTime ExtractExpirationDate(const AnsiString& code);
    static AnsiString GetUnderlyingCode(const AnsiString& optionCode);
    static AnsiString FormatOptionCode(const AnsiString& underlying, 
                                      double strikePrice, TDateTime expiry, bool isCall);
    
    // 파일 유틸리티
    static bool FileExists(const AnsiString& fileName);
    static bool DirectoryExists(const AnsiString& dirName);
    static bool CreateDirectoryRecursive(const AnsiString& path);
    static AnsiString GetAppDataPath();
    static AnsiString GetLogPath();
    static AnsiString GetConfigPath();
    
    // 데이터 검증
    static bool ValidatePrice(double price);
    static bool ValidateQuantity(int quantity);
    static bool ValidateCode(const AnsiString& code);
    static bool ValidateAccountNumber(const AnsiString& accountNo);
    
    // 컬러 유틸리티 (UI용)
    static TColor GetProfitColor(double profitRate);
    static TColor GetVolumeColor(int volume);
    static TColor GetTrendColor(double change);
    
    // 통계 함수
    static double CalculateMean(const std::vector<double>& data);
    static double CalculateStdDev(const std::vector<double>& data);
    static double CalculateVariance(const std::vector<double>& data);
    static double CalculateMin(const std::vector<double>& data);
    static double CalculateMax(const std::vector<double>& data);
    static std::vector<double> CalculateMovingAverage(const std::vector<double>& data, int period);
    
    // 데이터 타입 변환
    static AnsiString OrderStateToString(TOrderState state);
    static AnsiString OrderTypeToString(TOrderType type);
    static AnsiString TradeTypeToString(TTradeType type);
    static AnsiString TradingModeToString(TTradingMode mode);
    static AnsiString LogLevelToString(TLogLevel level);
    
    static TOrderState StringToOrderState(const AnsiString& str);
    static TOrderType StringToOrderType(const AnsiString& str);
    static TTradeType StringToTradeType(const AnsiString& str);
    static TTradingMode StringToTradingMode(const AnsiString& str);
    static TLogLevel StringToLogLevel(const AnsiString& str);
    
    // JSON 유틸리티
    static AnsiString OrderInfoToJSON(const TOrderInfo& order);
    static AnsiString PositionInfoToJSON(const TPositionInfo& position);
    static AnsiString AccountInfoToJSON(const TAccountInfo& account);
    static TOrderInfo JSONToOrderInfo(const AnsiString& json);
    static TPositionInfo JSONToPositionInfo(const AnsiString& json);
    static TAccountInfo JSONToAccountInfo(const AnsiString& json);
    
    // 에러 처리
    static void HandleException(const Exception& e, const AnsiString& context = "");
    static AnsiString GetLastErrorMessage();
    static void SetLastErrorMessage(const AnsiString& message);
    
private:
    static AnsiString FLastErrorMessage;
    
    // 내부 헬퍼 함수들
    static bool IsValidKoreanStockCode(const AnsiString& code);
    static bool IsValidOptionCode(const AnsiString& code);
    static TDateTime ParseKoreanDate(const AnsiString& dateStr);
};

// 인라인 구현 (간단한 함수들)
inline double TUtils::RoundTo(double value, int decimals) {
    double factor = pow(10.0, decimals);
    return round(value * factor) / factor;
}

inline double TUtils::CalculatePercentChange(double oldValue, double newValue) {
    return IsZero(oldValue) ? 0.0 : ((newValue - oldValue) / oldValue) * 100.0;
}

inline bool TUtils::IsZero(double value, double epsilon) {
    return abs(value) < epsilon;
}

inline double TUtils::SafeDivide(double numerator, double denominator, double defaultValue) {
    return IsZero(denominator) ? defaultValue : numerator / denominator;
}

inline bool TUtils::ValidatePrice(double price) {
    return price > 0.0 && price < 1000000.0;  // 최대 100만원
}

inline bool TUtils::ValidateQuantity(int quantity) {
    return quantity > 0 && quantity <= 10000;  // 최대 1만주
}

#endif // TUTILS_H