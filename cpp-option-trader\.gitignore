# C++Builder specific
*.cbproj.local
*.identcache
*.tds
*.tmp
*.~*
*.bak
*.dcu
*.dpu
*.rsm
*.map
*.pdb
*.dsk
*.exe
*.dll
*.bpl
*.bpi
*.lib
*.obj
*.tlog
*.ilk
*.exp
*.pch
*.idb
*.res
*.rc
*.aps
Win32/
Win64/
__history/
__recovery/

# Debug and Release folders
Debug/
Release/
x64/

# IDE files
*.groupproj.local
*.dproj.local
*.bdsproj.local

# Data files
dataFolder/
logs/
backup/
*.log
*.txt

# Configuration files (keep template but ignore user-specific)
config_user.ini
*.ini.bak

# Temporary files
temp/
*.temp

# Documentation
*.pdf
*.doc
*.docx

# Archive files
*.zip
*.rar
*.7z