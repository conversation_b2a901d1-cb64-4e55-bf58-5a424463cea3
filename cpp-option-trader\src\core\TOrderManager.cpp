#include "TOrderManager.h"
#include "../api/TXingAPIWrapper.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>

// 생성자
__fastcall TOrderManager::TOrderManager(TXingAPIWrapper* xingAPI) 
    : TObject(), FXingAPI(xingAPI) {
    
    FIsEnabled = false;
    FIsProcessing = false;
    
    // 크리티컬 섹션 생성
    FCriticalSection = new TCriticalSection();
    
    // 타이머 생성
    FOrderProcessTimer = new TTimer(NULL);
    FOrderProcessTimer->Interval = 1000;  // 1초
    FOrderProcessTimer->OnTimer = OnOrderProcessTimer;
    FOrderProcessTimer->Enabled = false;
    
    FTrackingTimer = new TTimer(NULL);
    FTrackingTimer->Interval = 500;   // 0.5초
    FTrackingTimer->OnTimer = OnTrackingTimer;
    FTrackingTimer->Enabled = false;
    
    FProfitTrackingTimer = new TTimer(NULL);
    FProfitTrackingTimer->Interval = 1000;  // 1초
    FProfitTrackingTimer->OnTimer = OnProfitTrackingTimer;
    FProfitTrackingTimer->Enabled = false;
    
    // XingAPI 이벤트 연결
    if (FXingAPI) {
        FXingAPI->OnOrderResult = OnOrderConfirmed;
    }
}

// 소멸자
__fastcall TOrderManager::~TOrderManager() {
    Stop();
    
    delete FOrderProcessTimer;
    delete FTrackingTimer;
    delete FProfitTrackingTimer;
    delete FCriticalSection;
}

// 초기화
void TOrderManager::Initialize(const TStrategyParams& params) {
    TLockGuard lock(FCriticalSection);
    
    FStrategyParams = params;
    WriteLog(TLogLevel::INFO, "주문 관리자 초기화 완료");
}

// 시작
void TOrderManager::Start() {
    if (!FIsEnabled) {
        FIsEnabled = true;
        FOrderProcessTimer->Enabled = true;
        FTrackingTimer->Enabled = true;
        FProfitTrackingTimer->Enabled = true;
        
        WriteLog(TLogLevel::INFO, "주문 관리자 시작");
    }
}

// 중지
void TOrderManager::Stop() {
    if (FIsEnabled) {
        FIsEnabled = false;
        FOrderProcessTimer->Enabled = false;
        FTrackingTimer->Enabled = false;
        FProfitTrackingTimer->Enabled = false;
        
        WriteLog(TLogLevel::INFO, "주문 관리자 중지");
    }
}

// 일시정지
void TOrderManager::Pause() {
    if (FIsEnabled) {
        FOrderProcessTimer->Enabled = false;
        FTrackingTimer->Enabled = false;
        WriteLog(TLogLevel::INFO, "주문 관리자 일시정지");
    }
}

// 재개
void TOrderManager::Resume() {
    if (FIsEnabled) {
        FOrderProcessTimer->Enabled = true;
        FTrackingTimer->Enabled = true;
        WriteLog(TLogLevel::INFO, "주문 관리자 재개");
    }
}

// 주문 실행
AnsiString TOrderManager::PlaceOrder(const AnsiString& code, TTradeType tradeType, 
                                    int quantity, double price, TOrderType orderType) {
    try {
        if (!FIsEnabled) {
            WriteLog(TLogLevel::ERROR, "주문 관리자가 비활성화 상태입니다");
            return "";
        }
        
        if (!FXingAPI || !FXingAPI->IsLoggedIn()) {
            WriteLog(TLogLevel::ERROR, "API 연결이 되지 않았습니다");
            return "";
        }
        
        // 새 주문 생성
        auto order = std::make_shared<TOrderInfo>();
        order->order_id = AnsiString().sprintf("ORD%08d", GetTickCount());
        order->code = code;
        order->trade_type = tradeType;
        order->quantity = quantity;
        order->price = price;
        order->order_type = orderType;
        order->state = TOrderState::WAITING;
        order->modify_count = 0;
        order->created_time = Now();
        order->updated_time = Now();
        
        // 주문 조건 검증
        if (!CanPlaceOrder(*order)) {
            WriteLog(TLogLevel::ERROR, "주문 조건을 만족하지 않습니다: " + code);
            return "";
        }
        
        // 주문 큐에 추가
        {
            TLockGuard lock(FCriticalSection);
            FActiveOrders[order->order_id] = order;
            FOrderQueue.push(order);
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문 등록: %s, %s %d주, 가격: %.0f", 
                code.c_str(), TUtils::TradeTypeToString(tradeType).c_str(), 
                quantity, price));
        
        if (FOnOrderPlaced) {
            FOnOrderPlaced(this);
        }
        
        return order->order_id;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 실행 오류: " + e.Message);
        return "";
    }
}

// 주문 취소
bool TOrderManager::CancelOrder(const AnsiString& orderId) {
    try {
        TLockGuard lock(FCriticalSection);
        
        auto it = FActiveOrders.find(orderId);
        if (it == FActiveOrders.end()) {
            WriteLog(TLogLevel::WARNING, "존재하지 않는 주문 ID: " + orderId);
            return false;
        }
        
        auto order = it->second;
        if (order->state == TOrderState::EXECUTED || order->state == TOrderState::CANCELLED) {
            WriteLog(TLogLevel::WARNING, "이미 처리된 주문입니다: " + orderId);
            return false;
        }
        
        // API를 통한 취소 요청
        bool result = false;
        if (FXingAPI) {
            result = FXingAPI->CancelOrder(orderId);
        }
        
        if (result) {
            order->state = TOrderState::CANCELLED;
            order->updated_time = Now();
            
            // 완료된 주문으로 이동
            FCompletedOrders[orderId] = order;
            FActiveOrders.erase(it);
            
            WriteLog(TLogLevel::INFO, "주문 취소 완료: " + orderId);
            
            if (FOnOrderCancelled) {
                FOnOrderCancelled(this);
            }
            
            return true;
        } else {
            WriteLog(TLogLevel::ERROR, "주문 취소 실패: " + orderId);
            return false;
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 취소 오류: " + e.Message);
        return false;
    }
}

// 주문 정정
bool TOrderManager::ModifyOrder(const AnsiString& orderId, double newPrice) {
    try {
        TLockGuard lock(FCriticalSection);
        
        auto it = FActiveOrders.find(orderId);
        if (it == FActiveOrders.end()) {
            WriteLog(TLogLevel::WARNING, "존재하지 않는 주문 ID: " + orderId);
            return false;
        }
        
        auto order = it->second;
        if (order->state != TOrderState::TRACKING && order->state != TOrderState::PRE_ORDER) {
            WriteLog(TLogLevel::WARNING, "정정할 수 없는 주문 상태: " + orderId);
            return false;
        }
        
        if (order->modify_count >= FStrategyParams.max_modify_count) {
            WriteLog(TLogLevel::WARNING, "최대 정정 횟수 초과: " + orderId);
            return false;
        }
        
        // API를 통한 정정 요청
        bool result = false;
        if (FXingAPI) {
            result = FXingAPI->ModifyOrder(orderId, newPrice);
        }
        
        if (result) {
            order->price = newPrice;
            order->modify_count++;
            order->updated_time = Now();
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("주문 정정 완료: %s, 새가격: %.0f (%d회째)", 
                    orderId.c_str(), newPrice, order->modify_count));
            
            return true;
        } else {
            WriteLog(TLogLevel::ERROR, "주문 정정 실패: " + orderId);
            return false;
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 정정 오류: " + e.Message);
        return false;
    }
}

// 조건부 주문 (봉완성 15초 전)
AnsiString TOrderManager::PlaceConditionalOrder(const AnsiString& code, TTradeType tradeType, 
                                               int quantity, TDateTime executeTime) {
    try {
        auto order = std::make_shared<TOrderInfo>();
        order->order_id = AnsiString().sprintf("COND%08d", GetTickCount());
        order->code = code;
        order->trade_type = tradeType;
        order->quantity = quantity;
        order->price = 0.0;  // 1호가로 설정될 예정
        order->order_type = TOrderType::LIMIT;
        order->state = TOrderState::PRE_ORDER;
        order->modify_count = 0;
        order->created_time = Now();
        order->updated_time = Now();
        
        // 실행 시간 설정 (봉완성 15초 전)
        TDateTime preOrderTime = executeTime - (15.0 / (24 * 60 * 60));  // 15초 전
        
        {
            TLockGuard lock(FCriticalSection);
            FActiveOrders[order->order_id] = order;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("조건부 주문 등록: %s, 실행시간: %s", 
                code.c_str(), TUtils::FormatDateTime(preOrderTime).c_str()));
        
        return order->order_id;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "조건부 주문 등록 오류: " + e.Message);
        return "";
    }
}

// 익절 주문
AnsiString TOrderManager::PlaceProfitOrder(const AnsiString& parentOrderId, 
                                          double profitTarget, double stopLoss) {
    try {
        TLockGuard lock(FCriticalSection);
        
        // 부모 주문 찾기
        auto parentIt = FCompletedOrders.find(parentOrderId);
        if (parentIt == FCompletedOrders.end()) {
            WriteLog(TLogLevel::ERROR, "부모 주문을 찾을 수 없습니다: " + parentOrderId);
            return "";
        }
        
        auto parentOrder = parentIt->second;
        if (parentOrder->state != TOrderState::EXECUTED) {
            WriteLog(TLogLevel::ERROR, "체결되지 않은 주문입니다: " + parentOrderId);
            return "";
        }
        
        // 익절 주문 생성
        auto profitOrder = std::make_shared<TOrderInfo>();
        profitOrder->order_id = AnsiString().sprintf("PROFIT%08d", GetTickCount());
        profitOrder->code = parentOrder->code;
        profitOrder->trade_type = (parentOrder->trade_type == TTradeType::BUY) ? 
                                  TTradeType::SELL : TTradeType::BUY;
        profitOrder->quantity = parentOrder->quantity;
        profitOrder->price = profitTarget;
        profitOrder->order_type = TOrderType::LIMIT;
        profitOrder->state = TOrderState::PROFIT_TRACKING;
        profitOrder->parent_order_id = parentOrderId;
        profitOrder->profit_target = profitTarget;
        profitOrder->stop_loss = stopLoss;
        profitOrder->created_time = Now();
        profitOrder->updated_time = Now();
        
        FActiveOrders[profitOrder->order_id] = profitOrder;
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("익절 주문 등록: %s, 목표가: %.0f", 
                profitOrder->code.c_str(), profitTarget));
        
        return profitOrder->order_id;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "익절 주문 등록 오류: " + e.Message);
        return "";
    }
}

// 1호가 추적 주문
AnsiString TOrderManager::PlaceTrackingOrder(const AnsiString& code, TTradeType tradeType, 
                                            int quantity, int maxModifyCount) {
    auto order = std::make_shared<TOrderInfo>();
    order->order_id = AnsiString().sprintf("TRACK%08d", GetTickCount());
    order->code = code;
    order->trade_type = tradeType;
    order->quantity = quantity;
    order->price = GetBestPrice(code, tradeType);  // 현재 1호가
    order->order_type = TOrderType::LIMIT;
    order->state = TOrderState::TRACKING;
    order->modify_count = 0;
    order->created_time = Now();
    order->updated_time = Now();
    
    {
        TLockGuard lock(FCriticalSection);
        FActiveOrders[order->order_id] = order;
    }
    
    WriteLog(TLogLevel::INFO, 
        AnsiString().sprintf("1호가 추적 주문 등록: %s, 가격: %.0f", 
            code.c_str(), order->price));
    
    return order->order_id;
}

// 주문 처리 타이머
void __fastcall TOrderManager::OnOrderProcessTimer(TObject* Sender) {
    if (!FIsEnabled || FIsProcessing) return;
    
    FIsProcessing = true;
    
    try {
        ProcessOrderQueue();
        ProcessPreOrders();
        CancelTimeoutOrders();
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 처리 타이머 오류: " + e.Message);
    }
    
    FIsProcessing = false;
}

// 추적 타이머
void __fastcall TOrderManager::OnTrackingTimer(TObject* Sender) {
    if (!FIsEnabled) return;
    
    try {
        ProcessTrackingOrders();
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "추적 타이머 오류: " + e.Message);
    }
}

// 익절 추적 타이머
void __fastcall TOrderManager::OnProfitTrackingTimer(TObject* Sender) {
    if (!FIsEnabled) return;
    
    try {
        ProcessProfitTracking();
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "익절 추적 타이머 오류: " + e.Message);
    }
}

// 주문 큐 처리
void __fastcall TOrderManager::ProcessOrderQueue() {
    TLockGuard lock(FCriticalSection);
    
    while (!FOrderQueue.empty()) {
        auto order = FOrderQueue.front();
        FOrderQueue.pop();
        
        if (order && order->state == TOrderState::WAITING) {
            // 1호가 가격 설정
            if (order->price == 0.0 || order->state == TOrderState::TRACKING) {
                order->price = GetBestPrice(order->code, order->trade_type);
            }
            
            // API를 통한 주문 전송
            if (FXingAPI) {
                AnsiString apiOrderId = FXingAPI->SendOrder(
                    order->code, order->trade_type, order->quantity, 
                    order->price, order->order_type);
                
                if (!apiOrderId.IsEmpty()) {
                    order->state = TOrderState::TRACKING;
                    order->updated_time = Now();
                    
                    WriteLog(TLogLevel::INFO, "주문 전송 완료: " + order->order_id);
                }
            }
        }
    }
}

// 사전 주문 처리
void __fastcall TOrderManager::ProcessPreOrders() {
    TLockGuard lock(FCriticalSection);
    TDateTime now = Now();
    
    for (auto& pair : FActiveOrders) {
        auto order = pair.second;
        
        if (order->state == TOrderState::PRE_ORDER) {
            // 봉완성 15초 전인지 확인 (간단화된 로직)
            TDateTime nextMinute = IncMinute(Trunc(now * 24 * 60) / (24.0 * 60), 1);
            TDateTime preOrderTime = nextMinute - (15.0 / (24 * 60 * 60));
            
            if (now >= preOrderTime) {
                // 조건 재확인
                if (ValidateOrderConditions(order->code)) {
                    order->price = GetBestPrice(order->code, order->trade_type);
                    order->state = TOrderState::TRACKING;
                    FOrderQueue.push(order);
                    
                    WriteLog(TLogLevel::INFO, "사전 주문 활성화: " + order->order_id);
                }
            }
        }
    }
}

// 추적 주문 처리
void __fastcall TOrderManager::ProcessTrackingOrders() {
    TLockGuard lock(FCriticalSection);
    
    for (auto& pair : FActiveOrders) {
        auto order = pair.second;
        
        if (order->state == TOrderState::TRACKING) {
            double currentBestPrice = GetBestPrice(order->code, order->trade_type);
            
            // 가격이 변경되었고 정정 횟수가 남아있으면 정정
            if (abs(currentBestPrice - order->price) > 0.01 && 
                order->modify_count < FStrategyParams.max_modify_count) {
                
                UpdateOrderPrice(order);
            }
            
            // 봉완성 시점이면 시장가로 전환 또는 취소
            TDateTime now = Now();
            Word hour, min, sec, msec;
            DecodeTime(now, hour, min, sec, msec);
            
            if (sec >= 58) {  // 봉완성 2초 전
                if (ValidateOrderConditions(order->code)) {
                    ConvertToMarketOrder(order);
                } else {
                    CancelOrder(order->order_id);
                }
            }
        }
    }
}

// 익절 추적 처리
void __fastcall TOrderManager::ProcessProfitTracking() {
    TLockGuard lock(FCriticalSection);
    
    for (auto& pair : FActiveOrders) {
        auto order = pair.second;
        
        if (order->state == TOrderState::PROFIT_TRACKING) {
            UpdateProfitTarget(order);
        }
    }
}

// 주문 가능 여부 확인
bool TOrderManager::CanPlaceOrder(const TOrderInfo& order) {
    // 기본 검증
    if (!TUtils::ValidateCode(order.code) || 
        !TUtils::ValidateQuantity(order.quantity) || 
        !TUtils::ValidatePrice(order.price)) {
        return false;
    }
    
    // 추가 비즈니스 로직 검증
    return ValidateOrderConditions(order.code);
}

// 주문 조건 검증
bool TOrderManager::ValidateOrderConditions(const AnsiString& code) {
    // 시장 개장 시간 확인
    if (!TUtils::IsMarketOpen()) {
        return false;
    }
    
    // 기타 조건 확인 (예: 거래정지, 가격제한 등)
    return true;
}

// 최적 가격 조회 (1호가)
double TOrderManager::GetBestPrice(const AnsiString& code, TTradeType tradeType) {
    // 실제 구현에서는 호가 데이터에서 1호가를 가져와야 함
    // 현재는 임시 구현
    return 100.0;  // 임시값
}

// 주문 가격 업데이트
void TOrderManager::UpdateOrderPrice(std::shared_ptr<TOrderInfo> order) {
    double newPrice = GetBestPrice(order->code, order->trade_type);
    ModifyOrder(order->order_id, newPrice);
}

// 시장가 주문으로 전환
void TOrderManager::ConvertToMarketOrder(std::shared_ptr<TOrderInfo> order) {
    order->order_type = TOrderType::MARKET;
    order->price = 0.0;  // 시장가는 가격 없음
    
    // 기존 주문 취소 후 새로 전송
    CancelOrder(order->order_id);
    FOrderQueue.push(order);
    
    WriteLog(TLogLevel::INFO, "시장가 주문으로 전환: " + order->order_id);
}

// 타임아웃 주문 취소
void TOrderManager::CancelTimeoutOrders() {
    TLockGuard lock(FCriticalSection);
    TDateTime now = Now();
    
    std::vector<AnsiString> toCancel;
    
    for (const auto& pair : FActiveOrders) {
        auto order = pair.second;
        
        // 타임아웃 체크 (설정된 시간 초과)
        double elapsedSeconds = (now - order->created_time) * 24 * 60 * 60;
        if (elapsedSeconds > FStrategyParams.order_timeout_seconds) {
            toCancel.push_back(order->order_id);
        }
    }
    
    // 타임아웃된 주문들 취소
    for (const AnsiString& orderId : toCancel) {
        CancelOrder(orderId);
        WriteLog(TLogLevel::WARNING, "타임아웃으로 주문 취소: " + orderId);
    }
}

// 익절 목표가 업데이트
void TOrderManager::UpdateProfitTarget(std::shared_ptr<TOrderInfo> order) {
    // 실제 구현에서는 현재가와 목표 수익률을 비교하여 익절 조건 확인
    // 현재는 간단화된 로직
}

// 주문 조회 메서드들
std::shared_ptr<TOrderInfo> TOrderManager::GetOrder(const AnsiString& orderId) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FActiveOrders.find(orderId);
    if (it != FActiveOrders.end()) {
        return it->second;
    }
    
    auto completedIt = FCompletedOrders.find(orderId);
    if (completedIt != FCompletedOrders.end()) {
        return completedIt->second;
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<TOrderInfo>> TOrderManager::GetActiveOrders() {
    TLockGuard lock(FCriticalSection);
    
    std::vector<std::shared_ptr<TOrderInfo>> result;
    for (const auto& pair : FActiveOrders) {
        result.push_back(pair.second);
    }
    
    return result;
}

// 통계 메서드들
int TOrderManager::GetTotalOrderCount() {
    TLockGuard lock(FCriticalSection);
    return FActiveOrders.size() + FCompletedOrders.size();
}

int TOrderManager::GetActiveOrderCount() {
    TLockGuard lock(FCriticalSection);
    return FActiveOrders.size();
}

// 긴급 중단
void TOrderManager::EmergencyStop() {
    WriteLog(TLogLevel::CRITICAL, "긴급 중단 실행");
    
    Stop();
    CancelAllOrders();
}

// 모든 주문 취소
void TOrderManager::CancelAllOrders() {
    TLockGuard lock(FCriticalSection);
    
    std::vector<AnsiString> orderIds;
    for (const auto& pair : FActiveOrders) {
        orderIds.push_back(pair.first);
    }
    
    for (const AnsiString& orderId : orderIds) {
        CancelOrder(orderId);
    }
    
    WriteLog(TLogLevel::WARNING, "모든 주문 취소 완료");
}

// 로깅
void TOrderManager::WriteLog(TLogLevel level, const AnsiString& message) {
    #ifdef _DEBUG
    AnsiString logMsg = AnsiString().sprintf("[OrderManager] %s", message.c_str());
    OutputDebugStringA(logMsg.c_str());
    #endif
}

// 이벤트 핸들러들
void __fastcall TOrderManager::OnOrderConfirmed(TObject* Sender, const AnsiString& orderId) {
    // 주문 확인 처리
}

void __fastcall TOrderManager::OnOrderExecuted(TObject* Sender, const AnsiString& orderId, 
                                              int executedQty, double executedPrice) {
    // 체결 처리
}

void __fastcall TOrderManager::OnOrderCancelled(TObject* Sender, const AnsiString& orderId) {
    // 취소 처리
}

void __fastcall TOrderManager::OnOrderRejected(TObject* Sender, const AnsiString& orderId, 
                                              const AnsiString& reason) {
    // 거부 처리
}