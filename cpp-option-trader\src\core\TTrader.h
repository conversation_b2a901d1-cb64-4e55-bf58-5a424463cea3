#ifndef TTRADER_H
#define TTRADER_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <memory>
#include <vector>
#include <map>
#include "../data/DataTypes.h"

// 전방 선언
class TXingAPIWrapper;
class TOrderManager;
class TDataLogger;

/**
 * TTrader
 * 메인 거래 시스템 - XingAPI 연동, 잔고/체결 관리, 포지션 추적
 * Python의 Trader 클래스를 C++Builder로 포팅
 */
class TTrader : public TObject {
private:
    // 컴포넌트 참조
    TXingAPIWrapper* FXingAPI;
    TOrderManager* FOrderManager;
    TDataLogger* FDataLogger;
    
    // 계좌 및 포지션 정보
    std::shared_ptr<TAccountInfo> FAccountInfo;
    TPositionMap FPositions;              // 보유 포지션
    std::map<AnsiString, double> FLastPrices;  // 최근 가격 정보
    
    // 거래 설정
    AnsiString FAccountNumber;
    bool FIsConnected;
    bool FIsLoggedIn;
    bool FIsTrading;
    
    // 타이머
    TTimer* FPositionUpdateTimer;         // 포지션 업데이트 타이머
    TTimer* FAccountUpdateTimer;          // 계좌 정보 업데이트 타이머
    
    // 크리티컬 섹션
    TCriticalSection* FCriticalSection;
    
    // 내부 메서드
    void UpdatePositions();
    void UpdateAccountInfo();
    void CalculatePositionProfitLoss();
    void ProcessExecutionReport(const AnsiString& orderId, int qty, double price);
    
    // 이벤트 핸들러
    void __fastcall OnPositionUpdateTimer(TObject* Sender);
    void __fastcall OnAccountUpdateTimer(TObject* Sender);
    void __fastcall OnAPIConnected(TObject* Sender);
    void __fastcall OnAPIDisconnected(TObject* Sender);
    void __fastcall OnLoginResult(TObject* Sender, bool success);
    void __fastcall OnExecutionReceived(TObject* Sender, const AnsiString& orderId, 
                                       int executedQty, double executedPrice);
    void __fastcall OnPriceUpdate(TObject* Sender, const AnsiString& code, double price);
    
    // 내부 검증 메서드
    bool ValidateTradeRequest(const AnsiString& code, TTradeType tradeType, 
                             int quantity, double price);
    bool CheckMarginRequirement(const AnsiString& code, TTradeType tradeType, 
                               int quantity, double price);
    bool CheckPositionLimit(const AnsiString& code, int additionalQty);
    
public:
    // 생성자/소멸자
    __fastcall TTrader(TXingAPIWrapper* xingAPI);
    __fastcall ~TTrader();
    
    // 초기화 및 연결
    bool Initialize(const AnsiString& accountNumber);
    bool Connect();
    void Disconnect();
    bool Login(const AnsiString& userId, const AnsiString& password, 
              const AnsiString& certPassword = "");
    void Logout();
    
    // 거래 제어
    void StartTrading();
    void StopTrading();
    void PauseTrading();
    void ResumeTrading();
    
    // 주문 실행 (OrderManager를 통해)
    AnsiString PlaceOrder(const AnsiString& code, TTradeType tradeType, 
                         int quantity, double price = 0.0, 
                         TOrderType orderType = TOrderType::LIMIT);
    bool CancelOrder(const AnsiString& orderId);
    bool ModifyOrder(const AnsiString& orderId, double newPrice);
    
    // 포지션 관리
    std::shared_ptr<TPositionInfo> GetPosition(const AnsiString& code);
    std::vector<std::shared_ptr<TPositionInfo>> GetAllPositions();
    bool ClosePosition(const AnsiString& code, int quantity = 0);  // 0 = 전량
    bool CloseAllPositions();
    void UpdatePositionPrice(const AnsiString& code, double currentPrice);
    
    // 계좌 정보
    std::shared_ptr<TAccountInfo> GetAccountInfo() const { return FAccountInfo; }
    void RefreshAccountInfo();
    void RefreshPositions();
    
    // 잔고 조회
    double GetAvailableCash();
    double GetTotalAsset();
    double GetTotalProfitLoss();
    double GetTotalProfitRate();
    int GetPositionCount();
    
    // 주문 가능 여부 검증
    bool CanPlaceOrder(const AnsiString& code, TTradeType tradeType, 
                      int quantity, double price);
    int GetMaxOrderQuantity(const AnsiString& code, TTradeType tradeType, double price);
    
    // 상태 조회
    bool IsConnected() const { return FIsConnected; }
    bool IsLoggedIn() const { return FIsLoggedIn; }
    bool IsTrading() const { return FIsTrading; }
    AnsiString GetAccountNumber() const { return FAccountNumber; }
    
    // 시장 데이터 처리
    void OnHogaReceived(const THogaData& hoga);
    void OnJeobsuReceived(const TJeobsuData& jeobsu);
    
    // 포지션 통계
    int GetWinningPositions();
    int GetLosingPositions();
    double GetAverageProfitRate();
    double GetMaxDrawdown();
    
    // 수동 포지션 관리
    bool AddPosition(const AnsiString& code, int quantity, double avgPrice);
    bool RemovePosition(const AnsiString& code);
    void ClearAllPositions();
    
    // 리스크 관리
    void SetMaxPositionSize(int maxSize);
    void SetMaxTotalExposure(double maxExposure);
    bool CheckRiskLimits(const AnsiString& code, TTradeType tradeType, 
                        int quantity, double price);
    
    // 데이터 저장/로드
    bool SavePositionsToFile(const AnsiString& filename);
    bool LoadPositionsFromFile(const AnsiString& filename);
    
    // 로깅
    void WriteLog(TLogLevel level, const AnsiString& message);
    
    // 컴포넌트 설정
    void SetOrderManager(TOrderManager* orderManager) { FOrderManager = orderManager; }
    void SetDataLogger(TDataLogger* dataLogger) { FDataLogger = dataLogger; }
    
    // 이벤트 선언
    __property TNotifyEvent OnConnected = { read = FOnConnected, write = FOnConnected };
    __property TNotifyEvent OnDisconnected = { read = FOnDisconnected, write = FOnDisconnected };
    __property TNotifyEvent OnLoginSuccess = { read = FOnLoginSuccess, write = FOnLoginSuccess };
    __property TNotifyEvent OnLoginFailed = { read = FOnLoginFailed, write = FOnLoginFailed };
    __property TNotifyEvent OnPositionUpdated = { read = FOnPositionUpdated, write = FOnPositionUpdated };
    __property TNotifyEvent OnAccountUpdated = { read = FOnAccountUpdated, write = FOnAccountUpdated };
    __property TNotifyEvent OnTradeExecuted = { read = FOnTradeExecuted, write = FOnTradeExecuted };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnConnected;
    TNotifyEvent FOnDisconnected;
    TNotifyEvent FOnLoginSuccess;
    TNotifyEvent FOnLoginFailed;
    TNotifyEvent FOnPositionUpdated;
    TNotifyEvent FOnAccountUpdated;
    TNotifyEvent FOnTradeExecuted;
    
    // 리스크 관리 설정
    int FMaxPositionSize;
    double FMaxTotalExposure;
};

#endif // TTRADER_H