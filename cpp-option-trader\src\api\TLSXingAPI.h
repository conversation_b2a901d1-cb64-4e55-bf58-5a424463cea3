#ifndef TLSXINGAPI_H
#define TLSXINGAPI_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <ComObj.hpp>
#include <Variants.hpp>
#include <IniFiles.hpp>
#include <memory>
#include <map>
#include <vector>
#include <queue>
#include "../data/DataTypes.h"
#include "../data/TRStructures.h"
#include "../utils/TUtils.h"

// XingAPI COM 인터페이스 전방 선언
#include "XA_DATASETLib_OCX.h"
#include "XA_SESSIONLib_OCX.h"

// 연결 상태 열거형
enum class TConnectionState {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    LOGIN_IN_PROGRESS = 3,
    LOGGED_IN = 4,
    ERROR_STATE = 99
};

// TR 요청 타입
enum class TTRType {
    ACCOUNT_INFO,       // 계좌 정보
    POSITION_INFO,      // 잔고 정보
    ORDER_SEND,         // 주문 전송
    ORDER_CANCEL,       // 주문 취소
    ORDER_MODIFY,       // 주문 정정
    OPTION_CODE_LIST,   // 옵션 종목 리스트
    CHART_DATA,         // 차트 데이터
    CURRENT_PRICE       // 현재가
};

// 실시간 데이터 타입
enum class TRealDataType {
    OPTION_HOGA,        // 옵션 호가
    OPTION_JEOBSU,      // 옵션 체결
    ORDER_STATUS        // 주문 체결
};

// TR 요청 정보 구조체
struct TTRRequest {
    TTRType type;
    AnsiString trCode;
    AnsiString requestId;
    TDateTime requestTime;
    std::map<AnsiString, AnsiString> inputData;
    bool isCompleted;
    
    TTRRequest() : isCompleted(false), requestTime(Now()) {}
};

// 옵션 데이터 구조체들
struct TOptionCurrentPrice {
    AnsiString code;
    double price;
    double change;
    double changeRate;
    int volume;
    int openInterest;
    double theoreticalPrice;
    double impliedVolatility;
    double delta;
    double gamma;
    double theta;
    double vega;
};

struct TOptionHoga {
    AnsiString code;
    double sellPrices[10];
    int sellQuantities[10];
    double buyPrices[10];
    int buyQuantities[10];
    int totalSellQuantity;
    int totalBuyQuantity;
};

struct TOptionExecution {
    AnsiString code;
    AnsiString time;
    double price;
    int quantity;
    double change;
    int volume;
};

struct TOrderResult {
    AnsiString orderId;
    AnsiString code;
    int quantity;
    double price;
    AnsiString result;
    AnsiString message;
};

// 이벤트 핸들러 타입 정의
typedef void __fastcall (__closure *TConnectionEvent)(TObject* Sender, bool connected);
typedef void __fastcall (__closure *TLoginEvent)(TObject* Sender, bool success, const AnsiString& message);
typedef void __fastcall (__closure *TTRDataEvent)(TObject* Sender, const AnsiString& trCode, const TTRRequest& request);
typedef void __fastcall (__closure *TRealDataEvent)(TObject* Sender, TRealDataType dataType, const AnsiString& code);
typedef void __fastcall (__closure *TErrorEvent)(TObject* Sender, const AnsiString& errorCode, const AnsiString& errorMsg);

// 옵션 데이터 콜백 타입들
typedef void __fastcall (__closure *TOptionCurrentPriceEvent)(const TOptionCurrentPrice& data);
typedef void __fastcall (__closure *TOptionHogaEvent)(const TOptionHoga& data);
typedef void __fastcall (__closure *TOptionExecutionEvent)(const TOptionExecution& data);
typedef void __fastcall (__closure *TOrderResultEvent)(const TOrderResult& data);
typedef void __fastcall (__closure *TQueryReceivedEvent)(const AnsiString& trCode, int blockCount);
typedef void __fastcall (__closure *TRealDataReceivedEvent)(const AnsiString& trCode);

/**
 * TLSXingAPI
 * LS증권 XingAPI 전용 래퍼 클래스
 * quantcoding11/quantcoding 저장소 분석 결과를 반영한 최적화된 구현
 */
class TLSXingAPI : public TObject {
private:
    // COM 컴포넌트들
    TXASession* FXASession;
    std::map<AnsiString, TXAQuery*> FXAQueries;
    std::map<AnsiString, TXAReal*> FXAReals;
    
    // 연결 및 로그인 상태
    TConnectionState FConnectionState;
    AnsiString FServerAddress;
    int FServerPort;
    bool FIsRealServer;
    
    // 계정 정보
    AnsiString FUserId;
    AnsiString FPassword;
    AnsiString FCertPassword;
    AnsiString FAccountNumber;
    std::vector<AnsiString> FAccountList;
    
    // TR 요청 관리
    std::map<AnsiString, std::shared_ptr<TTRRequest>> FPendingRequests;
    std::queue<std::shared_ptr<TTRRequest>> FRequestQueue;
    TTimer* FRequestTimer;
    TCriticalSection* FRequestLock;
    
    // 실시간 데이터 관리
    std::map<AnsiString, TRealDataType> FRealSubscriptions;
    TCriticalSection* FRealDataLock;
    
    // 재연결 관리
    bool FAutoReconnect;
    TTimer* FReconnectTimer;
    int FReconnectAttempts;
    int FMaxReconnectAttempts;
    
    // 설정 관리
    AnsiString FConfigPath;
    
    // 내부 메서드
    bool InitializeComponents();
    void CleanupComponents();
    bool LoadConfiguration();
    void SaveConfiguration();
    
    // COM 이벤트 핸들러
    void __fastcall OnSessionLogin(TObject* Sender, OLECHAR* szCode, OLECHAR* szMsg);
    void __fastcall OnSessionDisconnect(TObject* Sender);
    void __fastcall OnQueryReceiveData(TObject* Sender, OLECHAR* szTrCode);
    void __fastcall OnRealReceiveData(TObject* Sender, OLECHAR* szTrCode);
    
    // TR 처리 메서드
    void __fastcall OnRequestTimer(TObject* Sender);
    void ProcessNextRequest();
    AnsiString GenerateRequestId();
    
    // 재연결 메서드
    void __fastcall OnReconnectTimer(TObject* Sender);
    void StartReconnectTimer();
    void StopReconnectTimer();
    
    // 데이터 처리 메서드
    void HandleOptionCurrentPrice(TXAQuery* query, int blockCount);
    void HandleOptionHoga(TXAQuery* query, int blockCount);
    void HandleOptionExecution(TXAQuery* query, int blockCount);
    void HandleOrderResult(TXAQuery* query, int blockCount);
    void HandleModifyOrderResult(TXAQuery* query, int blockCount);
    void HandleCancelOrderResult(TXAQuery* query, int blockCount);
    
    // reference 코드 기반 새로운 처리 메서드
    void HandleOptionBoard(TXAQuery* query, int blockCount);      // t2301
    void HandleOrderList(TXAQuery* query, int blockCount);        // t0434
    void HandlePositionDetail(TXAQuery* query, int blockCount);   // t0441
    void HandleOptionChart(TXAQuery* query, int blockCount);      // t8415
    
    // 실시간 데이터 처리
    void HandleRealOptionHoga(TXAReal* real);                     // OH0
    void HandleRealOptionExecution(TXAReal* real);                // OC0
    void HandleRealOrderExecution(TXAReal* real);                 // C01
    void HandleRealOrderModifyCancel(TXAReal* real);              // H01
    void HandleRealOrderAccept(TXAReal* real);                    // O01
    
    // 데이터 파싱 메서드 (기존)
    THogaData ParseOptionHogaData(TXAReal* real);
    TJeobsuData ParseOptionJeobsuData(TXAReal* real);
    std::shared_ptr<TAccountInfo> ParseAccountData(TXAQuery* query);
    TPositionMap ParsePositionData(TXAQuery* query);
    
    // 유틸리티 메서드
    void WriteLog(TLogLevel level, const AnsiString& message);
    AnsiString GetErrorMessage(const AnsiString& errorCode);
    
public:
    // 생성자/소멸자
    __fastcall TLSXingAPI();
    __fastcall ~TLSXingAPI();
    
    // 연결 관리
    bool ConnectServer(bool isRealServer = false, 
                      const AnsiString& serverAddr = "api.ls-sec.co.kr", 
                      int port = 20001);
    void DisconnectServer();
    bool IsConnected() const { return FConnectionState >= TConnectionState::CONNECTED; }
    
    // 로그인 관리
    bool Login(const AnsiString& userId, const AnsiString& password, 
              const AnsiString& certPassword = "");
    void Logout();
    bool IsLoggedIn() const { return FConnectionState == TConnectionState::LOGGED_IN; }
    
    // 설정 관리
    void SetConfigPath(const AnsiString& path) { FConfigPath = path; }
    AnsiString GetConfigPath() const { return FConfigPath; }
    void SetAutoReconnect(bool enable) { FAutoReconnect = enable; }
    
    // 계좌 관리
    std::vector<AnsiString> GetAccountList();
    bool SetAccount(const AnsiString& accountNumber);
    AnsiString GetCurrentAccount() const { return FAccountNumber; }
    
    // TR 요청 메서드
    AnsiString RequestAccountInfo();
    AnsiString RequestPositionInfo();
    AnsiString RequestOptionCodeList();
    AnsiString RequestChartData(const AnsiString& code, const AnsiString& period, int count);
    AnsiString RequestCurrentPrice(const AnsiString& code);
    
    // 옵션 전용 TR 요청
    AnsiString RequestOptionBoard(const AnsiString& yyyymm, char gubun = 'G'); // t2301
    AnsiString RequestOrderList(const AnsiString& accno, const AnsiString& passwd, 
                               const AnsiString& expcode = "", char chegb = '0'); // t0434
    AnsiString RequestPositionDetail(const AnsiString& accno, const AnsiString& passwd); // t0441
    AnsiString RequestOptionChart(const AnsiString& shcode, int ncnt = 1, 
                                 int qrycnt = 100, const AnsiString& sdate = ""); // t8415
    
    // 주문 관리
    AnsiString SendOrder(const AnsiString& code, TTradeType tradeType, 
                        int quantity, double price, TOrderType orderType = TOrderType::LIMIT);
    AnsiString CancelOrder(const AnsiString& orderId);
    AnsiString ModifyOrder(const AnsiString& orderId, double newPrice, int newQuantity = 0);
    
    // 정확한 TR 주문 메서드
    AnsiString SendOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                              const AnsiString& fnoIsuNo, char bnsTpCode,
                              const AnsiString& fnoOrdprcPtnCode, double fnoOrdPrc, 
                              long ordQty); // CFOAT00100
    AnsiString ModifyOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                                const AnsiString& fnoIsuNo, const AnsiString& orgOrdNo,
                                const AnsiString& fnoOrdprcPtnCode, double fnoOrdPrc,
                                long mdfyQty); // CFOAT00200
    AnsiString CancelOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                                const AnsiString& fnoIsuNo, const AnsiString& orgOrdNo,
                                long cancQty); // CFOAT00300
    
    // 실시간 데이터 구독
    bool SubscribeOptionHoga(const AnsiString& code);
    bool SubscribeOptionJeobsu(const AnsiString& code);
    bool SubscribeOrderStatus();
    bool UnsubscribeRealData(const AnsiString& code);
    bool UnsubscribeAll();
    
    // 옵션 관련 유틸리티
    std::vector<AnsiString> GetWeeklyOptionCodes();
    std::vector<AnsiString> GetMonthlyOptionCodes();
    bool IsOptionCode(const AnsiString& code);
    bool IsCallOption(const AnsiString& code);
    bool IsPutOption(const AnsiString& code);
    
    // 상태 조회
    TConnectionState GetConnectionState() const { return FConnectionState; }
    AnsiString GetStateDescription() const;
    TDateTime GetLastConnectTime() const;
    
    // 에러 처리
    AnsiString GetLastError() const;
    void ClearError();
    
    // 이벤트 속성
    __property TConnectionEvent OnConnected = { read = FOnConnected, write = FOnConnected };
    __property TConnectionEvent OnDisconnected = { read = FOnDisconnected, write = FOnDisconnected };
    __property TLoginEvent OnLoginResult = { read = FOnLoginResult, write = FOnLoginResult };
    __property TTRDataEvent OnTRDataReceived = { read = FOnTRDataReceived, write = FOnTRDataReceived };
    __property TRealDataEvent OnRealDataReceived = { read = FOnRealDataReceived, write = FOnRealDataReceived };
    __property TErrorEvent OnError = { read = FOnError, write = FOnError };
    
    // 옵션 데이터 이벤트 속성
    __property TOptionCurrentPriceEvent OnOptionCurrentPrice = { read = FOnOptionCurrentPrice, write = FOnOptionCurrentPrice };
    __property TOptionHogaEvent OnOptionHoga = { read = FOnOptionHoga, write = FOnOptionHoga };
    __property TOptionExecutionEvent OnOptionExecution = { read = FOnOptionExecution, write = FOnOptionExecution };
    __property TOrderResultEvent OnOrderResult = { read = FOnOrderResult, write = FOnOrderResult };
    __property TOrderResultEvent OnOrderModifyResult = { read = FOnOrderModifyResult, write = FOnOrderModifyResult };
    __property TOrderResultEvent OnOrderCancelResult = { read = FOnOrderCancelResult, write = FOnOrderCancelResult };
    __property TQueryReceivedEvent OnQueryReceived = { read = FOnQueryReceived, write = FOnQueryReceived };
    __property TRealDataReceivedEvent OnRealDataReceived = { read = FOnRealDataReceived, write = FOnRealDataReceived };
    
private:
    // 이벤트 필드
    TConnectionEvent FOnConnected;
    TConnectionEvent FOnDisconnected;
    TLoginEvent FOnLoginResult;
    TTRDataEvent FOnTRDataReceived;
    TRealDataEvent FOnRealDataReceived;
    TErrorEvent FOnError;
    
    // 옵션 데이터 이벤트 필드
    TOptionCurrentPriceEvent FOnOptionCurrentPrice;
    TOptionHogaEvent FOnOptionHoga;
    TOptionExecutionEvent FOnOptionExecution;
    TOrderResultEvent FOnOrderResult;
    TOrderResultEvent FOnOrderModifyResult;
    TOrderResultEvent FOnOrderCancelResult;
    TQueryReceivedEvent FOnQueryReceived;
    TRealDataReceivedEvent FOnRealDataReceived;
    
    // 내부 상태
    TDateTime FLastConnectTime;
    AnsiString FLastError;
    int FRequestCounter;
};

#endif // TLSXINGAPI_H