#ifndef TVirtualMarketH
#define TVirtualMarketH

#include <System.Classes.hpp>
#include <memory>
#include <vector>
#include <map>
#include <queue>
#include <fstream>
#include <mutex>
#include <thread>
#include "DataTypes.h"
#include "TUtils.h"

struct TMarketDataEvent {
    TDateTime time;
    AnsiString dataType;  // "HOGA" or "JEOBSU"
    AnsiString code;
    THogaData hoga;
    TJeobsuData jeobsu;
    
    bool operator<(const TMarketDataEvent& other) const {
        return time > other.time; // 우선순위 큐에서 최소값 우선
    }
};

class TVirtualMarket : public TObject
{
private:
    std::unique_ptr<TUtils> FUtils;
    AnsiString FDataFolder;
    TDateTime FStartDate;
    TDateTime FEndDate;
    TDateTime FCurrentTime;
    double FPlaybackSpeed;
    bool FIsPlaying;
    bool FIsPaused;
    std::thread FPlaybackThread;
    
    std::priority_queue<TMarketDataEvent> FDataEvents;
    std::map<AnsiString, THogaData> FCurrentHoga;
    std::map<AnsiString, TJeobsuData> FCurrentJeobsu;
    std::map<AnsiString, double> FCurrentPrices;
    std::vector<AnsiString> FTargetCodes;
    
    std::mutex FDataMutex;
    std::mutex FPlaybackMutex;
    
    void LoadMarketData();
    void LoadHogaData(const AnsiString& fileName, const AnsiString& code);
    void LoadJeobsuData(const AnsiString& fileName, const AnsiString& code);
    THogaData ParseHogaLine(const AnsiString& line, const AnsiString& code, TDateTime baseDate);
    TJeobsuData ParseJeobsuLine(const AnsiString& line, const AnsiString& code, TDateTime baseDate);
    void PlaybackThreadProc();
    void ProcessDataEvent(const TMarketDataEvent& event);
    
public:
    __fastcall TVirtualMarket();
    __fastcall ~TVirtualMarket();
    
    // 이벤트 핸들러
    TOnHogaReceived OnHogaReceived;
    TOnJeobsuReceived OnJeobsuReceived;
    TNotifyEvent OnPlaybackFinished;
    
    // 설정 메서드
    void SetDataFolder(const AnsiString& folder);
    void SetDateRange(TDateTime startDate, TDateTime endDate);
    void SetPlaybackSpeed(double speed); // 1.0 = 실시간, 2.0 = 2배속
    void SetTargetCodes(const std::vector<AnsiString>& codes);
    
    // 재생 제어
    void StartPlayback();
    void StopPlayback();
    void PausePlayback();
    void ResumePlayback();
    void SeekTo(TDateTime time);
    
    // 현재 상태 조회
    bool IsPlaying() const { return FIsPlaying; }
    bool IsPaused() const { return FIsPaused; }
    TDateTime GetCurrentTime() const { return FCurrentTime; }
    double GetPlaybackSpeed() const { return FPlaybackSpeed; }
    
    // 시장 데이터 조회
    THogaData GetCurrentHoga(const AnsiString& code);
    TJeobsuData GetCurrentJeobsu(const AnsiString& code);
    double GetCurrentPrice(const AnsiString& code);
    std::vector<AnsiString> GetAvailableDates();
    std::vector<AnsiString> GetAvailableCodes(TDateTime date);
};

#endif