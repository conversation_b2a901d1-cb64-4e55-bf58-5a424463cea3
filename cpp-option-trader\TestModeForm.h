#ifndef TestModeFormH
#define TestModeFormH

#include <System.Classes.hpp>
#include <Vcl.Controls.hpp>
#include <Vcl.StdCtrls.hpp>
#include <Vcl.Forms.hpp>
#include <Vcl.ExtCtrls.hpp>
#include <Vcl.ComCtrls.hpp>
#include <Vcl.Grids.hpp>
#include <memory>
#include "TTestModeManager.h"
#include "TBacktestingFramework.h"

class TTestModeForm : public TForm
{
__published:
    // 모드 선택
    TGroupBox* GroupBoxMode;
    TRadioButton* RadioButtonLive;
    TRadioButton* RadioButtonReplay;
    TRadioButton* RadioButtonBacktest;
    TRadioButton* RadioButtonSimulation;
    
    // 데이터 설정
    TGroupBox* GroupBoxData;
    TLabel* LabelDataFolder;
    TEdit* EditDataFolder;
    TButton* ButtonBrowseFolder;
    TCheckBox* CheckBoxRecordData;
    TCheckBox* CheckBoxVirtualExecution;
    
    // 재생 설정
    TGroupBox* GroupBoxPlayback;
    TLabel* LabelStartDate;
    TDateTimePicker* DatePickerStart;
    TLabel* LabelEndDate;
    TDateTimePicker* DatePickerEnd;
    TLabel* LabelSpeed;
    TEdit* EditSpeed;
    TUpDown* UpDownSpeed;
    
    // 종목 설정
    TGroupBox* GroupBoxCodes;
    TListBox* ListBoxCodes;
    TEdit* EditNewCode;
    TButton* ButtonAddCode;
    TButton* ButtonRemoveCode;
    
    // 제어 버튼
    TPanel* PanelControls;
    TButton* ButtonStart;
    TButton* ButtonStop;
    TButton* ButtonPause;
    TButton* ButtonApplyConfig;
    
    // 상태 표시
    TGroupBox* GroupBoxStatus;
    TLabel* LabelCurrentMode;
    TLabel* LabelStatus;
    TProgressBar* ProgressBarPlayback;
    TLabel* LabelProgress;
    TMemo* MemoLog;
    
    // 백테스트 결과
    TPageControl* PageControlResults;
    TTabSheet* TabSheetSummary;
    TTabSheet* TabSheetTrades;
    TTabSheet* TabSheetChart;
    
    // 요약 탭
    TStringGrid* GridSummary;
    
    // 거래 내역 탭
    TStringGrid* GridTrades;
    
    // 타이머
    TTimer* TimerUpdate;
    
    // 이벤트 핸들러
    void __fastcall FormCreate(TObject* Sender);
    void __fastcall FormDestroy(TObject* Sender);
    void __fastcall RadioButtonModeClick(TObject* Sender);
    void __fastcall ButtonBrowseFolderClick(TObject* Sender);
    void __fastcall ButtonAddCodeClick(TObject* Sender);
    void __fastcall ButtonRemoveCodeClick(TObject* Sender);
    void __fastcall ButtonStartClick(TObject* Sender);
    void __fastcall ButtonStopClick(TObject* Sender);
    void __fastcall ButtonPauseClick(TObject* Sender);
    void __fastcall ButtonApplyConfigClick(TObject* Sender);
    void __fastcall TimerUpdateTimer(TObject* Sender);

private:
    std::unique_ptr<TTestModeManager> FTestManager;
    TTestMode FSelectedMode;
    
    void UpdateModeUI();
    void UpdateStatusUI();
    void UpdateControls();
    void LoadAvailableCodes();
    void DisplayBacktestResult(const TBacktestResult& result);
    void LogMessage(const AnsiString& message);
    
    // 테스트 매니저 이벤트 핸들러
    void OnModeChanged(TObject* Sender);
    void OnTestHogaReceived(TObject* Sender, const THogaData& hoga);
    void OnTestJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu);

public:
    __fastcall TTestModeForm(TComponent* Owner);
    
    TTestModeManager* GetTestManager() const { return FTestManager.get(); }
};

extern PACKAGE TTestModeForm* TestModeForm;

#endif