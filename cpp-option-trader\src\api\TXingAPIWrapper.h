#ifndef TXINGAPIWRAPPER_H
#define TXINGAPIWRAPPER_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <ComObj.hpp>
#include <Variants.hpp>
#include <memory>
#include <map>
#include <vector>
#include "../data/DataTypes.h"

// COM 인터페이스 타입 정의
typedef Variant TXASession;
typedef Variant TXAQuery;
typedef Variant TXAReal;

// 이벤트 핸들러 타입
typedef void __fastcall (__closure *TLoginEvent)(TObject* Sender, bool success, const AnsiString& message);
typedef void __fastcall (__closure *THogaEvent)(TObject* Sender, const THogaData& hoga);
typedef void __fastcall (__closure *TJeobsuEvent)(TObject* Sender, const TJeobsuData& jeobsu);
typedef void __fastcall (__closure *TOrderEvent)(TObject* Sender, const AnsiString& orderId, 
                                                 const AnsiString& status, const AnsiString& message);
typedef void __fastcall (__closure *TErrorEvent)(TObject* Sender, const AnsiString& error);

/**
 * TXingAPIWrapper
 * XingAPI COM 인터페이스 래퍼 클래스
 * Python의 XingAPI 클래스를 C++Builder COM으로 포팅
 */
class TXingAPIWrapper : public TObject {
private:
    // COM 객체들
    TXASession FXASession;
    std::map<AnsiString, TXAQuery> FXAQueries;
    std::map<AnsiString, TXAReal> FXAReals;
    
    // 연결 상태
    bool FIsConnected;
    bool FIsLoggedIn;
    bool FIsReal;              // 실서버 여부
    
    // 계좌 정보
    AnsiString FUserId;
    AnsiString FAccountNumber;
    std::vector<AnsiString> FAccountList;
    
    // 실시간 데이터 구독 목록
    std::map<AnsiString, bool> FSubscribedCodes;
    
    // 이벤트 타이머
    TTimer* FEventProcessTimer;
    
    // 크리티컬 섹션
    TCriticalSection* FCriticalSection;
    
    // COM 이벤트 핸들러
    void __fastcall OnSessionEvent(TObject* Sender);
    void __fastcall OnQueryEvent(TObject* Sender, const AnsiString& queryName);
    void __fastcall OnRealEvent(TObject* Sender, const AnsiString& realName);
    void __fastcall OnEventProcessTimer(TObject* Sender);
    
    // 내부 메서드
    bool InitializeCOMObjects();
    void CleanupCOMObjects();
    TXAQuery CreateQuery(const AnsiString& queryName);
    TXAReal CreateReal(const AnsiString& realName);
    void ProcessSessionEvent();
    void ProcessQueryResult(const AnsiString& queryName);
    void ProcessRealData(const AnsiString& realName);
    
    // 데이터 파싱
    THogaData ParseHogaData(const TXAReal& real);
    TJeobsuData ParseJeobsuData(const TXAReal& real);
    void ParseAccountData(const TXAQuery& query);
    void ParsePositionData(const TXAQuery& query);
    void ParseOrderResult(const TXAQuery& query);
    
    // 에러 처리
    void HandleCOMError(const Exception& e);
    AnsiString GetLastErrorMessage();
    
public:
    // 생성자/소멸자
    __fastcall TXingAPIWrapper();
    __fastcall ~TXingAPIWrapper();
    
    // 연결 및 로그인
    bool ConnectServer(bool isReal = false);
    void DisconnectServer();
    bool Login(const AnsiString& userId, const AnsiString& password, 
              const AnsiString& certPassword = "");
    void Logout();
    
    // 상태 조회
    bool IsConnected() const { return FIsConnected; }
    bool IsLoggedIn() const { return FIsLoggedIn; }
    bool IsRealServer() const { return FIsReal; }
    AnsiString GetUserId() const { return FUserId; }
    
    // 계좌 관리
    std::vector<AnsiString> GetAccountList();
    bool SetAccount(const AnsiString& accountNumber);
    AnsiString GetCurrentAccount() const { return FAccountNumber; }
    
    // 계좌 정보 조회
    bool RequestAccountInfo();
    bool RequestPositionInfo();
    bool RequestOrderHistory(const TDateTime& fromDate, const TDateTime& toDate);
    
    // 주문 관련
    AnsiString SendOrder(const AnsiString& code, TTradeType tradeType, 
                        int quantity, double price, TOrderType orderType = TOrderType::LIMIT);
    bool CancelOrder(const AnsiString& orderId);
    bool ModifyOrder(const AnsiString& orderId, double newPrice);
    bool RequestOrderStatus(const AnsiString& orderId);
    
    // 시세 조회
    bool RequestCurrentPrice(const AnsiString& code);
    bool RequestHoga(const AnsiString& code);
    bool RequestDayChart(const AnsiString& code, const TDateTime& fromDate, const TDateTime& toDate);
    bool RequestMinuteChart(const AnsiString& code, int minutes, int count);
    
    // 실시간 데이터 구독
    bool SubscribeHoga(const AnsiString& code);
    bool SubscribeJeobsu(const AnsiString& code);
    bool SubscribeOrderStatus();
    bool UnsubscribeHoga(const AnsiString& code);
    bool UnsubscribeJeobsu(const AnsiString& code);
    bool UnsubscribeAll();
    
    // 옵션 관련
    std::vector<AnsiString> GetOptionCodeList();
    std::vector<AnsiString> GetWeeklyOptionCodes();
    AnsiString GetUnderlyingCode(const AnsiString& optionCode);
    bool IsCallOption(const AnsiString& code);
    bool IsPutOption(const AnsiString& code);
    double GetStrikePrice(const AnsiString& code);
    TDateTime GetExpirationDate(const AnsiString& code);
    
    // 유틸리티
    bool IsMarketOpen();
    TDateTime GetServerTime();
    AnsiString ConvertCodeFormat(const AnsiString& code);
    bool ValidateCode(const AnsiString& code);
    
    // 설정
    void SetEventProcessInterval(int milliseconds);
    void EnableDebugMode(bool enable);
    
    // 재연결 기능
    bool Reconnect();
    void SetAutoReconnect(bool enable);
    bool IsAutoReconnectEnabled() const { return FAutoReconnect; }
    
    // 로깅
    void WriteLog(TLogLevel level, const AnsiString& message);
    
    // 이벤트 선언
    __property TNotifyEvent OnConnected = { read = FOnConnected, write = FOnConnected };
    __property TNotifyEvent OnDisconnected = { read = FOnDisconnected, write = FOnDisconnected };
    __property TLoginEvent OnLoginResult = { read = FOnLoginResult, write = FOnLoginResult };
    __property THogaEvent OnHogaReceived = { read = FOnHogaReceived, write = FOnHogaReceived };
    __property TJeobsuEvent OnJeobsuReceived = { read = FOnJeobsuReceived, write = FOnJeobsuReceived };
    __property TOrderEvent OnOrderResult = { read = FOnOrderResult, write = FOnOrderResult };
    __property TNotifyEvent OnAccountInfoReceived = { read = FOnAccountInfoReceived, write = FOnAccountInfoReceived };
    __property TNotifyEvent OnPositionInfoReceived = { read = FOnPositionInfoReceived, write = FOnPositionInfoReceived };
    __property TErrorEvent OnError = { read = FOnError, write = FOnError };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnConnected;
    TNotifyEvent FOnDisconnected;
    TLoginEvent FOnLoginResult;
    THogaEvent FOnHogaReceived;
    TJeobsuEvent FOnJeobsuReceived;
    TOrderEvent FOnOrderResult;
    TNotifyEvent FOnAccountInfoReceived;
    TNotifyEvent FOnPositionInfoReceived;
    TErrorEvent FOnError;
    
    // 재연결 관련
    bool FAutoReconnect;
    TTimer* FReconnectTimer;
    int FReconnectAttempts;
    int FMaxReconnectAttempts;
    
    // 디버그 모드
    bool FDebugMode;
    
    // 마지막 수신 데이터
    std::shared_ptr<TAccountInfo> FLastAccountInfo;
    TPositionMap FLastPositions;
    TOrderMap FLastOrders;
    
    void __fastcall OnReconnectTimer(TObject* Sender);
    void StartReconnectTimer();
    void StopReconnectTimer();
};

#endif // TXINGAPIWRAPPER_H