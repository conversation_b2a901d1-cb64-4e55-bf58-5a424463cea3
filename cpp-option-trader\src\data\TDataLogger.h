#ifndef TDATALOGGER_H
#define TDATALOGGER_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <memory>
#include <vector>
#include <fstream>
#include "DataTypes.h"

/**
 * TDataLogger
 * 실시간 데이터 로깅 및 분석용 데이터 저장 클래스
 * Python의 DataLogger 기능을 C++Builder로 포팅
 */
class TDataLogger : public TObject {
private:
    // 로그 파일 경로
    AnsiString FDataPath;
    AnsiString FLogPath;
    
    // 로그 설정
    TLogLevel FMinLogLevel;
    bool FIsEnabled;
    bool FFileLoggingEnabled;
    bool FRealTimeLoggingEnabled;
    
    // 로그 버퍼
    std::vector<std::shared_ptr<TLogMessage>> FLogBuffer;
    TCriticalSection* FLogLock;
    
    // 파일 스트림
    std::unique_ptr<std::ofstream> FLogFile;
    std::unique_ptr<std::ofstream> FHogaFile;
    std::unique_ptr<std::ofstream> FJeobsuFile;
    std::unique_ptr<std::ofstream> FOrderFile;
    
    // 타이머
    TTimer* FFlushTimer;
    
    // 내부 메서드
    void InitializeLogFiles();
    void FlushLogBuffer();
    AnsiString GetLogFileName(const AnsiString& category);
    AnsiString FormatLogMessage(const TLogMessage& log);
    void WriteToFile(std::ofstream& file, const AnsiString& message);
    void RotateLogFiles();
    
public:
    // 생성자/소멸자
    __fastcall TDataLogger(const AnsiString& dataPath = "");
    __fastcall ~TDataLogger();
    
    // 초기화 및 제어
    bool Start();
    void Stop();
    void Pause();
    void Resume();
    
    // 로그 기록
    void WriteLog(TLogLevel level, const AnsiString& category, const AnsiString& message);
    void WriteLog(TLogLevel level, const AnsiString& message);
    
    // 실시간 데이터 로깅
    void LogHoga(const THogaData& hoga);
    void LogJeobsu(const TJeobsuData& jeobsu);
    void LogOrder(const TOrderInfo& order);
    void LogPosition(const TPositionInfo& position);
    void LogBar(const TBarData& bar);
    
    // 설정
    void SetDataPath(const AnsiString& path);
    void SetMinLogLevel(TLogLevel level) { FMinLogLevel = level; }
    void SetFileLogging(bool enabled) { FFileLoggingEnabled = enabled; }
    void SetRealTimeLogging(bool enabled) { FRealTimeLoggingEnabled = enabled; }
    
    // 조회
    AnsiString GetDataPath() const { return FDataPath; }
    TLogLevel GetMinLogLevel() const { return FMinLogLevel; }
    bool IsEnabled() const { return FIsEnabled; }
    
    // 로그 조회
    std::vector<std::shared_ptr<TLogMessage>> GetLogs(TLogLevel minLevel = TLogLevel::INFO);
    std::vector<std::shared_ptr<TLogMessage>> GetRecentLogs(int count = 100);
    void ClearLogs();
    
    // 파일 관리
    void FlushAll();
    void RotateAll();
    AnsiString GetLogFileSize();
    
private:
    // 타이머 이벤트
    void __fastcall OnFlushTimer(TObject* Sender);
};

#endif // TDATALOGGER_H