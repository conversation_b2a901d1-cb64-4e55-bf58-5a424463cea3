#include <vcl.h>
#pragma hdrstop
#include "MainForm.h"
#include "SettingsForm.h"
#include <memory>

#pragma package(smart_init)
#pragma resource "*.dfm"

TMainForm* MainForm;

__fastcall TMainForm::TMainForm(TComponent* Owner)
    : TForm(Owner), FIsConnected(false), FIsTrading(false)
{
}

void __fastcall TMainForm::FormCreate(TObject* Sender)
{
    try {
        // Initialize Trading System
        FTradingSystem = std::make_unique<TIntegratedTradingSystem>();
        
        // Setup event handlers
        FTradingSystem->OnConnectionStatusChanged = OnConnectionStatusChanged;
        FTradingSystem->OnHogaReceived = OnHogaReceived;
        FTradingSystem->OnJeobsuReceived = OnJeobsuReceived;
        FTradingSystem->OnOrderStatusChanged = OnOrderStatusChanged;
        FTradingSystem->OnPositionUpdated = OnPositionUpdated;
        FTradingSystem->OnErrorOccurred = OnErrorOccurred;
        FTradingSystem->OnBarCompleted = OnBarCompleted;

        // Initialize UI
        InitializeGrids();
        UpdateConnectionStatus();
        UpdateTradingStatus();
        
        // Start update timer
        TimerUpdate->Interval = 1000; // 1 second
        TimerUpdate->Enabled = true;

        LogMessage("시스템이 초기화되었습니다.");
    }
    catch (const Exception& e) {
        ShowMessage("초기화 오류: " + e.Message);
    }
}

void __fastcall TMainForm::FormDestroy(TObject* Sender)
{
    try {
        if (FIsTrading) {
            FTradingSystem->StopTrading();
        }
        if (FIsConnected) {
            FTradingSystem->Disconnect();
        }
        FTradingSystem.reset();
    }
    catch (...) {
        // Ignore errors during cleanup
    }
}

void __fastcall TMainForm::FormClose(TObject* Sender, TCloseAction& Action)
{
    if (FIsTrading) {
        if (MessageDlg("거래가 진행 중입니다. 정말 종료하시겠습니까?", 
                      mtConfirmation, TMsgDlgButtons() << mbYes << mbNo, 0) != mrYes) {
            Action = caNone;
            return;
        }
    }
    Action = caFree;
}

void __fastcall TMainForm::ButtonConnectClick(TObject* Sender)
{
    try {
        if (FTradingSystem->Connect()) {
            LogMessage("XingAPI 연결을 시도합니다.");
        } else {
            LogMessage("XingAPI 연결에 실패했습니다.");
        }
    }
    catch (const Exception& e) {
        LogMessage("연결 오류: " + e.Message);
    }
}

void __fastcall TMainForm::ButtonDisconnectClick(TObject* Sender)
{
    try {
        if (FIsTrading) {
            FTradingSystem->StopTrading();
        }
        FTradingSystem->Disconnect();
        LogMessage("XingAPI 연결을 해제했습니다.");
    }
    catch (const Exception& e) {
        LogMessage("연결 해제 오류: " + e.Message);
    }
}

void __fastcall TMainForm::ButtonStartTradingClick(TObject* Sender)
{
    try {
        if (!FIsConnected) {
            ShowMessage("먼저 XingAPI에 연결해주세요.");
            return;
        }

        AnsiString optionCode = EditOptionCode->Text.Trim();
        if (optionCode.IsEmpty()) {
            ShowMessage("옵션 코드를 입력해주세요.");
            EditOptionCode->SetFocus();
            return;
        }

        int quantity = StrToIntDef(EditQuantity->Text, 0);
        if (quantity <= 0) {
            ShowMessage("수량을 올바르게 입력해주세요.");
            EditQuantity->SetFocus();
            return;
        }

        TTradingMode mode = CheckBoxAutoTrading->Checked ? tmAutomatic : tmManual;
        
        FTradingSystem->SetOptionCode(optionCode);
        FTradingSystem->SetQuantity(quantity);
        
        if (FTradingSystem->StartTrading(mode)) {
            LogMessage("거래를 시작했습니다. 코드: " + optionCode + ", 수량: " + IntToStr(quantity));
        } else {
            LogMessage("거래 시작에 실패했습니다.");
        }
    }
    catch (const Exception& e) {
        LogMessage("거래 시작 오류: " + e.Message);
    }
}

void __fastcall TMainForm::ButtonStopTradingClick(TObject* Sender)
{
    try {
        FTradingSystem->StopTrading();
        LogMessage("거래를 중지했습니다.");
    }
    catch (const Exception& e) {
        LogMessage("거래 중지 오류: " + e.Message);
    }
}

void __fastcall TMainForm::MenuConnectClick(TObject* Sender)
{
    ButtonConnectClick(Sender);
}

void __fastcall TMainForm::MenuDisconnectClick(TObject* Sender)
{
    ButtonDisconnectClick(Sender);
}

void __fastcall TMainForm::MenuSettingsClick(TObject* Sender)
{
    try {
        std::unique_ptr<TSettingsForm> settingsForm(new TSettingsForm(this));
        settingsForm->ShowModal();
    }
    catch (const Exception& e) {
        ShowMessage("설정 창 오류: " + e.Message);
    }
}

void __fastcall TMainForm::MenuExitClick(TObject* Sender)
{
    Close();
}

void __fastcall TMainForm::MenuStartTradingClick(TObject* Sender)
{
    ButtonStartTradingClick(Sender);
}

void __fastcall TMainForm::MenuStopTradingClick(TObject* Sender)
{
    ButtonStopTradingClick(Sender);
}

void __fastcall TMainForm::MenuAboutClick(TObject* Sender)
{
    ShowMessage("옵션 트레이더 시스템 v1.0\n"
                "C++Builder 11 기반 자동거래 시스템\n"
                "© 2025");
}

void __fastcall TMainForm::TimerUpdateTimer(TObject* Sender)
{
    try {
        UpdateMarketDataGrid();
        UpdatePositionsGrid();
        UpdateOrdersGrid();
        
        // Update status bar
        AnsiString statusText = "연결: " + AnsiString(FIsConnected ? "연결됨" : "연결안됨") +
                               " | 거래: " + AnsiString(FIsTrading ? "진행중" : "중지") +
                               " | 시간: " + FormatDateTime("hh:nn:ss", Now());
        StatusBar1->Panels->Items[0]->Text = statusText;
    }
    catch (...) {
        // Ignore timer update errors
    }
}

void TMainForm::UpdateConnectionStatus()
{
    if (FIsConnected) {
        LabelConnectionStatus->Caption = "연결됨";
        LabelConnectionStatus->Font->Color = clGreen;
        ButtonConnect->Enabled = false;
        ButtonDisconnect->Enabled = true;
        MenuConnect->Enabled = false;
        MenuDisconnect->Enabled = true;
    } else {
        LabelConnectionStatus->Caption = "연결안됨";
        LabelConnectionStatus->Font->Color = clRed;
        ButtonConnect->Enabled = true;
        ButtonDisconnect->Enabled = false;
        MenuConnect->Enabled = true;
        MenuDisconnect->Enabled = false;
    }
}

void TMainForm::UpdateTradingStatus()
{
    if (FIsTrading) {
        LabelTradingStatus->Caption = "거래 진행중";
        LabelTradingStatus->Font->Color = clBlue;
        ButtonStartTrading->Enabled = false;
        ButtonStopTrading->Enabled = true;
        MenuStartTrading->Enabled = false;
        MenuStopTrading->Enabled = true;
        EditOptionCode->Enabled = false;
        EditQuantity->Enabled = false;
        CheckBoxAutoTrading->Enabled = false;
    } else {
        LabelTradingStatus->Caption = "거래 중지";
        LabelTradingStatus->Font->Color = clGray;
        ButtonStartTrading->Enabled = FIsConnected;
        ButtonStopTrading->Enabled = false;
        MenuStartTrading->Enabled = FIsConnected;
        MenuStopTrading->Enabled = false;
        EditOptionCode->Enabled = true;
        EditQuantity->Enabled = true;
        CheckBoxAutoTrading->Enabled = true;
    }
}

void TMainForm::UpdateMarketDataGrid()
{
    if (!FTradingSystem || !FIsConnected) return;

    try {
        auto marketData = FTradingSystem->GetCurrentMarketData();
        if (!marketData.empty()) {
            GridMarketData->RowCount = marketData.size() + 1;
            int row = 1;
            for (const auto& data : marketData) {
                GridMarketData->Cells[0][row] = data.code;
                GridMarketData->Cells[1][row] = FormatFloat("#,##0", data.currentPrice);
                GridMarketData->Cells[2][row] = FormatFloat("+#,##0;-#,##0", data.change);
                GridMarketData->Cells[3][row] = FormatFloat("#,##0", data.volume);
                GridMarketData->Cells[4][row] = FormatDateTime("hh:nn:ss", data.time);
                row++;
            }
        }
    }
    catch (...) {
        // Ignore grid update errors
    }
}

void TMainForm::UpdatePositionsGrid()
{
    if (!FTradingSystem) return;

    try {
        auto positions = FTradingSystem->GetCurrentPositions();
        if (!positions.empty()) {
            GridPositions->RowCount = positions.size() + 1;
            int row = 1;
            for (const auto& pos : positions) {
                GridPositions->Cells[0][row] = pos.code;
                GridPositions->Cells[1][row] = IntToStr(pos.quantity);
                GridPositions->Cells[2][row] = FormatFloat("#,##0.00", pos.avgPrice);
                GridPositions->Cells[3][row] = FormatFloat("#,##0.00", pos.currentPrice);
                GridPositions->Cells[4][row] = FormatFloat("+#,##0;-#,##0", pos.unrealizedPnL);
                row++;
            }
        }
    }
    catch (...) {
        // Ignore grid update errors
    }
}

void TMainForm::UpdateOrdersGrid()
{
    if (!FTradingSystem) return;

    try {
        auto orders = FTradingSystem->GetRecentOrders();
        if (!orders.empty()) {
            GridOrders->RowCount = orders.size() + 1;
            int row = 1;
            for (const auto& order : orders) {
                GridOrders->Cells[0][row] = order.orderNo;
                GridOrders->Cells[1][row] = order.code;
                GridOrders->Cells[2][row] = order.side == osLong ? "매수" : "매도";
                GridOrders->Cells[3][row] = IntToStr(order.quantity);
                GridOrders->Cells[4][row] = FormatFloat("#,##0", order.price);
                GridOrders->Cells[5][row] = order.status;
                GridOrders->Cells[6][row] = FormatDateTime("hh:nn:ss", order.time);
                row++;
            }
        }
    }
    catch (...) {
        // Ignore grid update errors
    }
}

void TMainForm::InitializeGrids()
{
    // Market Data Grid
    GridMarketData->ColCount = 5;
    GridMarketData->RowCount = 1;
    GridMarketData->Cells[0][0] = "코드";
    GridMarketData->Cells[1][0] = "현재가";
    GridMarketData->Cells[2][0] = "등락";
    GridMarketData->Cells[3][0] = "거래량";
    GridMarketData->Cells[4][0] = "시간";

    // Positions Grid
    GridPositions->ColCount = 5;
    GridPositions->RowCount = 1;
    GridPositions->Cells[0][0] = "코드";
    GridPositions->Cells[1][0] = "수량";
    GridPositions->Cells[2][0] = "평균가";
    GridPositions->Cells[3][0] = "현재가";
    GridPositions->Cells[4][0] = "평가손익";

    // Orders Grid
    GridOrders->ColCount = 7;
    GridOrders->RowCount = 1;
    GridOrders->Cells[0][0] = "주문번호";
    GridOrders->Cells[1][0] = "코드";
    GridOrders->Cells[2][0] = "구분";
    GridOrders->Cells[3][0] = "수량";
    GridOrders->Cells[4][0] = "가격";
    GridOrders->Cells[5][0] = "상태";
    GridOrders->Cells[6][0] = "시간";
}

void TMainForm::LogMessage(const AnsiString& message)
{
    AnsiString timestamp = FormatDateTime("yyyy-mm-dd hh:nn:ss", Now());
    AnsiString logEntry = "[" + timestamp + "] " + message;
    
    MemoLog->Lines->Add(logEntry);
    
    // Keep only last 1000 lines
    if (MemoLog->Lines->Count > 1000) {
        MemoLog->Lines->Delete(0);
    }
    
    // Scroll to bottom
    SendMessage(MemoLog->Handle, WM_VSCROLL, SB_BOTTOM, 0);
}

// Trading System Event Handlers
void TMainForm::OnConnectionStatusChanged(TObject* Sender, bool connected)
{
    FIsConnected = connected;
    TThread::Synchronize(nullptr, [this]() {
        UpdateConnectionStatus();
        UpdateTradingStatus();
        LogMessage(FIsConnected ? "XingAPI에 연결되었습니다." : "XingAPI 연결이 해제되었습니다.");
    });
}

void TMainForm::OnHogaReceived(TObject* Sender, const THogaData& hoga)
{
    // Update real-time market data display
    TThread::Synchronize(nullptr, [this]() {
        // Update market data grid is handled by timer
    });
}

void TMainForm::OnJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu)
{
    TThread::Synchronize(nullptr, [this, jeobsu]() {
        AnsiString message = Format("체결: %s %.0f원 %d계약", 
                                   ARRAYOFCONST((jeobsu.code, jeobsu.price, jeobsu.quantity)));
        LogMessage(message);
    });
}

void TMainForm::OnOrderStatusChanged(TObject* Sender, const TOrderInfo& order)
{
    TThread::Synchronize(nullptr, [this, order]() {
        AnsiString message = Format("주문상태변경: %s %s %s", 
                                   ARRAYOFCONST((order.orderNo, order.code, order.status)));
        LogMessage(message);
    });
}

void TMainForm::OnPositionUpdated(TObject* Sender, const TPositionInfo& position)
{
    TThread::Synchronize(nullptr, [this, position]() {
        AnsiString message = Format("포지션업데이트: %s %d계약 평가손익: %.0f원", 
                                   ARRAYOFCONST((position.code, position.quantity, position.unrealizedPnL)));
        LogMessage(message);
    });
}

void TMainForm::OnErrorOccurred(TObject* Sender, const AnsiString& error)
{
    TThread::Synchronize(nullptr, [this, error]() {
        LogMessage("오류: " + error);
        ShowMessage("오류가 발생했습니다: " + error);
    });
}

void TMainForm::OnBarCompleted(const AnsiString& code, const TBarData& bar)
{
    TThread::Synchronize(nullptr, [this, code, bar]() {
        AnsiString message = Format("봉완성: %s OHLCV(%.0f/%.0f/%.0f/%.0f/%d)", 
                                   ARRAYOFCONST((code, bar.open, bar.high, bar.low, bar.close, bar.volume)));
        LogMessage(message);
    });
}