# 옵션 트레이더 시스템 v1.0

C++Builder 11 기반 고급 옵션 자동거래 시스템

## 주요 특징

- **실시간 거래**: LS증권 XingAPI를 통한 실시간 옵션 거래
- **고급 전략**: Stochastic + CCI + DMI/ADX 기반 다중 지표 전략
- **리스크 관리**: 포지션, 손익, 일일 한도 등 포괄적 리스크 제어
- **백테스팅**: 실제 데이터 기반 정확한 백테스트 및 최적화
- **가상 체결**: 실제와 유사한 체결 시뮬레이션

## 시스템 구성

### 핵심 컴포넌트
- TIntegratedTradingSystem: 메인 컨트롤러
- TXingAPIWrapper/TLSXingAPI: 브로커 연동
- TOptionStrategy: 거래 전략
- TOrderManager: 주문 관리
- TTrader: 포지션/리스크 관리

### 테스트 시스템
- TTestModeManager: 테스트 모드 관리
- TMarketDataRecorder: 실시간 데이터 기록
- TVirtualMarket: 데이터 재생
- TVirtualExecutionEngine: 가상 체결
- TBacktestingFramework: 백테스트

## 빌드 요구사항

- Embarcadero C++Builder 11
- Windows 10/11
- 이베스트투자증권 XingAPI SDK

## 라이센스

개인 사용 목적 © 2025
EOF < /dev/null
