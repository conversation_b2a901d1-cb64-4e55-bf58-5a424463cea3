#include "TBarGenerator.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>
#include <algorithm>

// 생성자
__fastcall TBarGenerator::TBarGenerator(int barInterval) : TObject() {
    FBarInterval = barInterval;
    FCriticalSection = new TCriticalSection();
    
    WriteLog(TLogLevel::INFO, 
        AnsiString().sprintf("분봉 생성기 초기화: %d분 봉", FBarInterval));
}

// 소멸자
__fastcall TBarGenerator::~TBarGenerator() {
    ClearData();
    delete FCriticalSection;
}

// 틱 데이터 추가 (TJeobsuData)
void TBarGenerator::AddTick(const TJeobsuData& tick) {
    try {
        TLockGuard lock(FCriticalSection);
        
        AnsiString code = tick.code;
        if (code.IsEmpty()) {
            return;
        }
        
        // 현재 봉 조회 또는 생성
        auto currentBar = FCurrentBars[code];
        
        if (!currentBar || ShouldCreateNewBar(currentBar, tick)) {
            // 기존 봉이 있으면 완성 처리
            if (currentBar) {
                // 봉 데이터 저장
                if (FBarDataMap.find(code) == FBarDataMap.end()) {
                    FBarDataMap[code] = std::vector<std::shared_ptr<TBarData>>();
                }
                FBarDataMap[code].push_back(currentBar);
                
                // 봉 완성 이벤트
                if (FOnBarCompleted) {
                    FOnBarCompleted(code, *currentBar);
                }
                
                WriteLog(TLogLevel::DEBUG, 
                    AnsiString().sprintf("봉 완성: %s, 시간: %s, OHLCV: %.0f/%.0f/%.0f/%.0f/%d", 
                        code.c_str(), 
                        FormatDateTime("hh:nn:ss", currentBar->time).c_str(),
                        currentBar->open, currentBar->high, currentBar->low, 
                        currentBar->close, currentBar->volume));
            }
            
            // 새 봉 생성
            currentBar = CreateNewBar(code, tick);
            FCurrentBars[code] = currentBar;
        }
        else {
            // 기존 봉 업데이트
            UpdateCurrentBar(currentBar, tick);
        }
        
        // 봉 업데이트 이벤트
        if (FOnBarUpdated && currentBar) {
            FOnBarUpdated(code, *currentBar);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "틱 데이터 추가 중 오류: " + e.Message);
    }
}

// 틱 데이터 추가 (개별 파라미터)
void TBarGenerator::AddTick(const AnsiString& code, TDateTime time, double price, int volume) {
    TJeobsuData tick;
    tick.code = code;
    tick.timestamp = time;
    tick.price = price;
    tick.quantity = volume;
    tick.volume = volume;
    
    AddTick(tick);
}

// 새 봉 생성
std::shared_ptr<TBarData> TBarGenerator::CreateNewBar(const AnsiString& code, const TJeobsuData& tick) {
    auto bar = std::make_shared<TBarData>();
    
    bar->code = code;
    bar->time = GetBarTime(tick.timestamp);
    bar->open = tick.price;
    bar->high = tick.price;
    bar->low = tick.price;
    bar->close = tick.price;
    bar->volume = tick.volume;
    bar->tick_count = 1;
    bar->value = tick.price * tick.volume;
    
    return bar;
}

// 현재 봉 업데이트
void TBarGenerator::UpdateCurrentBar(std::shared_ptr<TBarData> bar, const TJeobsuData& tick) {
    if (!bar) return;
    
    // OHLCV 업데이트
    bar->high = std::max(bar->high, tick.price);
    bar->low = std::min(bar->low, tick.price);
    bar->close = tick.price;
    bar->volume += tick.volume;
    bar->tick_count++;
    bar->value += (tick.price * tick.volume);
    
    // 최종 업데이트 시간
    bar->last_update = tick.timestamp;
}

// 새 봉 생성 여부 판단
bool TBarGenerator::ShouldCreateNewBar(std::shared_ptr<TBarData> currentBar, const TJeobsuData& tick) {
    if (!currentBar) {
        return true;
    }
    
    TDateTime barTime = GetBarTime(tick.timestamp);
    return (barTime != currentBar->time);
}

// 봉 시간 계산
TDateTime TBarGenerator::GetBarTime(TDateTime tickTime) {
    return TBarUtils::AlignToMinute(tickTime, FBarInterval);
}

// 분봉 데이터 조회
std::vector<std::shared_ptr<TBarData>> TBarGenerator::GetBars(const AnsiString& code, int count) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FBarDataMap.find(code);
    if (it == FBarDataMap.end()) {
        return std::vector<std::shared_ptr<TBarData>>();
    }
    
    auto& bars = it->second;
    
    if (count <= 0 || count >= bars.size()) {
        return bars;
    }
    
    // 최근 count개 반환
    std::vector<std::shared_ptr<TBarData>> result;
    int start = std::max(0, (int)bars.size() - count);
    
    for (int i = start; i < bars.size(); i++) {
        result.push_back(bars[i]);
    }
    
    return result;
}

// 현재 봉 조회
std::shared_ptr<TBarData> TBarGenerator::GetCurrentBar(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FCurrentBars.find(code);
    if (it != FCurrentBars.end()) {
        return it->second;
    }
    
    return nullptr;
}

// 마지막 완성된 봉 조회
std::shared_ptr<TBarData> TBarGenerator::GetLastCompletedBar(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FBarDataMap.find(code);
    if (it != FBarDataMap.end() && !it->second.empty()) {
        return it->second.back();
    }
    
    return nullptr;
}

// 데이터 초기화
void TBarGenerator::ClearData(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    if (code.IsEmpty()) {
        // 전체 초기화
        FBarDataMap.clear();
        FCurrentBars.clear();
        WriteLog(TLogLevel::INFO, "모든 분봉 데이터 초기화");
    }
    else {
        // 특정 종목만 초기화
        FBarDataMap.erase(code);
        FCurrentBars.erase(code);
        WriteLog(TLogLevel::INFO, "분봉 데이터 초기화: " + code);
    }
}

// 봉 개수 조회
int TBarGenerator::GetBarCount(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    auto it = FBarDataMap.find(code);
    if (it != FBarDataMap.end()) {
        return it->second.size();
    }
    
    return 0;
}

// 종목 리스트 조회
std::vector<AnsiString> TBarGenerator::GetCodes() {
    TLockGuard lock(FCriticalSection);
    
    std::vector<AnsiString> codes;
    for (const auto& pair : FCurrentBars) {
        codes.push_back(pair.first);
    }
    
    return codes;
}

// 다음 봉 시간 예측
TDateTime TBarGenerator::GetNextBarTime(const AnsiString& code) {
    auto currentBar = GetCurrentBar(code);
    if (currentBar) {
        return TBarUtils::GetNextAlignedTime(currentBar->time, FBarInterval);
    }
    
    return TBarUtils::GetNextAlignedTime(Now(), FBarInterval);
}

// 다음 봉까지 남은 시간 (초)
int TBarGenerator::GetSecondsToNextBar(const AnsiString& code) {
    TDateTime nextTime = GetNextBarTime(code);
    TDateTime now = Now();
    
    double diff = nextTime - now;
    return (int)(diff * 24 * 60 * 60);
}

// 봉 완성 임박 여부
bool TBarGenerator::IsBarNearCompletion(const AnsiString& code, int secondsBefore) {
    int remainingSeconds = GetSecondsToNextBar(code);
    return (remainingSeconds <= secondsBefore && remainingSeconds > 0);
}

// 수동 봉 완성
void TBarGenerator::ForceCompleteBar(const AnsiString& code) {
    TLockGuard lock(FCriticalSection);
    
    auto currentBar = FCurrentBars[code];
    if (currentBar) {
        // 봉 데이터 저장
        if (FBarDataMap.find(code) == FBarDataMap.end()) {
            FBarDataMap[code] = std::vector<std::shared_ptr<TBarData>>();
        }
        FBarDataMap[code].push_back(currentBar);
        
        // 현재 봉 제거
        FCurrentBars.erase(code);
        
        // 봉 완성 이벤트
        if (FOnBarCompleted) {
            FOnBarCompleted(code, *currentBar);
        }
        
        WriteLog(TLogLevel::INFO, "수동 봉 완성: " + code);
    }
}

// 모든 봉 강제 완성
void TBarGenerator::ForceCompleteAllBars() {
    std::vector<AnsiString> codes = GetCodes();
    for (const auto& code : codes) {
        ForceCompleteBar(code);
    }
}

// 로깅
void TBarGenerator::WriteLog(TLogLevel level, const AnsiString& message) {
    try {
        AnsiString logMsg = AnsiString().sprintf("[BarGen] %s", message.c_str());
        
        #ifdef _DEBUG
        OutputDebugStringA(logMsg.c_str());
        #endif
    }
    catch (...) {
        // 로깅 실패는 무시
    }
}

// === TBarUtils 구현 ===

// 분 단위로 시간 정렬
TDateTime TBarUtils::AlignToMinute(TDateTime time, int minutes) {
    // 시간을 분 단위로 내림
    double totalMinutes = time * 24 * 60;
    int alignedMinutes = ((int)totalMinutes / minutes) * minutes;
    
    return alignedMinutes / (24.0 * 60.0);
}

// 다음 정렬된 시간
TDateTime TBarUtils::GetNextAlignedTime(TDateTime time, int minutes) {
    TDateTime aligned = AlignToMinute(time, minutes);
    return aligned + (minutes / (24.0 * 60.0));
}

// 같은 봉 시간인지 확인
bool TBarUtils::IsSameBar(TDateTime time1, TDateTime time2, int minutes) {
    return AlignToMinute(time1, minutes) == AlignToMinute(time2, minutes);
}

// 대표가격 계산 (HLC/3)
double TBarUtils::CalculateTypicalPrice(double high, double low, double close) {
    return (high + low + close) / 3.0;
}

// 가중평균가격 계산
double TBarUtils::CalculateWeightedPrice(double price, int volume, double totalValue, int totalVolume) {
    if (totalVolume == 0) {
        return price;
    }
    
    return (totalValue + price * volume) / (totalVolume + volume);
}

// 봉 데이터 검증
bool TBarUtils::ValidateBar(const TBarData& bar) {
    // 기본 검증
    if (bar.high < bar.low) return false;
    if (bar.open < bar.low || bar.open > bar.high) return false;
    if (bar.close < bar.low || bar.close > bar.high) return false;
    if (bar.volume < 0) return false;
    
    return true;
}

// 봉 데이터 수정
void TBarUtils::FixBarData(TBarData& bar) {
    // 고가/저가 순서 수정
    if (bar.high < bar.low) {
        std::swap(bar.high, bar.low);
    }
    
    // 시가/종가 범위 수정
    bar.open = std::max(bar.low, std::min(bar.high, bar.open));
    bar.close = std::max(bar.low, std::min(bar.high, bar.close));
    
    // 음수 거래량 수정
    if (bar.volume < 0) {
        bar.volume = 0;
    }
}

// 시간봉 변환
std::vector<std::shared_ptr<TBarData>> TBarUtils::ConvertTimeFrame(
    const std::vector<std::shared_ptr<TBarData>>& sourceBars, 
    int targetMinutes) {
    
    std::vector<std::shared_ptr<TBarData>> result;
    
    if (sourceBars.empty()) {
        return result;
    }
    
    std::shared_ptr<TBarData> currentBar = nullptr;
    
    for (const auto& sourceBar : sourceBars) {
        TDateTime targetTime = AlignToMinute(sourceBar->time, targetMinutes);
        
        if (!currentBar || currentBar->time != targetTime) {
            // 새 봉 시작
            if (currentBar) {
                result.push_back(currentBar);
            }
            
            currentBar = std::make_shared<TBarData>();
            currentBar->code = sourceBar->code;
            currentBar->time = targetTime;
            currentBar->open = sourceBar->open;
            currentBar->high = sourceBar->high;
            currentBar->low = sourceBar->low;
            currentBar->close = sourceBar->close;
            currentBar->volume = sourceBar->volume;
            currentBar->tick_count = sourceBar->tick_count;
            currentBar->value = sourceBar->value;
        }
        else {
            // 기존 봉에 합치기
            currentBar->high = std::max(currentBar->high, sourceBar->high);
            currentBar->low = std::min(currentBar->low, sourceBar->low);
            currentBar->close = sourceBar->close;
            currentBar->volume += sourceBar->volume;
            currentBar->tick_count += sourceBar->tick_count;
            currentBar->value += sourceBar->value;
        }
    }
    
    // 마지막 봉 추가
    if (currentBar) {
        result.push_back(currentBar);
    }
    
    return result;
}