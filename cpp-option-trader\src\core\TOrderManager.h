#ifndef TORDERMANAGER_H
#define TORDERMANAGER_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <memory>
#include <vector>
#include <queue>
#include <map>
#include "../data/DataTypes.h"

// 전방 선언
class TXingAPIWrapper;

/**
 * TOrderManager
 * 주문 관리 시스템 - 조건부 주문, 1호가 추적, 익절/손절 관리
 * Python의 OrderManager 클래스를 C++Builder로 포팅
 */
class TOrderManager : public TObject {
private:
    // API 래퍼 참조
    TXingAPIWrapper* FXingAPI;
    
    // 주문 관리
    TOrderMap FActiveOrders;          // 활성 주문 맵
    TOrderMap FCompletedOrders;       // 완료된 주문 맵
    std::queue<std::shared_ptr<TOrderInfo>> FOrderQueue;  // 주문 큐
    
    // 타이머
    TTimer* FOrderProcessTimer;       // 주문 처리 타이머
    TTimer* FTrackingTimer;          // 1호가 추적 타이머
    TTimer* FProfitTrackingTimer;    // 익절 추적 타이머
    
    // 설정
    TStrategyParams FStrategyParams;
    bool FIsEnabled;
    bool FIsProcessing;
    
    // 크리티컬 섹션 (스레드 안전성)
    TCriticalSection* FCriticalSection;
    
    // 내부 메서드
    void __fastcall ProcessOrderQueue();
    void __fastcall ProcessPreOrders();
    void __fastcall ProcessTrackingOrders();
    void __fastcall ProcessProfitTracking();
    
    bool CanPlaceOrder(const TOrderInfo& order);
    bool ValidateOrderConditions(const AnsiString& code);
    double GetBestPrice(const AnsiString& code, TTradeType tradeType);
    void UpdateOrderPrice(std::shared_ptr<TOrderInfo> order);
    void ConvertToMarketOrder(std::shared_ptr<TOrderInfo> order);
    void CancelTimeoutOrders();
    
    // 익절/손절 관리
    void CreateProfitOrder(const TOrderInfo& parentOrder);
    void UpdateProfitTarget(std::shared_ptr<TOrderInfo> order);
    
    // 이벤트 핸들러
    void __fastcall OnOrderProcessTimer(TObject* Sender);
    void __fastcall OnTrackingTimer(TObject* Sender);
    void __fastcall OnProfitTrackingTimer(TObject* Sender);
    void __fastcall OnOrderConfirmed(TObject* Sender, const AnsiString& orderId);
    void __fastcall OnOrderExecuted(TObject* Sender, const AnsiString& orderId, 
                                   int executedQty, double executedPrice);
    void __fastcall OnOrderCancelled(TObject* Sender, const AnsiString& orderId);
    void __fastcall OnOrderRejected(TObject* Sender, const AnsiString& orderId, 
                                   const AnsiString& reason);
    
public:
    // 생성자/소멸자
    __fastcall TOrderManager(TXingAPIWrapper* xingAPI);
    __fastcall ~TOrderManager();
    
    // 초기화
    void Initialize(const TStrategyParams& params);
    void Start();
    void Stop();
    void Pause();
    void Resume();
    
    // 주문 관리
    AnsiString PlaceOrder(const AnsiString& code, TTradeType tradeType, 
                         int quantity, double price = 0.0, 
                         TOrderType orderType = TOrderType::LIMIT);
    bool CancelOrder(const AnsiString& orderId);
    bool ModifyOrder(const AnsiString& orderId, double newPrice);
    
    // 조건부 주문 (봉완성 15초 전 주문)
    AnsiString PlaceConditionalOrder(const AnsiString& code, TTradeType tradeType, 
                                    int quantity, TDateTime executeTime);
    
    // 익절/손절 주문
    AnsiString PlaceProfitOrder(const AnsiString& parentOrderId, 
                               double profitTarget, double stopLoss = 0.0);
    
    // 1호가 추적 주문
    AnsiString PlaceTrackingOrder(const AnsiString& code, TTradeType tradeType, 
                                 int quantity, int maxModifyCount = 3);
    
    // 주문 조회
    std::shared_ptr<TOrderInfo> GetOrder(const AnsiString& orderId);
    std::vector<std::shared_ptr<TOrderInfo>> GetActiveOrders();
    std::vector<std::shared_ptr<TOrderInfo>> GetCompletedOrders();
    std::vector<std::shared_ptr<TOrderInfo>> GetOrdersByCode(const AnsiString& code);
    std::vector<std::shared_ptr<TOrderInfo>> GetOrdersByState(TOrderState state);
    
    // 상태 관리
    void UpdateOrderState(const AnsiString& orderId, TOrderState newState);
    void UpdateOrderExecution(const AnsiString& orderId, int executedQty, double executedPrice);
    
    // 설정
    void SetStrategyParams(const TStrategyParams& params) { FStrategyParams = params; }
    TStrategyParams GetStrategyParams() const { return FStrategyParams; }
    void SetEnabled(bool enabled) { FIsEnabled = enabled; }
    bool IsEnabled() const { return FIsEnabled; }
    bool IsProcessing() const { return FIsProcessing; }
    
    // 통계
    int GetTotalOrderCount();
    int GetActiveOrderCount();
    int GetExecutedOrderCount();
    int GetCancelledOrderCount();
    double GetSuccessRate();
    
    // 주문 내역 정리
    void ClearCompletedOrders();
    void ClearOldOrders(int daysOld = 7);
    
    // 긴급 중단
    void EmergencyStop();
    void CancelAllOrders();
    
    // 로깅
    void WriteLog(TLogLevel level, const AnsiString& message);
    
    // 이벤트 선언
    __property TNotifyEvent OnOrderPlaced = { read = FOnOrderPlaced, write = FOnOrderPlaced };
    __property TNotifyEvent OnOrderExecuted = { read = FOnOrderExecuted, write = FOnOrderExecuted };
    __property TNotifyEvent OnOrderCancelled = { read = FOnOrderCancelled, write = FOnOrderCancelled };
    __property TNotifyEvent OnOrderStateChanged = { read = FOnOrderStateChanged, write = FOnOrderStateChanged };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnOrderPlaced;
    TNotifyEvent FOnOrderExecuted;
    TNotifyEvent FOnOrderCancelled;
    TNotifyEvent FOnOrderStateChanged;
};

#endif // TORDERMANAGER_H