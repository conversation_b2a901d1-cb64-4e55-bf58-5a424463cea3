#include "TUtils.h"
#include <System.JSON.hpp>
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>
#include <System.IOUtils.hpp>
#include <Vcl.Graphics.hpp>

// 정적 멤버 초기화
AnsiString TUtils::FLastErrorMessage = "";

// 문자열 유틸리티 구현
AnsiString TUtils::FormatNumber(double value, int decimals) {
    return FormatFloat("0." + AnsiString::StringOfChar('0', decimals), value);
}

AnsiString TUtils::FormatCurrency(double value) {
    return FormatFloat("#,##0", value) + "원";
}

AnsiString TUtils::FormatPercent(double value, int decimals) {
    return FormatFloat("0." + AnsiString::StringOfChar('0', decimals), value) + "%";
}

AnsiString TUtils::FormatDateTime(TDateTime dt, const AnsiString& format) {
    if (dt == 0.0) dt = Now();
    return FormatDateTime(format, dt);
}

AnsiString TUtils::Trim(const AnsiString& str) {
    return str.Trim();
}

std::vector<AnsiString> TUtils::Split(const AnsiString& str, const AnsiString& delimiter) {
    std::vector<AnsiString> result;
    AnsiString temp = str;
    int pos = 0;
    
    while ((pos = temp.Pos(delimiter)) > 0) {
        result.push_back(temp.SubString(1, pos - 1));
        temp = temp.SubString(pos + delimiter.Length(), temp.Length());
    }
    
    if (!temp.IsEmpty()) {
        result.push_back(temp);
    }
    
    return result;
}

// 날짜/시간 유틸리티 구현
bool TUtils::IsMarketOpen(TDateTime dateTime) {
    if (dateTime == 0.0) dateTime = Now();
    
    // 주말 체크
    if (!IsWeekday(dateTime)) return false;
    
    // 시간 추출
    Word hour, min, sec, msec;
    DecodeTime(dateTime, hour, min, sec, msec);
    
    // 한국 주식 시장: 09:00 ~ 15:30
    if (hour < 9) return false;
    if (hour > 15) return false;
    if (hour == 15 && min > 30) return false;
    
    return true;
}

bool TUtils::IsWeekday(TDateTime dateTime) {
    if (dateTime == 0.0) dateTime = Now();
    int dayOfWeek = DayOfTheWeek(dateTime);
    return (dayOfWeek >= 1 && dayOfWeek <= 5);  // 월~금
}

TDateTime TUtils::GetMarketOpenTime(TDateTime date) {
    if (date == 0.0) date = Date();
    return date + EncodeTime(9, 0, 0, 0);
}

TDateTime TUtils::GetMarketCloseTime(TDateTime date) {
    if (date == 0.0) date = Date();
    return date + EncodeTime(15, 30, 0, 0);
}

int TUtils::GetTradingDaysBetween(TDateTime startDate, TDateTime endDate) {
    int count = 0;
    TDateTime current = startDate;
    
    while (current <= endDate) {
        if (IsWeekday(current)) {
            count++;
        }
        current = current + 1.0;  // 하루 추가
    }
    
    return count;
}

// 옵션 관련 유틸리티 구현
bool TUtils::IsOptionCode(const AnsiString& code) {
    return code.Length() >= 8 && (IsCallOption(code) || IsPutOption(code));
}

bool TUtils::IsCallOption(const AnsiString& code) {
    return code.Pos("C") > 0 || code.Pos("Call") > 0;
}

bool TUtils::IsPutOption(const AnsiString& code) {
    return code.Pos("P") > 0 || code.Pos("Put") > 0;
}

double TUtils::ExtractStrikePrice(const AnsiString& code) {
    // 옵션 코드에서 행사가 추출 (구현 예시)
    // 실제 구현은 XingAPI 규격에 맞춰 조정 필요
    try {
        // 간단한 패턴 매칭 예시
        for (int i = 1; i <= code.Length() - 2; i++) {
            if (isdigit(code[i]) && isdigit(code[i+1]) && isdigit(code[i+2])) {
                AnsiString priceStr = code.SubString(i, 3);
                return StrToFloat(priceStr);
            }
        }
    } catch (...) {
        return 0.0;
    }
    return 0.0;
}

TDateTime TUtils::ExtractExpirationDate(const AnsiString& code) {
    // 옵션 코드에서 만료일 추출 (구현 예시)
    try {
        // YYYYMMDD 형태 찾기
        for (int i = 1; i <= code.Length() - 7; i++) {
            if (isdigit(code[i])) {
                AnsiString dateStr = code.SubString(i, 8);
                if (dateStr.Length() == 8) {
                    return ParseKoreanDate(dateStr);
                }
            }
        }
    } catch (...) {
        return 0.0;
    }
    return 0.0;
}

AnsiString TUtils::GetUnderlyingCode(const AnsiString& optionCode) {
    // 옵션 코드에서 기초자산 코드 추출
    if (optionCode.Length() >= 6) {
        return optionCode.SubString(1, 6);  // 첫 6자리가 보통 기초자산
    }
    return "";
}

// 파일 유틸리티 구현
bool TUtils::FileExists(const AnsiString& fileName) {
    return TFile::Exists(fileName);
}

bool TUtils::DirectoryExists(const AnsiString& dirName) {
    return TDirectory::Exists(dirName);
}

bool TUtils::CreateDirectoryRecursive(const AnsiString& path) {
    try {
        if (!DirectoryExists(path)) {
            TDirectory::CreateDirectory(path);
        }
        return true;
    } catch (...) {
        return false;
    }
}

AnsiString TUtils::GetAppDataPath() {
    return TPath::GetDocumentsPath() + "\\OptionTrader\\";
}

AnsiString TUtils::GetLogPath() {
    AnsiString path = GetAppDataPath() + "logs\\";
    CreateDirectoryRecursive(path);
    return path;
}

AnsiString TUtils::GetConfigPath() {
    AnsiString path = GetAppDataPath() + "config\\";
    CreateDirectoryRecursive(path);
    return path;
}

// 컬러 유틸리티 구현
TColor TUtils::GetProfitColor(double profitRate) {
    if (profitRate > 0) return clRed;      // 수익: 빨간색
    else if (profitRate < 0) return clBlue;  // 손실: 파란색
    else return clBlack;                     // 보합: 검은색
}

TColor TUtils::GetVolumeColor(int volume) {
    if (volume > 10000) return clRed;
    else if (volume > 1000) return clMaroon;
    else return clGray;
}

TColor TUtils::GetTrendColor(double change) {
    return GetProfitColor(change);
}

// 통계 함수 구현
double TUtils::CalculateMean(const std::vector<double>& data) {
    if (data.empty()) return 0.0;
    
    double sum = 0.0;
    for (double value : data) {
        sum += value;
    }
    return sum / data.size();
}

double TUtils::CalculateStdDev(const std::vector<double>& data) {
    return sqrt(CalculateVariance(data));
}

double TUtils::CalculateVariance(const std::vector<double>& data) {
    if (data.size() < 2) return 0.0;
    
    double mean = CalculateMean(data);
    double variance = 0.0;
    
    for (double value : data) {
        double diff = value - mean;
        variance += diff * diff;
    }
    
    return variance / (data.size() - 1);
}

double TUtils::CalculateMin(const std::vector<double>& data) {
    if (data.empty()) return 0.0;
    return *std::min_element(data.begin(), data.end());
}

double TUtils::CalculateMax(const std::vector<double>& data) {
    if (data.empty()) return 0.0;
    return *std::max_element(data.begin(), data.end());
}

std::vector<double> TUtils::CalculateMovingAverage(const std::vector<double>& data, int period) {
    std::vector<double> result;
    if (data.size() < period) return result;
    
    for (int i = period - 1; i < data.size(); i++) {
        double sum = 0.0;
        for (int j = 0; j < period; j++) {
            sum += data[i - j];
        }
        result.push_back(sum / period);
    }
    
    return result;
}

// 데이터 타입 변환 구현
AnsiString TUtils::OrderStateToString(TOrderState state) {
    switch (state) {
        case TOrderState::WAITING: return "대기중";
        case TOrderState::PRE_ORDER: return "사전주문";
        case TOrderState::TRACKING: return "추적중";
        case TOrderState::EXECUTED: return "체결완료";
        case TOrderState::CANCELLED: return "취소됨";
        case TOrderState::PROFIT_TRACKING: return "익절추적";
        default: return "알수없음";
    }
}

AnsiString TUtils::OrderTypeToString(TOrderType type) {
    switch (type) {
        case TOrderType::LIMIT: return "지정가";
        case TOrderType::MARKET: return "시장가";
        case TOrderType::CANCEL: return "취소";
        default: return "알수없음";
    }
}

AnsiString TUtils::TradeTypeToString(TTradeType type) {
    switch (type) {
        case TTradeType::BUY: return "매수";
        case TTradeType::SELL: return "매도";
        default: return "알수없음";
    }
}

AnsiString TUtils::TradingModeToString(TTradingMode mode) {
    switch (mode) {
        case TTradingMode::REAL_TIME: return "실시간";
        case TTradingMode::SIMULATION: return "시뮬레이션";
        case TTradingMode::REPLAY: return "리플레이";
        default: return "알수없음";
    }
}

AnsiString TUtils::LogLevelToString(TLogLevel level) {
    switch (level) {
        case TLogLevel::DEBUG: return "DEBUG";
        case TLogLevel::INFO: return "INFO";
        case TLogLevel::WARNING: return "WARNING";
        case TLogLevel::ERROR: return "ERROR";
        case TLogLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

// JSON 유틸리티 구현 (간단한 버전)
AnsiString TUtils::OrderInfoToJSON(const TOrderInfo& order) {
    std::unique_ptr<TJSONObject> json(new TJSONObject());
    
    json->AddPair("order_id", order.order_id);
    json->AddPair("code", order.code);
    json->AddPair("quantity", TJSONNumber::Create(order.quantity));
    json->AddPair("price", TJSONNumber::Create(order.price));
    json->AddPair("state", OrderStateToString(order.state));
    json->AddPair("created_time", FormatDateTime(order.created_time));
    
    return json->ToString();
}

// 에러 처리 구현
void TUtils::HandleException(const Exception& e, const AnsiString& context) {
    AnsiString errorMsg = context.IsEmpty() ? 
        e.Message : context + ": " + e.Message;
    SetLastErrorMessage(errorMsg);
    
    // 로깅 (실제 로거가 구현되면 연결)
    // Logger::WriteError(errorMsg);
}

AnsiString TUtils::GetLastErrorMessage() {
    return FLastErrorMessage;
}

void TUtils::SetLastErrorMessage(const AnsiString& message) {
    FLastErrorMessage = message;
}

// 내부 헬퍼 함수 구현
TDateTime TUtils::ParseKoreanDate(const AnsiString& dateStr) {
    if (dateStr.Length() != 8) return 0.0;
    
    try {
        int year = StrToInt(dateStr.SubString(1, 4));
        int month = StrToInt(dateStr.SubString(5, 2));
        int day = StrToInt(dateStr.SubString(7, 2));
        
        return EncodeDate(year, month, day);
    } catch (...) {
        return 0.0;
    }
}