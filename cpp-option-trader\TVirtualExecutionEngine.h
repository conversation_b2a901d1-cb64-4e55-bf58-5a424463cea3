#ifndef TVirtualExecutionEngineH
#define TVirtualExecutionEngineH

#include <System.Classes.hpp>
#include <memory>
#include <map>
#include <vector>
#include <queue>
#include <mutex>
#include <random>
#include "DataTypes.h"
#include "TUtils.h"

struct TVirtualOrderBook {
    AnsiString code;
    std::map<double, int> bidOrders;  // 가격별 매수 주문량
    std::map<double, int> askOrders;  // 가격별 매도 주문량
    THogaData currentHoga;
    double lastPrice;
    TDateTime lastUpdateTime;
};

struct TVirtualFillResult {
    AnsiString orderNo;
    AnsiString code;
    int filledQty;
    double fillPrice;
    TDateTime fillTime;
    bool isPartialFill;
};

class TVirtualExecutionEngine : public TObject
{
private:
    std::unique_ptr<TUtils> FUtils;
    std::map<AnsiString, TVirtualOrderBook> FOrderBooks;
    std::map<AnsiString, TOrderInfo> FPendingOrders;
    std::vector<TOrderInfo> FCompletedOrders;
    std::mutex FOrderMutex;
    
    // 설정 파라미터
    double FSlippageRate;        // 슬리피지 비율 (0.001 = 0.1%)
    double FPartialFillRate;     // 부분체결 확률 (0.3 = 30%)
    double FRejectRate;          // 주문 거부 확률 (0.01 = 1%)
    int FLatencyMs;              // 주문 처리 지연 시간 (밀리초)
    bool FUseRealisticExecution; // 현실적 체결 모드
    
    // 랜덤 엔진
    std::mt19937 FRandomEngine;
    std::uniform_real_distribution<double> FUniformDist;
    
    void InitializeOrderBook(const AnsiString& code);
    void UpdateOrderBook(const AnsiString& code, const THogaData& hoga);
    TVirtualFillResult ProcessMarketOrder(const TOrderInfo& order);
    TVirtualFillResult ProcessLimitOrder(const TOrderInfo& order);
    double CalculateExecutionPrice(const TOrderInfo& order, double marketPrice);
    int CalculateExecutionQuantity(const TOrderInfo& order, const TVirtualOrderBook& orderBook);
    bool ShouldRejectOrder(const TOrderInfo& order);
    bool ShouldPartialFill(const TOrderInfo& order);
    AnsiString GenerateOrderNumber();
    
public:
    __fastcall TVirtualExecutionEngine();
    __fastcall ~TVirtualExecutionEngine();
    
    // 이벤트 핸들러
    TOnOrderStatusChanged OnOrderStatusChanged;
    TOnOrderFilled OnOrderFilled;
    
    // 설정 메서드
    void SetSlippageRate(double rate) { FSlippageRate = rate; }
    void SetPartialFillRate(double rate) { FPartialFillRate = rate; }
    void SetRejectRate(double rate) { FRejectRate = rate; }
    void SetLatency(int latencyMs) { FLatencyMs = latencyMs; }
    void SetRealisticMode(bool enabled) { FUseRealisticExecution = enabled; }
    
    // 주문 처리
    AnsiString SubmitOrder(const TOrderInfo& order);
    bool CancelOrder(const AnsiString& orderNo);
    bool ModifyOrder(const AnsiString& orderNo, double newPrice, int newQuantity);
    
    // 시장 데이터 업데이트
    void OnHogaUpdate(const THogaData& hoga);
    void OnJeobsuUpdate(const TJeobsuData& jeobsu);
    
    // 주문 상태 조회
    TOrderInfo GetOrderInfo(const AnsiString& orderNo);
    std::vector<TOrderInfo> GetPendingOrders(const AnsiString& code = "");
    std::vector<TOrderInfo> GetCompletedOrders(const AnsiString& code = "");
    std::vector<TVirtualFillResult> GetFillHistory(const AnsiString& code = "");
    
    // 통계 정보
    double GetFillRate(const AnsiString& code = "");
    double GetAverageLatency(const AnsiString& code = "");
    int GetTotalOrderCount();
    int GetFilledOrderCount();
    
    // 초기화
    void Reset();
};

#endif