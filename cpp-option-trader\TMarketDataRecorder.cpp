#include <vcl.h>
#pragma hdrstop
#include "TMarketDataRecorder.h"
#include <SysUtils.hpp>
#include <IOUtils.hpp>

__fastcall TMarketDataRecorder::TMarketDataRecorder()
    : FIsRecording(false), FCurrentDate(Date())
{
    FUtils = std::make_unique<TUtils>();
    FDataFolder = ExtractFilePath(Application->ExeName) + "dataFolder";
}

__fastcall TMarketDataRecorder::~TMarketDataRecorder()
{
    StopRecording();
    CloseAllFiles();
}

void TMarketDataRecorder::SetDataFolder(const AnsiString& folder)
{
    FDataFolder = folder;
    CreateDirectoryIfNotExists(FDataFolder);
    CreateDirectoryIfNotExists(FDataFolder + "\\hoga");
    CreateDirectoryIfNotExists(FDataFolder + "\\jeobsu");
}

void TMarketDataRecorder::StartRecording()
{
    if (!FIsRecording) {
        FIsRecording = true;
        FCurrentDate = Date();
        CreateDirectoryIfNotExists(FDataFolder);
        CreateDirectoryIfNotExists(FDataFolder + "\\hoga");
        CreateDirectoryIfNotExists(FDataFolder + "\\jeobsu");
    }
}

void TMarketDataRecorder::StopRecording()
{
    if (FIsRecording) {
        FIsRecording = false;
        FlushAllFiles();
        CloseAllFiles();
    }
}

void TMarketDataRecorder::RecordHogaData(const THogaData& hoga)
{
    if (!FIsRecording || hoga.code.IsEmpty()) return;
    
    try {
        std::lock_guard<std::mutex> lock(FFileMutex);
        
        CheckDateChange();
        
        // 해당 종목의 호가 파일이 없으면 생성
        if (FHogaFiles.find(hoga.code) == FHogaFiles.end()) {
            OpenFileForCode(hoga.code);
        }
        
        auto it = FHogaFiles.find(hoga.code);
        if (it != FHogaFiles.end() && it->second) {
            AnsiString dataLine = FormatHogaData(hoga);
            *it->second << dataLine.c_str() << std::endl;
            it->second->flush();
        }
    }
    catch (const Exception& e) {
        // 로그 오류 처리
    }
}

void TMarketDataRecorder::RecordJeobsuData(const TJeobsuData& jeobsu)
{
    if (!FIsRecording || jeobsu.code.IsEmpty()) return;
    
    try {
        std::lock_guard<std::mutex> lock(FFileMutex);
        
        CheckDateChange();
        
        // 해당 종목의 체결 파일이 없으면 생성
        if (FJeobsuFiles.find(jeobsu.code) == FJeobsuFiles.end()) {
            OpenFileForCode(jeobsu.code);
        }
        
        auto it = FJeobsuFiles.find(jeobsu.code);
        if (it != FJeobsuFiles.end() && it->second) {
            AnsiString dataLine = FormatJeobsuData(jeobsu);
            *it->second << dataLine.c_str() << std::endl;
            it->second->flush();
        }
    }
    catch (const Exception& e) {
        // 로그 오류 처리
    }
}

AnsiString TMarketDataRecorder::GetHogaFileName(const AnsiString& code, TDateTime date)
{
    AnsiString dateStr = FormatDateTime("yyyymmdd", date);
    return FDataFolder + "\\hoga\\hoga_" + code + "_" + dateStr + ".txt";
}

AnsiString TMarketDataRecorder::GetJeobsuFileName(const AnsiString& code, TDateTime date)
{
    AnsiString dateStr = FormatDateTime("yyyymmdd", date);
    return FDataFolder + "\\jeobsu\\jeobsu_" + code + "_" + dateStr + ".txt";
}

void TMarketDataRecorder::CreateDirectoryIfNotExists(const AnsiString& path)
{
    if (!DirectoryExists(path)) {
        ForceDirectories(path);
    }
}

void TMarketDataRecorder::OpenFileForCode(const AnsiString& code)
{
    try {
        // 호가 파일 열기
        AnsiString hogaFileName = GetHogaFileName(code, FCurrentDate);
        auto hogaFile = std::make_unique<std::ofstream>(hogaFileName.c_str(), 
                                                       std::ios::out | std::ios::app);
        if (hogaFile->is_open()) {
            // 파일이 새로 생성된 경우 헤더 추가
            if (hogaFile->tellp() == 0) {
                *hogaFile << "Time,Code,BidPrice1,BidQty1,BidPrice2,BidQty2,BidPrice3,BidQty3,"
                         << "BidPrice4,BidQty4,BidPrice5,BidQty5,AskPrice1,AskQty1,AskPrice2,AskQty2,"
                         << "AskPrice3,AskQty3,AskPrice4,AskQty4,AskPrice5,AskQty5,TotalBidQty,TotalAskQty" 
                         << std::endl;
            }
            FHogaFiles[code] = std::move(hogaFile);
        }
        
        // 체결 파일 열기
        AnsiString jeobsuFileName = GetJeobsuFileName(code, FCurrentDate);
        auto jeobsuFile = std::make_unique<std::ofstream>(jeobsuFileName.c_str(), 
                                                         std::ios::out | std::ios::app);
        if (jeobsuFile->is_open()) {
            // 파일이 새로 생성된 경우 헤더 추가
            if (jeobsuFile->tellp() == 0) {
                *jeobsuFile << "Time,Code,Price,Quantity,Volume,Change,ChangeRate,TradeType" << std::endl;
            }
            FJeobsuFiles[code] = std::move(jeobsuFile);
        }
    }
    catch (const Exception& e) {
        // 파일 열기 오류 처리
    }
}

void TMarketDataRecorder::CloseAllFiles()
{
    std::lock_guard<std::mutex> lock(FFileMutex);
    
    for (auto& pair : FHogaFiles) {
        if (pair.second && pair.second->is_open()) {
            pair.second->close();
        }
    }
    FHogaFiles.clear();
    
    for (auto& pair : FJeobsuFiles) {
        if (pair.second && pair.second->is_open()) {
            pair.second->close();
        }
    }
    FJeobsuFiles.clear();
}

void TMarketDataRecorder::CheckDateChange()
{
    TDateTime currentDate = Date();
    if (currentDate != FCurrentDate) {
        // 날짜가 바뀐 경우 기존 파일들 닫고 새로 열기
        CloseAllFiles();
        FCurrentDate = currentDate;
    }
}

AnsiString TMarketDataRecorder::FormatHogaData(const THogaData& hoga)
{
    AnsiString timeStr = FormatDateTime("hh:nn:ss.zzz", hoga.time);
    
    AnsiString result = Format("%s,%s,%.0f,%d,%.0f,%d,%.0f,%d,%.0f,%d,%.0f,%d,"
                              "%.0f,%d,%.0f,%d,%.0f,%d,%.0f,%d,%.0f,%d,%d,%d",
        ARRAYOFCONST((
            timeStr, hoga.code,
            hoga.bidPrice1, hoga.bidQty1, hoga.bidPrice2, hoga.bidQty2,
            hoga.bidPrice3, hoga.bidQty3, hoga.bidPrice4, hoga.bidQty4,
            hoga.bidPrice5, hoga.bidQty5,
            hoga.askPrice1, hoga.askQty1, hoga.askPrice2, hoga.askQty2,
            hoga.askPrice3, hoga.askQty3, hoga.askPrice4, hoga.askQty4,
            hoga.askPrice5, hoga.askQty5,
            hoga.totalBidQty, hoga.totalAskQty
        )));
    
    return result;
}

AnsiString TMarketDataRecorder::FormatJeobsuData(const TJeobsuData& jeobsu)
{
    AnsiString timeStr = FormatDateTime("hh:nn:ss.zzz", jeobsu.time);
    AnsiString tradeType = jeobsu.change > 0 ? "매수" : (jeobsu.change < 0 ? "매도" : "보합");
    
    AnsiString result = Format("%s,%s,%.0f,%d,%d,%.0f,%.2f,%s",
        ARRAYOFCONST((
            timeStr, jeobsu.code, jeobsu.price, jeobsu.quantity,
            jeobsu.volume, jeobsu.change, jeobsu.changeRate, tradeType
        )));
    
    return result;
}

void TMarketDataRecorder::FlushAllFiles()
{
    std::lock_guard<std::mutex> lock(FFileMutex);
    
    for (auto& pair : FHogaFiles) {
        if (pair.second && pair.second->is_open()) {
            pair.second->flush();
        }
    }
    
    for (auto& pair : FJeobsuFiles) {
        if (pair.second && pair.second->is_open()) {
            pair.second->flush();
        }
    }
}