#include <vcl.h>
#pragma hdrstop
#include "TVirtualExecutionEngine.h"
#include <SysUtils.hpp>
#include <algorithm>

__fastcall TVirtualExecutionEngine::TVirtualExecutionEngine()
    : FSlippageRate(0.001), FPartialFillRate(0.3), FRejectRate(0.01), 
      FLatencyMs(50), FUseRealisticExecution(true),
      FRandomEngine(std::random_device{}()), FUniformDist(0.0, 1.0)
{
    FUtils = std::make_unique<TUtils>();
}

__fastcall TVirtualExecutionEngine::~TVirtualExecutionEngine()
{
    Reset();
}

AnsiString TVirtualExecutionEngine::SubmitOrder(const TOrderInfo& order)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    // 주문 거부 확률 체크
    if (ShouldRejectOrder(order)) {
        TOrderInfo rejectedOrder = order;
        rejectedOrder.status = "주문거부";
        rejectedOrder.time = Now();
        
        if (OnOrderStatusChanged) {
            TThread::Synchronize(nullptr, [this, rejectedOrder]() {
                OnOrderStatusChanged(this, rejectedOrder);
            });
        }
        return "";
    }
    
    // 주문번호 생성
    AnsiString orderNo = GenerateOrderNumber();
    TOrderInfo newOrder = order;
    newOrder.orderNo = orderNo;
    newOrder.status = "주문접수";
    newOrder.time = Now();
    newOrder.filledQty = 0;
    newOrder.avgFillPrice = 0.0;
    
    // 주문 처리 지연 시뮬레이션
    if (FLatencyMs > 0) {
        Sleep(FLatencyMs);
    }
    
    FPendingOrders[orderNo] = newOrder;
    
    // 주문북 초기화 (필요시)
    if (FOrderBooks.find(order.code) == FOrderBooks.end()) {
        InitializeOrderBook(order.code);
    }
    
    // 주문 상태 변경 이벤트
    newOrder.status = "주문확인";
    FPendingOrders[orderNo] = newOrder;
    
    if (OnOrderStatusChanged) {
        TThread::Synchronize(nullptr, [this, newOrder]() {
            OnOrderStatusChanged(this, newOrder);
        });
    }
    
    // 즉시 체결 가능한지 확인 (시장가 주문 또는 조건 만족시)
    if (order.orderType == otMarket || 
        (order.orderType == otLimit && CanExecuteImmediately(order))) {
        
        TVirtualFillResult fillResult;
        if (order.orderType == otMarket) {
            fillResult = ProcessMarketOrder(newOrder);
        } else {
            fillResult = ProcessLimitOrder(newOrder);
        }
        
        if (fillResult.filledQty > 0) {
            ProcessFillResult(fillResult);
        }
    }
    
    return orderNo;
}

bool TVirtualExecutionEngine::CancelOrder(const AnsiString& orderNo)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    auto it = FPendingOrders.find(orderNo);
    if (it != FPendingOrders.end()) {
        TOrderInfo cancelledOrder = it->second;
        cancelledOrder.status = "주문취소";
        cancelledOrder.time = Now();
        
        FPendingOrders.erase(it);
        FCompletedOrders.push_back(cancelledOrder);
        
        if (OnOrderStatusChanged) {
            TThread::Synchronize(nullptr, [this, cancelledOrder]() {
                OnOrderStatusChanged(this, cancelledOrder);
            });
        }
        
        return true;
    }
    
    return false;
}

bool TVirtualExecutionEngine::ModifyOrder(const AnsiString& orderNo, double newPrice, int newQuantity)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    auto it = FPendingOrders.find(orderNo);
    if (it != FPendingOrders.end()) {
        TOrderInfo& order = it->second;
        order.price = newPrice;
        order.quantity = newQuantity;
        order.status = "주문수정";
        order.time = Now();
        
        if (OnOrderStatusChanged) {
            TThread::Synchronize(nullptr, [this, order]() {
                OnOrderStatusChanged(this, order);
            });
        }
        
        return true;
    }
    
    return false;
}

void TVirtualExecutionEngine::OnHogaUpdate(const THogaData& hoga)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    UpdateOrderBook(hoga.code, hoga);
    
    // 해당 종목의 미체결 지정가 주문들 체결 가능성 체크
    std::vector<AnsiString> ordersToProcess;
    for (const auto& pair : FPendingOrders) {
        const TOrderInfo& order = pair.second;
        if (order.code == hoga.code && order.orderType == otLimit) {
            ordersToProcess.push_back(pair.first);
        }
    }
    
    for (const AnsiString& orderNo : ordersToProcess) {
        auto it = FPendingOrders.find(orderNo);
        if (it != FPendingOrders.end()) {
            const TOrderInfo& order = it->second;
            
            bool canExecute = false;
            if (order.side == osLong && order.price >= hoga.askPrice1) {
                canExecute = true;
            } else if (order.side == osShort && order.price <= hoga.bidPrice1) {
                canExecute = true;
            }
            
            if (canExecute) {
                TVirtualFillResult fillResult = ProcessLimitOrder(order);
                if (fillResult.filledQty > 0) {
                    ProcessFillResult(fillResult);
                }
            }
        }
    }
}

void TVirtualExecutionEngine::OnJeobsuUpdate(const TJeobsuData& jeobsu)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    auto it = FOrderBooks.find(jeobsu.code);
    if (it != FOrderBooks.end()) {
        it->second.lastPrice = jeobsu.price;
        it->second.lastUpdateTime = jeobsu.time;
    }
}

TVirtualFillResult TVirtualExecutionEngine::ProcessMarketOrder(const TOrderInfo& order)
{
    TVirtualFillResult result;
    result.orderNo = order.orderNo;
    result.code = order.code;
    result.fillTime = Now();
    result.filledQty = 0;
    result.fillPrice = 0.0;
    result.isPartialFill = false;
    
    auto it = FOrderBooks.find(order.code);
    if (it == FOrderBooks.end()) {
        return result; // 주문북이 없음
    }
    
    const TVirtualOrderBook& orderBook = it->second;
    double marketPrice;
    
    if (order.side == osLong) {
        marketPrice = orderBook.currentHoga.askPrice1;
    } else {
        marketPrice = orderBook.currentHoga.bidPrice1;
    }
    
    if (marketPrice <= 0) {
        return result; // 유효한 가격이 없음
    }
    
    // 시장가는 일반적으로 전량 체결
    result.filledQty = order.quantity;
    result.fillPrice = CalculateExecutionPrice(order, marketPrice);
    
    // 부분체결 시뮬레이션 (시장 상황에 따라)
    if (FUseRealisticExecution && ShouldPartialFill(order)) {
        double fillRatio = 0.5 + FUniformDist(FRandomEngine) * 0.5; // 50~100%
        result.filledQty = static_cast<int>(order.quantity * fillRatio);
        result.isPartialFill = (result.filledQty < order.quantity);
    }
    
    return result;
}

TVirtualFillResult TVirtualExecutionEngine::ProcessLimitOrder(const TOrderInfo& order)
{
    TVirtualFillResult result;
    result.orderNo = order.orderNo;
    result.code = order.code;
    result.fillTime = Now();
    result.filledQty = 0;
    result.fillPrice = order.price;
    result.isPartialFill = false;
    
    auto it = FOrderBooks.find(order.code);
    if (it == FOrderBooks.end()) {
        return result;
    }
    
    const TVirtualOrderBook& orderBook = it->second;
    
    // 지정가 주문 체결 조건 확인
    bool canExecute = false;
    double marketPrice = 0;
    
    if (order.side == osLong) {
        marketPrice = orderBook.currentHoga.askPrice1;
        canExecute = (order.price >= marketPrice);
    } else {
        marketPrice = orderBook.currentHoga.bidPrice1;
        canExecute = (order.price <= marketPrice);
    }
    
    if (!canExecute) {
        return result; // 체결 조건 불만족
    }
    
    // 체결 수량 계산
    result.filledQty = CalculateExecutionQuantity(order, orderBook);
    
    // 부분체결 처리
    if (FUseRealisticExecution && ShouldPartialFill(order)) {
        double fillRatio = 0.3 + FUniformDist(FRandomEngine) * 0.7; // 30~100%
        result.filledQty = std::min(result.filledQty, 
                                   static_cast<int>(order.quantity * fillRatio));
        result.isPartialFill = (result.filledQty < order.quantity);
    }
    
    // 지정가로 체결 (더 유리한 가격이면 그 가격으로)
    if (order.side == osLong && marketPrice < order.price) {
        result.fillPrice = marketPrice;
    } else if (order.side == osShort && marketPrice > order.price) {
        result.fillPrice = marketPrice;
    }
    
    return result;
}

double TVirtualExecutionEngine::CalculateExecutionPrice(const TOrderInfo& order, double marketPrice)
{
    if (!FUseRealisticExecution) {
        return marketPrice;
    }
    
    // 슬리피지 적용
    double slippage = marketPrice * FSlippageRate * (FUniformDist(FRandomEngine) * 2.0 - 1.0);
    
    if (order.side == osLong) {
        return marketPrice + abs(slippage); // 매수는 불리하게
    } else {
        return marketPrice - abs(slippage); // 매도는 불리하게
    }
}

int TVirtualExecutionEngine::CalculateExecutionQuantity(const TOrderInfo& order, 
                                                       const TVirtualOrderBook& orderBook)
{
    int availableQty = 0;
    
    if (order.side == osLong) {
        // 매수: 현재 호가의 매도 물량 확인
        if (order.price >= orderBook.currentHoga.askPrice1) {
            availableQty += orderBook.currentHoga.askQty1;
        }
        if (order.price >= orderBook.currentHoga.askPrice2) {
            availableQty += orderBook.currentHoga.askQty2;
        }
        // 추가 호가 단계들...
    } else {
        // 매도: 현재 호가의 매수 물량 확인
        if (order.price <= orderBook.currentHoga.bidPrice1) {
            availableQty += orderBook.currentHoga.bidQty1;
        }
        if (order.price <= orderBook.currentHoga.bidPrice2) {
            availableQty += orderBook.currentHoga.bidQty2;
        }
        // 추가 호가 단계들...
    }
    
    return std::min(order.quantity, availableQty);
}

bool TVirtualExecutionEngine::ShouldRejectOrder(const TOrderInfo& order)
{
    if (!FUseRealisticExecution) return false;
    
    return FUniformDist(FRandomEngine) < FRejectRate;
}

bool TVirtualExecutionEngine::ShouldPartialFill(const TOrderInfo& order)
{
    if (!FUseRealisticExecution) return false;
    
    return FUniformDist(FRandomEngine) < FPartialFillRate;
}

AnsiString TVirtualExecutionEngine::GenerateOrderNumber()
{
    static int orderCounter = 1;
    AnsiString dateStr = FormatDateTime("yyyymmdd", Now());
    return dateStr + Format("%06d", ARRAYOFCONST((orderCounter++)));
}

void TVirtualExecutionEngine::InitializeOrderBook(const AnsiString& code)
{
    TVirtualOrderBook orderBook;
    orderBook.code = code;
    orderBook.lastPrice = 0.0;
    orderBook.lastUpdateTime = Now();
    FOrderBooks[code] = orderBook;
}

void TVirtualExecutionEngine::UpdateOrderBook(const AnsiString& code, const THogaData& hoga)
{
    auto it = FOrderBooks.find(code);
    if (it != FOrderBooks.end()) {
        it->second.currentHoga = hoga;
        it->second.lastUpdateTime = hoga.time;
    }
}

void TVirtualExecutionEngine::ProcessFillResult(const TVirtualFillResult& fillResult)
{
    auto it = FPendingOrders.find(fillResult.orderNo);
    if (it != FPendingOrders.end()) {
        TOrderInfo& order = it->second;
        
        // 체결 정보 업데이트
        int previousFilledQty = order.filledQty;
        double previousAvgPrice = order.avgFillPrice;
        
        order.filledQty += fillResult.filledQty;
        
        // 평균 체결가 계산
        if (order.filledQty > 0) {
            order.avgFillPrice = ((previousAvgPrice * previousFilledQty) + 
                                 (fillResult.fillPrice * fillResult.filledQty)) / order.filledQty;
        }
        
        // 주문 상태 업데이트
        if (order.filledQty >= order.quantity) {
            order.status = "체결완료";
            FCompletedOrders.push_back(order);
            FPendingOrders.erase(it);
        } else {
            order.status = "부분체결";
        }
        
        // 이벤트 발생
        if (OnOrderFilled) {
            TThread::Synchronize(nullptr, [this, order]() {
                OnOrderFilled(order);
            });
        }
        
        if (OnOrderStatusChanged) {
            TThread::Synchronize(nullptr, [this, order]() {
                OnOrderStatusChanged(this, order);
            });
        }
    }
}

bool TVirtualExecutionEngine::CanExecuteImmediately(const TOrderInfo& order)
{
    auto it = FOrderBooks.find(order.code);
    if (it == FOrderBooks.end()) return false;
    
    const TVirtualOrderBook& orderBook = it->second;
    
    if (order.side == osLong) {
        return order.price >= orderBook.currentHoga.askPrice1;
    } else {
        return order.price <= orderBook.currentHoga.bidPrice1;
    }
}

TOrderInfo TVirtualExecutionEngine::GetOrderInfo(const AnsiString& orderNo)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    auto it = FPendingOrders.find(orderNo);
    if (it != FPendingOrders.end()) {
        return it->second;
    }
    
    // 완료된 주문에서 검색
    for (const auto& order : FCompletedOrders) {
        if (order.orderNo == orderNo) {
            return order;
        }
    }
    
    return TOrderInfo(); // 빈 주문 정보 반환
}

std::vector<TOrderInfo> TVirtualExecutionEngine::GetPendingOrders(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    std::vector<TOrderInfo> result;
    for (const auto& pair : FPendingOrders) {
        if (code.IsEmpty() || pair.second.code == code) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

std::vector<TOrderInfo> TVirtualExecutionEngine::GetCompletedOrders(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    std::vector<TOrderInfo> result;
    for (const auto& order : FCompletedOrders) {
        if (code.IsEmpty() || order.code == code) {
            result.push_back(order);
        }
    }
    
    return result;
}

double TVirtualExecutionEngine::GetFillRate(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    int totalOrders = 0;
    int filledOrders = 0;
    
    for (const auto& order : FCompletedOrders) {
        if (code.IsEmpty() || order.code == code) {
            totalOrders++;
            if (order.filledQty > 0) {
                filledOrders++;
            }
        }
    }
    
    return totalOrders > 0 ? static_cast<double>(filledOrders) / totalOrders : 0.0;
}

int TVirtualExecutionEngine::GetTotalOrderCount()
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    return FPendingOrders.size() + FCompletedOrders.size();
}

int TVirtualExecutionEngine::GetFilledOrderCount()
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    int count = 0;
    for (const auto& order : FCompletedOrders) {
        if (order.filledQty > 0) {
            count++;
        }
    }
    
    return count;
}

void TVirtualExecutionEngine::Reset()
{
    std::lock_guard<std::mutex> lock(FOrderMutex);
    
    FPendingOrders.clear();
    FCompletedOrders.clear();
    FOrderBooks.clear();
}