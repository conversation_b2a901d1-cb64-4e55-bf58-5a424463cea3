#include <vcl.h>
#pragma hdrstop
#include "SettingsForm.h"

#pragma package(smart_init)
#pragma resource "*.dfm"

TSettingsForm* SettingsForm;

__fastcall TSettingsForm::TSettingsForm(TComponent* Owner)
    : TForm(Owner), FModified(false)
{
}

__fastcall TSettingsForm::~TSettingsForm()
{
    FUtils.reset();
}

void __fastcall TSettingsForm::FormCreate(TObject* Sender)
{
    try {
        FUtils = std::make_unique<TUtils>();
        
        // Initialize combo box
        ComboBoxBarInterval->Items->Clear();
        ComboBoxBarInterval->Items->Add("1분");
        ComboBoxBarInterval->Items->Add("3분");
        ComboBoxBarInterval->Items->Add("5분");
        ComboBoxBarInterval->Items->Add("10분");
        ComboBoxBarInterval->Items->Add("15분");
        ComboBoxBarInterval->Items->Add("30분");
        ComboBoxBarInterval->Items->Add("60분");
        ComboBoxBarInterval->ItemIndex = 2; // Default to 5분

        FModified = false;
    }
    catch (const Exception& e) {
        ShowMessage("설정폼 초기화 오류: " + e.Message);
    }
}

void __fastcall TSettingsForm::FormShow(TObject* Sender)
{
    LoadSettings();
    FModified = false;
}

void __fastcall TSettingsForm::ButtonOKClick(TObject* Sender)
{
    try {
        SaveSettings();
        ModalResult = mrOk;
    }
    catch (const Exception& e) {
        ShowMessage("설정 저장 오류: " + e.Message);
    }
}

void __fastcall TSettingsForm::ButtonCancelClick(TObject* Sender)
{
    if (FModified) {
        if (MessageDlg("변경사항이 있습니다. 저장하지 않고 닫으시겠습니까?", 
                      mtConfirmation, TMsgDlgButtons() << mbYes << mbNo, 0) == mrYes) {
            ModalResult = mrCancel;
        }
    } else {
        ModalResult = mrCancel;
    }
}

void __fastcall TSettingsForm::ButtonApplyClick(TObject* Sender)
{
    try {
        SaveSettings();
        FModified = false;
        ShowMessage("설정이 적용되었습니다.");
    }
    catch (const Exception& e) {
        ShowMessage("설정 적용 오류: " + e.Message);
    }
}

void TSettingsForm::LoadSettings()
{
    try {
        AnsiString configFile = ExtractFilePath(Application->ExeName) + "config.ini";
        
        // Connection Settings
        EditServerIP->Text = FUtils->ReadConfigString("Connection", "ServerIP", "demo.ebestsec.co.kr");
        EditServerPort->Text = FUtils->ReadConfigString("Connection", "ServerPort", "20001");
        EditUserID->Text = FUtils->ReadConfigString("Connection", "UserID", "");
        EditPassword->Text = FUtils->ReadConfigString("Connection", "Password", "");
        EditCertPassword->Text = FUtils->ReadConfigString("Connection", "CertPassword", "");
        CheckBoxAutoConnect->Checked = FUtils->ReadConfigBool("Connection", "AutoConnect", false);

        // Trading Settings
        EditDefaultQuantity->Text = IntToStr(FUtils->ReadConfigInt("Trading", "DefaultQuantity", 1));
        EditMaxPosition->Text = IntToStr(FUtils->ReadConfigInt("Trading", "MaxPosition", 10));
        EditSlippage->Text = IntToStr(FUtils->ReadConfigInt("Trading", "Slippage", 5));
        CheckBoxAutoTrading->Checked = FUtils->ReadConfigBool("Trading", "AutoTrading", false);
        CheckBoxOrderConfirm->Checked = FUtils->ReadConfigBool("Trading", "OrderConfirm", true);

        // Strategy Settings
        EditStochK->Text = IntToStr(FUtils->ReadConfigInt("Strategy", "StochK", 5));
        EditStochD->Text = IntToStr(FUtils->ReadConfigInt("Strategy", "StochD", 3));
        EditStochSlowing->Text = IntToStr(FUtils->ReadConfigInt("Strategy", "StochSlowing", 3));
        EditCCIPeriod->Text = IntToStr(FUtils->ReadConfigInt("Strategy", "CCIPeriod", 14));
        EditDMIPeriod->Text = IntToStr(FUtils->ReadConfigInt("Strategy", "DMIPeriod", 14));
        
        int barInterval = FUtils->ReadConfigInt("Strategy", "BarInterval", 5);
        switch (barInterval) {
            case 1: ComboBoxBarInterval->ItemIndex = 0; break;
            case 3: ComboBoxBarInterval->ItemIndex = 1; break;
            case 5: ComboBoxBarInterval->ItemIndex = 2; break;
            case 10: ComboBoxBarInterval->ItemIndex = 3; break;
            case 15: ComboBoxBarInterval->ItemIndex = 4; break;
            case 30: ComboBoxBarInterval->ItemIndex = 5; break;
            case 60: ComboBoxBarInterval->ItemIndex = 6; break;
            default: ComboBoxBarInterval->ItemIndex = 2; break;
        }

        // Risk Management Settings
        EditMaxLoss->Text = IntToStr(FUtils->ReadConfigInt("Risk", "MaxLoss", 500000));
        EditMaxProfit->Text = IntToStr(FUtils->ReadConfigInt("Risk", "MaxProfit", 1000000));
        EditStopLoss->Text = IntToStr(FUtils->ReadConfigInt("Risk", "StopLoss", 100000));
        EditTakeProfit->Text = IntToStr(FUtils->ReadConfigInt("Risk", "TakeProfit", 200000));
        CheckBoxRiskManagement->Checked = FUtils->ReadConfigBool("Risk", "RiskManagement", true);
    }
    catch (const Exception& e) {
        ShowMessage("설정 로드 오류: " + e.Message);
    }
}

void TSettingsForm::SaveSettings()
{
    try {
        AnsiString configFile = ExtractFilePath(Application->ExeName) + "config.ini";
        
        // Connection Settings
        FUtils->WriteConfigString("Connection", "ServerIP", EditServerIP->Text);
        FUtils->WriteConfigString("Connection", "ServerPort", EditServerPort->Text);
        FUtils->WriteConfigString("Connection", "UserID", EditUserID->Text);
        FUtils->WriteConfigString("Connection", "Password", EditPassword->Text);
        FUtils->WriteConfigString("Connection", "CertPassword", EditCertPassword->Text);
        FUtils->WriteConfigBool("Connection", "AutoConnect", CheckBoxAutoConnect->Checked);

        // Trading Settings
        FUtils->WriteConfigInt("Trading", "DefaultQuantity", StrToIntDef(EditDefaultQuantity->Text, 1));
        FUtils->WriteConfigInt("Trading", "MaxPosition", StrToIntDef(EditMaxPosition->Text, 10));
        FUtils->WriteConfigInt("Trading", "Slippage", StrToIntDef(EditSlippage->Text, 5));
        FUtils->WriteConfigBool("Trading", "AutoTrading", CheckBoxAutoTrading->Checked);
        FUtils->WriteConfigBool("Trading", "OrderConfirm", CheckBoxOrderConfirm->Checked);

        // Strategy Settings
        FUtils->WriteConfigInt("Strategy", "StochK", StrToIntDef(EditStochK->Text, 5));
        FUtils->WriteConfigInt("Strategy", "StochD", StrToIntDef(EditStochD->Text, 3));
        FUtils->WriteConfigInt("Strategy", "StochSlowing", StrToIntDef(EditStochSlowing->Text, 3));
        FUtils->WriteConfigInt("Strategy", "CCIPeriod", StrToIntDef(EditCCIPeriod->Text, 14));
        FUtils->WriteConfigInt("Strategy", "DMIPeriod", StrToIntDef(EditDMIPeriod->Text, 14));
        
        int barInterval = 5;
        switch (ComboBoxBarInterval->ItemIndex) {
            case 0: barInterval = 1; break;
            case 1: barInterval = 3; break;
            case 2: barInterval = 5; break;
            case 3: barInterval = 10; break;
            case 4: barInterval = 15; break;
            case 5: barInterval = 30; break;
            case 6: barInterval = 60; break;
        }
        FUtils->WriteConfigInt("Strategy", "BarInterval", barInterval);

        // Risk Management Settings
        FUtils->WriteConfigInt("Risk", "MaxLoss", StrToIntDef(EditMaxLoss->Text, 500000));
        FUtils->WriteConfigInt("Risk", "MaxProfit", StrToIntDef(EditMaxProfit->Text, 1000000));
        FUtils->WriteConfigInt("Risk", "StopLoss", StrToIntDef(EditStopLoss->Text, 100000));
        FUtils->WriteConfigInt("Risk", "TakeProfit", StrToIntDef(EditTakeProfit->Text, 200000));
        FUtils->WriteConfigBool("Risk", "RiskManagement", CheckBoxRiskManagement->Checked);

        FModified = false;
    }
    catch (const Exception& e) {
        ShowMessage("설정 저장 오류: " + e.Message);
    }
}