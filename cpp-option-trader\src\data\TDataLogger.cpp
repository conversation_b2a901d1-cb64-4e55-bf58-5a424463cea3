#include "TDataLogger.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>
#include <System.IOUtils.hpp>

// 생성자
__fastcall TDataLogger::TDataLogger(const AnsiString& dataPath) : TObject() {
    FDataPath = dataPath.IsEmpty() ? TUtils::GetDataPath() : dataPath;
    FLogPath = TPath::Combine(FDataPath, "logs");
    FMinLogLevel = TLogLevel::INFO;
    FIsEnabled = false;
    FFileLoggingEnabled = true;
    FRealTimeLoggingEnabled = true;
    
    // 크리티컬 섹션 생성
    FLogLock = new TCriticalSection();
    
    // 타이머 생성 (5초마다 플러시)
    FFlushTimer = new TTimer(NULL);
    FFlushTimer->Interval = 5000;
    FFlushTimer->OnTimer = OnFlushTimer;
    FFlushTimer->Enabled = false;
    
    // 로그 디렉토리 생성
    if (!TUtils::DirectoryExists(FLogPath)) {
        TUtils::CreateDirectoryRecursive(FLogPath);
    }
    
    WriteLog(TLogLevel::INFO, "Logger", "데이터 로거 생성: " + FDataPath);
}

// 소멸자
__fastcall TDataLogger::~TDataLogger() {
    Stop();
    delete FFlushTimer;
    delete FLogLock;
}

// 시작
bool TDataLogger::Start() {
    try {
        if (FIsEnabled) {
            return true;
        }
        
        InitializeLogFiles();
        
        FIsEnabled = true;
        FFlushTimer->Enabled = true;
        
        WriteLog(TLogLevel::INFO, "Logger", "데이터 로깅 시작");
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Logger", "로깅 시작 실패: " + e.Message);
        return false;
    }
}

// 중지
void TDataLogger::Stop() {
    if (!FIsEnabled) {
        return;
    }
    
    FIsEnabled = false;
    FFlushTimer->Enabled = false;
    
    // 버퍼 플러시
    FlushLogBuffer();
    
    // 파일 스트림 닫기
    if (FLogFile) {
        FLogFile->close();
        FLogFile.reset();
    }
    
    if (FHogaFile) {
        FHogaFile->close();
        FHogaFile.reset();
    }
    
    if (FJeobsuFile) {
        FJeobsuFile->close();
        FJeobsuFile.reset();
    }
    
    if (FOrderFile) {
        FOrderFile->close();
        FOrderFile.reset();
    }
    
    WriteLog(TLogLevel::INFO, "Logger", "데이터 로깅 중지");
}

// 일시정지
void TDataLogger::Pause() {
    FFlushTimer->Enabled = false;
    WriteLog(TLogLevel::INFO, "Logger", "로깅 일시정지");
}

// 재개
void TDataLogger::Resume() {
    FFlushTimer->Enabled = true;
    WriteLog(TLogLevel::INFO, "Logger", "로깅 재개");
}

// 로그 파일 초기화
void TDataLogger::InitializeLogFiles() {
    try {
        AnsiString today = FormatDateTime("yyyymmdd", Now());
        
        // 메인 로그 파일
        if (FFileLoggingEnabled) {
            AnsiString logFileName = TPath::Combine(FLogPath, "trading_" + today + ".log");
            FLogFile = std::make_unique<std::ofstream>(logFileName.c_str(), std::ios::app);
        }
        
        // 실시간 데이터 파일들
        if (FRealTimeLoggingEnabled) {
            AnsiString dataDir = TPath::Combine(FDataPath, today);
            if (!TUtils::DirectoryExists(dataDir)) {
                TUtils::CreateDirectoryRecursive(dataDir);
            }
            
            AnsiString hogaFileName = TPath::Combine(dataDir, "hoga_" + today + ".csv");
            AnsiString jeobsuFileName = TPath::Combine(dataDir, "jeobsu_" + today + ".csv");
            AnsiString orderFileName = TPath::Combine(dataDir, "orders_" + today + ".csv");
            
            FHogaFile = std::make_unique<std::ofstream>(hogaFileName.c_str(), std::ios::app);
            FJeobsuFile = std::make_unique<std::ofstream>(jeobsuFileName.c_str(), std::ios::app);
            FOrderFile = std::make_unique<std::ofstream>(orderFileName.c_str(), std::ios::app);
            
            // CSV 헤더 작성 (파일이 비어있는 경우)
            if (FHogaFile && FHogaFile->tellp() == 0) {
                WriteToFile(*FHogaFile, "Time,Code,BuyPrice1,BuyQty1,SellPrice1,SellQty1,TotalBuy,TotalSell");
            }
            
            if (FJeobsuFile && FJeobsuFile->tellp() == 0) {
                WriteToFile(*FJeobsuFile, "Time,Code,Price,Quantity,Volume,Change,TradeType");
            }
            
            if (FOrderFile && FOrderFile->tellp() == 0) {
                WriteToFile(*FOrderFile, "Time,OrderId,Code,Type,State,Quantity,Price,FilledQty,AvgPrice");
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Logger", "로그 파일 초기화 실패: " + e.Message);
    }
}

// 로그 기록
void TDataLogger::WriteLog(TLogLevel level, const AnsiString& category, const AnsiString& message) {
    if (!FIsEnabled || level < FMinLogLevel) {
        return;
    }
    
    try {
        auto log = std::make_shared<TLogMessage>(level, category, message);
        
        {
            TLockGuard lock(FLogLock);
            FLogBuffer.push_back(log);
        }
        
        // 디버그 출력
        #ifdef _DEBUG
        AnsiString debugMsg = FormatLogMessage(*log);
        OutputDebugStringA(debugMsg.c_str());
        #endif
        
        // 즉시 출력 (Critical 레벨)
        if (level == TLogLevel::CRITICAL) {
            FlushLogBuffer();
        }
    }
    catch (...) {
        // 로깅 실패는 무시
    }
}

// 간단한 로그 기록
void TDataLogger::WriteLog(TLogLevel level, const AnsiString& message) {
    WriteLog(level, "System", message);
}

// 호가 데이터 로깅
void TDataLogger::LogHoga(const THogaData& hoga) {
    if (!FIsEnabled || !FRealTimeLoggingEnabled || !FHogaFile) {
        return;
    }
    
    try {
        AnsiString csvLine = AnsiString().sprintf("%s,%s,%.0f,%d,%.0f,%d,%d,%d",
            FormatDateTime("hh:nn:ss.zzz", hoga.timestamp).c_str(),
            hoga.code.c_str(),
            hoga.buyPrices[0], hoga.buyQuantities[0],
            hoga.sellPrices[0], hoga.sellQuantities[0],
            hoga.totalBuyQuantity, hoga.totalSellQuantity);
        
        WriteToFile(*FHogaFile, csvLine);
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Logger", "호가 로깅 실패: " + e.Message);
    }
}

// 체결 데이터 로깅
void TDataLogger::LogJeobsu(const TJeobsuData& jeobsu) {
    if (!FIsEnabled || !FRealTimeLoggingEnabled || !FJeobsuFile) {
        return;
    }
    
    try {
        AnsiString tradeTypeStr = (jeobsu.trade_type == TTradeType::BUY) ? "BUY" : "SELL";
        
        AnsiString csvLine = AnsiString().sprintf("%s,%s,%.0f,%d,%d,%.2f,%s",
            FormatDateTime("hh:nn:ss.zzz", jeobsu.timestamp).c_str(),
            jeobsu.code.c_str(),
            jeobsu.price, jeobsu.quantity, jeobsu.volume,
            jeobsu.change, tradeTypeStr.c_str());
        
        WriteToFile(*FJeobsuFile, csvLine);
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Logger", "체결 로깅 실패: " + e.Message);
    }
}

// 주문 데이터 로깅
void TDataLogger::LogOrder(const TOrderInfo& order) {
    if (!FIsEnabled || !FRealTimeLoggingEnabled || !FOrderFile) {
        return;
    }
    
    try {
        AnsiString tradeTypeStr = (order.trade_type == TTradeType::BUY) ? "BUY" : "SELL";
        AnsiString stateStr = TUtils::OrderStateToString(order.state);
        
        AnsiString csvLine = AnsiString().sprintf("%s,%s,%s,%s,%s,%d,%.0f,%d,%.0f",
            FormatDateTime("hh:nn:ss.zzz", Now()).c_str(),
            order.order_id.c_str(), order.code.c_str(),
            tradeTypeStr.c_str(), stateStr.c_str(),
            order.quantity, order.price,
            order.filled_quantity, order.average_price);
        
        WriteToFile(*FOrderFile, csvLine);
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Logger", "주문 로깅 실패: " + e.Message);
    }
}

// 포지션 로깅
void TDataLogger::LogPosition(const TPositionInfo& position) {
    WriteLog(TLogLevel::INFO, "Position", 
        AnsiString().sprintf("%s: 수량=%d, 평균가=%.0f, 현재가=%.0f, 손익=%.2f",
            position.code.c_str(), position.quantity, 
            position.average_price, position.current_price, 
            position.unrealized_pnl));
}

// 분봉 로깅
void TDataLogger::LogBar(const TBarData& bar) {
    WriteLog(TLogLevel::DEBUG, "Bar", 
        AnsiString().sprintf("%s [%s]: O=%.0f H=%.0f L=%.0f C=%.0f V=%d",
            bar.code.c_str(), 
            FormatDateTime("hh:nn", bar.time).c_str(),
            bar.open, bar.high, bar.low, bar.close, bar.volume));
}

// 로그 버퍼 플러시
void TDataLogger::FlushLogBuffer() {
    if (!FFileLoggingEnabled || !FLogFile) {
        return;
    }
    
    try {
        TLockGuard lock(FLogLock);
        
        for (const auto& log : FLogBuffer) {
            AnsiString formattedLog = FormatLogMessage(*log);
            WriteToFile(*FLogFile, formattedLog);
        }
        
        FLogBuffer.clear();
        
        if (FLogFile) {
            FLogFile->flush();
        }
    }
    catch (const Exception& e) {
        // 플러시 실패는 무시 (무한 루프 방지)
    }
}

// 로그 메시지 포맷팅
AnsiString TDataLogger::FormatLogMessage(const TLogMessage& log) {
    AnsiString levelStr;
    switch (log.level) {
        case TLogLevel::DEBUG: levelStr = "DEBUG"; break;
        case TLogLevel::INFO: levelStr = "INFO"; break;
        case TLogLevel::WARNING: levelStr = "WARN"; break;
        case TLogLevel::ERROR: levelStr = "ERROR"; break;
        case TLogLevel::CRITICAL: levelStr = "CRIT"; break;
        default: levelStr = "UNKNOWN"; break;
    }
    
    return AnsiString().sprintf("[%s] %s [%s] %s",
        FormatDateTime("yyyy-mm-dd hh:nn:ss.zzz", log.timestamp).c_str(),
        levelStr.c_str(),
        log.category.c_str(),
        log.message.c_str());
}

// 파일에 쓰기
void TDataLogger::WriteToFile(std::ofstream& file, const AnsiString& message) {
    if (file.is_open()) {
        file << message.c_str() << std::endl;
    }
}

// 데이터 경로 설정
void TDataLogger::SetDataPath(const AnsiString& path) {
    FDataPath = path;
    FLogPath = TPath::Combine(FDataPath, "logs");
    
    if (!TUtils::DirectoryExists(FLogPath)) {
        TUtils::CreateDirectoryRecursive(FLogPath);
    }
}

// 로그 조회
std::vector<std::shared_ptr<TLogMessage>> TDataLogger::GetLogs(TLogLevel minLevel) {
    TLockGuard lock(FLogLock);
    
    std::vector<std::shared_ptr<TLogMessage>> result;
    for (const auto& log : FLogBuffer) {
        if (log->level >= minLevel) {
            result.push_back(log);
        }
    }
    
    return result;
}

// 최근 로그 조회
std::vector<std::shared_ptr<TLogMessage>> TDataLogger::GetRecentLogs(int count) {
    TLockGuard lock(FLogLock);
    
    std::vector<std::shared_ptr<TLogMessage>> result;
    int start = std::max(0, (int)FLogBuffer.size() - count);
    
    for (int i = start; i < FLogBuffer.size(); i++) {
        result.push_back(FLogBuffer[i]);
    }
    
    return result;
}

// 로그 클리어
void TDataLogger::ClearLogs() {
    TLockGuard lock(FLogLock);
    FLogBuffer.clear();
}

// 모든 파일 플러시
void TDataLogger::FlushAll() {
    FlushLogBuffer();
    
    if (FHogaFile) FHogaFile->flush();
    if (FJeobsuFile) FJeobsuFile->flush();
    if (FOrderFile) FOrderFile->flush();
}

// 로그 파일 로테이션
void TDataLogger::RotateLogFiles() {
    // 새로운 날짜의 파일로 교체
    Stop();
    Start();
}

// 로그 파일 크기 조회
AnsiString TDataLogger::GetLogFileSize() {
    AnsiString result = "로그 파일 크기:\n";
    
    try {
        AnsiString today = FormatDateTime("yyyymmdd", Now());
        AnsiString logFileName = TPath::Combine(FLogPath, "trading_" + today + ".log");
        
        if (TUtils::FileExists(logFileName)) {
            __int64 size = TUtils::GetFileSize(logFileName);
            result += AnsiString().sprintf("메인 로그: %s\n", TUtils::FormatFileSize(size).c_str());
        }
    }
    catch (const Exception& e) {
        result += "크기 조회 실패: " + e.Message;
    }
    
    return result;
}

// 타이머 이벤트
void __fastcall TDataLogger::OnFlushTimer(TObject* Sender) {
    if (FIsEnabled) {
        FlushLogBuffer();
    }
}