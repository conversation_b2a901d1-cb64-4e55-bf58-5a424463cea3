#ifndef TXINGAPIADAPTER_H
#define TXINGAPIADAPTER_H

#include "TLSXingAPI.h"
#include "TXingAPIWrapper.h"

/**
 * TXingAPIAdapter
 * TLSXingAPI를 기존 TXingAPIWrapper 인터페이스로 어댑터하는 클래스
 * 기존 코드 호환성을 유지하면서 새로운 최적화된 구현을 사용
 */
class TXingAPIAdapter : public TXingAPIWrapper {
private:
    std::unique_ptr<TLSXingAPI> FLSXingAPI;
    
    // 이벤트 핸들러들 - TLSXingAPI 이벤트를 기존 인터페이스로 변환
    void __fastcall OnLSConnected(TObject* Sender, bool connected);
    void __fastcall OnLSDisconnected(TObject* Sender, bool connected);
    void __fastcall OnLSLoginResult(TObject* Sender, bool success, const AnsiString& message);
    void __fastcall OnLSOptionHoga(const TOptionHoga& data);
    void __fastcall OnLSOptionExecution(const TOptionExecution& data);
    void __fastcall OnLSOrderResult(const TOrderResult& data);
    void __fastcall OnLSQueryReceived(const AnsiString& trCode, int blockCount);
    void __fastcall OnLSRealDataReceived(const AnsiString& trCode);
    
    // 데이터 변환 메서드들
    THogaData ConvertToHogaData(const TOptionHoga& lsHoga);
    TJeobsuData ConvertToJeobsuData(const TOptionExecution& lsExecution);
    
public:
    // 생성자/소멸자
    __fastcall TXingAPIAdapter();
    __fastcall ~TXingAPIAdapter();
    
    // TXingAPIWrapper 인터페이스 구현
    virtual bool Connect(bool isReal = false) override;
    virtual void Disconnect() override;
    virtual bool Login(const AnsiString& userId, const AnsiString& password, 
                      const AnsiString& certPassword = "") override;
    virtual void Logout() override;
    
    virtual bool IsConnected() const override { return FLSXingAPI->IsConnected(); }
    virtual bool IsLoggedIn() const override { return FLSXingAPI->IsLoggedIn(); }
    virtual bool IsRealServer() const override;
    
    virtual std::vector<AnsiString> GetAccountList() override;
    virtual bool SetAccount(const AnsiString& accountNumber) override;
    virtual AnsiString GetCurrentAccount() const override;
    
    // 옵션 관련 메서드
    virtual std::vector<AnsiString> GetOptionCodes() override;
    virtual bool SubscribeOptionHoga(const AnsiString& code) override;
    virtual bool SubscribeOptionJeobsu(const AnsiString& code) override;
    virtual bool UnsubscribeRealData(const AnsiString& code) override;
    
    // 주문 관련 메서드
    virtual AnsiString SendOrder(const AnsiString& code, TTradeType tradeType, 
                                int quantity, double price, 
                                TOrderType orderType = TOrderType::LIMIT) override;
    virtual bool CancelOrder(const AnsiString& orderId) override;
    virtual bool ModifyOrder(const AnsiString& orderId, double newPrice, int newQuantity = 0) override;
    
    // 조회 메서드
    virtual std::shared_ptr<TAccountInfo> GetAccountInfo() override;
    virtual TPositionMap GetPositions() override;
    virtual std::vector<TOrderInfo> GetPendingOrders() override;
    
    // 설정 메서드
    virtual void SetAutoReconnect(bool enable) override;
    virtual void SetConfigPath(const AnsiString& path) override;
    
    // TLSXingAPI 인스턴스 직접 접근 (고급 사용자용)
    TLSXingAPI* GetLSXingAPI() const { return FLSXingAPI.get(); }
    
    // 추가 최적화된 메서드들
    AnsiString RequestOptionCurrentPrice(const AnsiString& code);
    AnsiString RequestOptionChartData(const AnsiString& code, const AnsiString& period, int count);
    bool SubscribeOptionCurrentPrice(const AnsiString& code);
    
    // 진단 및 모니터링
    TConnectionState GetDetailedConnectionState() const;
    AnsiString GetDetailedStateDescription() const;
    TDateTime GetLastConnectTime() const;
    AnsiString GetLastError() const;
};

#endif // TXINGAPIADAPTER_H