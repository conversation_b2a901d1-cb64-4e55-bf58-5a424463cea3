#include "TRealTimeDataThread.h"
#include "../api/TXingAPIWrapper.h"
#include "../data/TDataLogger.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>

// TRealTimeDataThread 구현

// 생성자
__fastcall TRealTimeDataThread::TRealTimeDataThread(TXingAPIWrapper* xingAPI, TDataLogger* dataLogger)
    : TThread(true), FXingAPI(xingAPI), FDataLogger(dataLogger) {
    
    FIsRunning = false;
    FIsPaused = false;
    FProcessedMessageCount = 0;
    FErrorCount = 0;
    FProcessInterval = 10;  // 10ms
    FMaxQueueSize = 10000;
    FLogProcessing = false;
    
    // 동기화 객체 생성
    FQueueLock = new TCriticalSection();
    FPauseEvent = new TEvent(NULL, true, true, "");    // Manual reset, signaled
    FResumeEvent = new TEvent(NULL, true, false, "");  // Manual reset, non-signaled
    FShutdownEvent = new TEvent(NULL, true, false, ""); // Manual reset, non-signaled
    
    FreeOnTerminate = false;
}

// 소멸자
__fastcall TRealTimeDataThread::~TRealTimeDataThread() {
    if (!Terminated) {
        Stop();
    }
    
    ClearQueue();
    
    delete FQueueLock;
    delete FPauseEvent;
    delete FResumeEvent;
    delete FShutdownEvent;
}

// 스레드 실행
void __fastcall TRealTimeDataThread::Execute() {
    try {
        FIsRunning = true;
        
        while (!Terminated) {
            // 종료 신호 체크
            if (FShutdownEvent->WaitFor(0) == wrSignaled) {
                break;
            }
            
            // 일시정지 체크
            if (FIsPaused) {
                FResumeEvent->WaitFor(INFINITE);
                if (Terminated) break;
            }
            
            // 데이터 큐 처리
            ProcessDataQueue();
            
            // 처리 간격 대기
            Sleep(FProcessInterval);
        }
    }
    catch (const Exception& e) {
        FErrorCount++;
        LogMessage(TLogLevel::ERROR, "스레드 실행 중 오류: " + e.Message);
        
        FCurrentError = e.Message;
        Synchronize(SyncThreadError);
    }
    
    FIsRunning = false;
}

// 스레드 시작
void TRealTimeDataThread::Start() {
    if (!FIsRunning && Suspended) {
        FShutdownEvent->ResetEvent();
        Resume();
    }
}

// 스레드 중지
void TRealTimeDataThread::Stop() {
    if (FIsRunning) {
        FShutdownEvent->SetEvent();
        
        // 일시정지 상태면 재개
        if (FIsPaused) {
            Resume();
        }
        
        // 스레드 종료 대기 (최대 5초)
        Terminate();
        WaitFor();
    }
}

// 일시정지
void TRealTimeDataThread::Pause() {
    if (FIsRunning && !FIsPaused) {
        FIsPaused = true;
        FPauseEvent->ResetEvent();
        FResumeEvent->ResetEvent();
    }
}

// 재개
void TRealTimeDataThread::Resume() {
    if (FIsPaused) {
        FIsPaused = false;
        FPauseEvent->SetEvent();
        FResumeEvent->SetEvent();
    }
}

// 데이터 큐 처리
void TRealTimeDataThread::ProcessDataQueue() {
    try {
        std::shared_ptr<TDataMessage> message;
        
        // 큐에서 메시지 가져오기
        {
            TLockGuard lock(FQueueLock);
            if (FDataQueue.empty()) {
                return;
            }
            
            message = FDataQueue.front();
            FDataQueue.pop();
        }
        
        if (!message) return;
        
        // 메시지 타입별 처리
        switch (message->type) {
            case TDataMessageType::HOGA_DATA:
                ProcessHogaMessage(message);
                break;
                
            case TDataMessageType::JEOBSU_DATA:
                ProcessJeobsuMessage(message);
                break;
                
            case TDataMessageType::ORDER_STATUS:
                ProcessOrderStatusMessage(message);
                break;
                
            case TDataMessageType::ACCOUNT_UPDATE:
                ProcessAccountUpdateMessage(message);
                break;
                
            case TDataMessageType::ERROR_MESSAGE:
                ProcessErrorMessage(message);
                break;
                
            default:
                LogMessage(TLogLevel::WARNING, "알 수 없는 메시지 타입");
                break;
        }
        
        FProcessedMessageCount++;
        FLastProcessTime = Now();
        
        // UI 업데이트 (동기화)
        FCurrentMessage = message;
        Synchronize(SyncDataProcessed);
        
        UpdateStatistics();
    }
    catch (const Exception& e) {
        FErrorCount++;
        LogMessage(TLogLevel::ERROR, "메시지 처리 중 오류: " + e.Message);
    }
}

// 호가 메시지 처리
void TRealTimeDataThread::ProcessHogaMessage(const std::shared_ptr<TDataMessage>& message) {
    try {
        // Variant에서 THogaData 추출
        THogaData hogaData;
        // 실제 구현에서는 message->data에서 호가 데이터를 파싱
        hogaData.code = message->code;
        hogaData.timestamp = message->timestamp;
        
        // 데이터 로깅
        if (FDataLogger) {
            FDataLogger->LogHogaData(hogaData);
        }
        
        if (FLogProcessing) {
            LogMessage(TLogLevel::DEBUG, 
                AnsiString().sprintf("호가 데이터 처리: %s", message->code.c_str()));
        }
    }
    catch (const Exception& e) {
        LogMessage(TLogLevel::ERROR, "호가 데이터 처리 오류: " + e.Message);
    }
}

// 체결 메시지 처리
void TRealTimeDataThread::ProcessJeobsuMessage(const std::shared_ptr<TDataMessage>& message) {
    try {
        TJeobsuData jeobsuData;
        jeobsuData.code = message->code;
        jeobsuData.timestamp = message->timestamp;
        
        // 데이터 로깅
        if (FDataLogger) {
            FDataLogger->LogJeobsuData(jeobsuData);
        }
        
        if (FLogProcessing) {
            LogMessage(TLogLevel::DEBUG, 
                AnsiString().sprintf("체결 데이터 처리: %s", message->code.c_str()));
        }
    }
    catch (const Exception& e) {
        LogMessage(TLogLevel::ERROR, "체결 데이터 처리 오류: " + e.Message);
    }
}

// 주문 상태 메시지 처리
void TRealTimeDataThread::ProcessOrderStatusMessage(const std::shared_ptr<TDataMessage>& message) {
    try {
        if (FLogProcessing) {
            LogMessage(TLogLevel::DEBUG, 
                AnsiString().sprintf("주문 상태 업데이트: %s", message->code.c_str()));
        }
    }
    catch (const Exception& e) {
        LogMessage(TLogLevel::ERROR, "주문 상태 처리 오류: " + e.Message);
    }
}

// 계좌 업데이트 메시지 처리
void TRealTimeDataThread::ProcessAccountUpdateMessage(const std::shared_ptr<TDataMessage>& message) {
    try {
        if (FLogProcessing) {
            LogMessage(TLogLevel::DEBUG, "계좌 정보 업데이트");
        }
    }
    catch (const Exception& e) {
        LogMessage(TLogLevel::ERROR, "계좌 업데이트 처리 오류: " + e.Message);
    }
}

// 에러 메시지 처리
void TRealTimeDataThread::ProcessErrorMessage(const std::shared_ptr<TDataMessage>& message) {
    try {
        LogMessage(TLogLevel::ERROR, "API 에러: " + message->code);
    }
    catch (const Exception& e) {
        LogMessage(TLogLevel::ERROR, "에러 메시지 처리 오류: " + e.Message);
    }
}

// 메시지 추가
void TRealTimeDataThread::AddMessage(const std::shared_ptr<TDataMessage>& message) {
    TLockGuard lock(FQueueLock);
    
    if (FDataQueue.size() >= FMaxQueueSize) {
        // 큐가 가득 찬 경우 가장 오래된 메시지 제거
        FDataQueue.pop();
        
        if (FOnQueueFull) {
            Synchronize(SyncQueueFull);
        }
    }
    
    FDataQueue.push(message);
}

// 호가 데이터 추가
void TRealTimeDataThread::AddHogaData(const AnsiString& code, const THogaData& hoga) {
    auto message = std::make_shared<TDataMessage>();
    message->type = TDataMessageType::HOGA_DATA;
    message->code = code;
    message->timestamp = Now();
    // message->data에 hoga 데이터 설정 (Variant 변환 필요)
    
    AddMessage(message);
}

// 체결 데이터 추가
void TRealTimeDataThread::AddJeobsuData(const AnsiString& code, const TJeobsuData& jeobsu) {
    auto message = std::make_shared<TDataMessage>();
    message->type = TDataMessageType::JEOBSU_DATA;
    message->code = code;
    message->timestamp = Now();
    
    AddMessage(message);
}

// 주문 상태 추가
void TRealTimeDataThread::AddOrderStatus(const AnsiString& orderId, const AnsiString& status) {
    auto message = std::make_shared<TDataMessage>();
    message->type = TDataMessageType::ORDER_STATUS;
    message->code = orderId;
    message->timestamp = Now();
    
    AddMessage(message);
}

// 계좌 업데이트 추가
void TRealTimeDataThread::AddAccountUpdate() {
    auto message = std::make_shared<TDataMessage>();
    message->type = TDataMessageType::ACCOUNT_UPDATE;
    message->timestamp = Now();
    
    AddMessage(message);
}

// 에러 메시지 추가
void TRealTimeDataThread::AddErrorMessage(const AnsiString& error) {
    auto message = std::make_shared<TDataMessage>();
    message->type = TDataMessageType::ERROR_MESSAGE;
    message->code = error;
    message->timestamp = Now();
    
    AddMessage(message);
}

// 큐 크기 조회
int TRealTimeDataThread::GetQueueSize() {
    TLockGuard lock(FQueueLock);
    return FDataQueue.size();
}

// 큐 정리
void TRealTimeDataThread::ClearQueue() {
    TLockGuard lock(FQueueLock);
    while (!FDataQueue.empty()) {
        FDataQueue.pop();
    }
}

// 큐 가득참 여부
bool TRealTimeDataThread::IsQueueFull() {
    TLockGuard lock(FQueueLock);
    return FDataQueue.size() >= FMaxQueueSize;
}

// 처리율 계산
double TRealTimeDataThread::GetProcessingRate() {
    if (FProcessedMessageCount == 0) return 0.0;
    
    TDateTime elapsed = Now() - FLastProcessTime;
    double seconds = elapsed * 24 * 60 * 60;  // 초 단위 변환
    
    return seconds > 0 ? FProcessedMessageCount / seconds : 0.0;
}

// 통계 업데이트
void TRealTimeDataThread::UpdateStatistics() {
    // 통계 정보 업데이트 (필요시 구현)
}

// 로그 메시지
void TRealTimeDataThread::LogMessage(TLogLevel level, const AnsiString& message) {
    if (FDataLogger) {
        FDataLogger->WriteLog(level, "RealTimeDataThread", message);
    }
}

// 동기화 메서드들
void __fastcall TRealTimeDataThread::SyncDataProcessed() {
    if (FOnDataProcessed) {
        FOnDataProcessed(this);
    }
}

void __fastcall TRealTimeDataThread::SyncQueueFull() {
    if (FOnQueueFull) {
        FOnQueueFull(this);
    }
}

void __fastcall TRealTimeDataThread::SyncThreadError() {
    if (FOnThreadError) {
        FOnThreadError(this);
    }
}

// TOrderProcessThread 구현

// 생성자
__fastcall TOrderProcessThread::TOrderProcessThread(TXingAPIWrapper* xingAPI)
    : TThread(true), FXingAPI(xingAPI) {
    
    FIsRunning = false;
    FProcessInterval = 50;  // 50ms
    
    FOrderLock = new TCriticalSection();
    FShutdownEvent = new TEvent(NULL, true, false, "");
    
    FreeOnTerminate = false;
}

// 소멸자
__fastcall TOrderProcessThread::~TOrderProcessThread() {
    if (!Terminated) {
        Stop();
    }
    
    ClearQueue();
    
    delete FOrderLock;
    delete FShutdownEvent;
}

// 스레드 실행
void __fastcall TOrderProcessThread::Execute() {
    try {
        FIsRunning = true;
        
        while (!Terminated) {
            if (FShutdownEvent->WaitFor(0) == wrSignaled) {
                break;
            }
            
            ProcessOrderQueue();
            Sleep(FProcessInterval);
        }
    }
    catch (const Exception& e) {
        // 에러 처리
    }
    
    FIsRunning = false;
}

// 주문 큐 처리
void TOrderProcessThread::ProcessOrderQueue() {
    std::shared_ptr<TOrderInfo> order;
    
    {
        TLockGuard lock(FOrderLock);
        if (FOrderQueue.empty()) {
            return;
        }
        
        order = FOrderQueue.front();
        FOrderQueue.pop();
    }
    
    if (order && FXingAPI) {
        ProcessOrder(order);
    }
}

// 주문 처리
void TOrderProcessThread::ProcessOrder(const std::shared_ptr<TOrderInfo>& order) {
    try {
        // XingAPI를 통한 주문 전송
        AnsiString orderId = FXingAPI->SendOrder(
            order->code, 
            order->trade_type, 
            order->quantity, 
            order->price, 
            order->order_type
        );
        
        if (!orderId.IsEmpty()) {
            order->order_id = orderId;
            order->state = TOrderState::EXECUTED;
            
            FCurrentOrder = order;
            Synchronize(SyncOrderProcessed);
        }
    }
    catch (const Exception& e) {
        // 에러 처리
    }
}

// 스레드 시작
void TOrderProcessThread::Start() {
    if (!FIsRunning && Suspended) {
        FShutdownEvent->ResetEvent();
        Resume();
    }
}

// 스레드 중지
void TOrderProcessThread::Stop() {
    if (FIsRunning) {
        FShutdownEvent->SetEvent();
        Terminate();
        WaitFor();
    }
}

// 주문 추가
void TOrderProcessThread::AddOrder(const std::shared_ptr<TOrderInfo>& order) {
    TLockGuard lock(FOrderLock);
    FOrderQueue.push(order);
}

// 큐 크기 조회
int TOrderProcessThread::GetQueueSize() {
    TLockGuard lock(FOrderLock);
    return FOrderQueue.size();
}

// 큐 정리
void TOrderProcessThread::ClearQueue() {
    TLockGuard lock(FOrderLock);
    while (!FOrderQueue.empty()) {
        FOrderQueue.pop();
    }
}

// 동기화 메서드
void __fastcall TOrderProcessThread::SyncOrderProcessed() {
    if (FOnOrderProcessed) {
        FOnOrderProcessed(this);
    }
}

// TUIUpdateThread 구현

// 생성자
__fastcall TUIUpdateThread::TUIUpdateThread() : TThread(true) {
    FIsRunning = false;
    FUpdateInterval = 1000;  // 1초
    
    FUpdateLock = new TCriticalSection();
    FShutdownEvent = new TEvent(NULL, true, false, "");
    
    FreeOnTerminate = false;
}

// 소멸자
__fastcall TUIUpdateThread::~TUIUpdateThread() {
    if (!Terminated) {
        Stop();
    }
    
    delete FUpdateLock;
    delete FShutdownEvent;
}

// 스레드 실행
void __fastcall TUIUpdateThread::Execute() {
    try {
        FIsRunning = true;
        
        while (!Terminated) {
            if (FShutdownEvent->WaitFor(0) == wrSignaled) {
                break;
            }
            
            ProcessUpdates();
            Sleep(FUpdateInterval);
        }
    }
    catch (const Exception& e) {
        // 에러 처리
    }
    
    FIsRunning = false;
}

// 업데이트 처리
void TUIUpdateThread::ProcessUpdates() {
    AnsiString updateType;
    
    {
        TLockGuard lock(FUpdateLock);
        if (FUpdateQueue.empty()) {
            return;
        }
        
        updateType = FUpdateQueue.front();
        FUpdateQueue.pop();
    }
    
    if (!updateType.IsEmpty()) {
        FCurrentUpdate = updateType;
        Synchronize(SyncUIUpdate);
    }
}

// 스레드 시작
void TUIUpdateThread::Start() {
    if (!FIsRunning && Suspended) {
        FShutdownEvent->ResetEvent();
        Resume();
    }
}

// 스레드 중지
void TUIUpdateThread::Stop() {
    if (FIsRunning) {
        FShutdownEvent->SetEvent();
        Terminate();
        WaitFor();
    }
}

// 업데이트 요청
void TUIUpdateThread::RequestUpdate(const AnsiString& updateType) {
    TLockGuard lock(FUpdateLock);
    FUpdateQueue.push(updateType);
}

// 동기화 메서드
void __fastcall TUIUpdateThread::SyncUIUpdate() {
    if (FOnUIUpdateReady) {
        FOnUIUpdateReady(this);
    }
}