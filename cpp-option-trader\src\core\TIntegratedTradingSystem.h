#ifndef TINTEGRATEDTRADINGSYSTEM_H
#define TINTEGRATEDTRADINGSYSTEM_H

#include <vcl.h>
#include <System.hpp>
#include <memory>
#include <vector>
#include "../data/DataTypes.h"

// 전방 선언
class TOrderManager;
class TTrader;
class TOptionStrategy;
class TXingAPIWrapper;
class TDataLogger;
class TRealTimeDataThread;

/**
 * TIntegratedTradingSystem
 * 전체 거래 시스템을 통합 관리하는 메인 클래스
 * Python의 IntegratedTradingSystem 클래스를 C++Builder로 포팅
 */
class TIntegratedTradingSystem {
private:
    // 핵심 컴포넌트들
    std::unique_ptr<TOrderManager> FOrderManager;
    std::unique_ptr<TTrader> FTrader;
    std::unique_ptr<TOptionStrategy> FStrategy;
    std::unique_ptr<TXingAPIWrapper> FXingAPI;
    std::unique_ptr<TDataLogger> FDataLogger;
    std::unique_ptr<TRealTimeDataThread> FDataThread;
    
    // 시스템 상태
    TTradingMode FTradingMode;
    bool FIsRunning;
    bool FIsInitialized;
    TDateTime FStartTime;
    TDateTime FEndTime;
    
    // 설정
    TStrategyParams FStrategyParams;
    AnsiString FConfigPath;
    AnsiString FDataPath;
    
    // 계좌 정보
    std::shared_ptr<TAccountInfo> FAccountInfo;
    
    // 이벤트 핸들러
    void __fastcall OnOrderStateChanged(TObject* Sender, const TOrderInfo& order);
    void __fastcall OnPositionUpdated(TObject* Sender, const TPositionInfo& position);
    void __fastcall OnHogaReceived(TObject* Sender, const THogaData& hoga);
    void __fastcall OnJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu);
    void __fastcall OnError(TObject* Sender, const AnsiString& error);
    
    // 내부 메서드
    bool LoadConfiguration();
    bool SaveConfiguration();
    void InitializeComponents();
    void CleanupComponents();
    void UpdateAccountInfo();
    
public:
    // 생성자/소멸자
    __fastcall TIntegratedTradingSystem();
    __fastcall ~TIntegratedTradingSystem();
    
    // 초기화 및 종료
    bool Initialize(const AnsiString& configPath = "");
    void Shutdown();
    
    // 시스템 제어
    bool StartTrading(TTradingMode mode = TTradingMode::REAL_TIME);
    void StopTrading();
    void PauseTrading();
    void ResumeTrading();
    
    // 모드 설정
    void SetTradingMode(TTradingMode mode);
    TTradingMode GetTradingMode() const { return FTradingMode; }
    
    // 상태 조회
    bool IsRunning() const { return FIsRunning; }
    bool IsInitialized() const { return FIsInitialized; }
    TDateTime GetStartTime() const { return FStartTime; }
    TDateTime GetRunTime() const;
    
    // 설정 관리
    void SetStrategyParams(const TStrategyParams& params);
    TStrategyParams GetStrategyParams() const { return FStrategyParams; }
    void SetDataPath(const AnsiString& path) { FDataPath = path; }
    AnsiString GetDataPath() const { return FDataPath; }
    
    // 계좌 정보
    std::shared_ptr<TAccountInfo> GetAccountInfo() const { return FAccountInfo; }
    
    // 컴포넌트 접근
    TOrderManager* GetOrderManager() const { return FOrderManager.get(); }
    TTrader* GetTrader() const { return FTrader.get(); }
    TOptionStrategy* GetStrategy() const { return FStrategy.get(); }
    TXingAPIWrapper* GetXingAPI() const { return FXingAPI.get(); }
    TDataLogger* GetDataLogger() const { return FDataLogger.get(); }
    
    // 수동 거래
    bool PlaceOrder(const AnsiString& code, TTradeType tradeType, 
                   int quantity, double price = 0.0, TOrderType orderType = TOrderType::LIMIT);
    bool CancelOrder(const AnsiString& orderId);
    bool ModifyOrder(const AnsiString& orderId, double newPrice);
    
    // 포지션 관리
    std::vector<std::shared_ptr<TPositionInfo>> GetPositions();
    std::shared_ptr<TPositionInfo> GetPosition(const AnsiString& code);
    bool ClosePosition(const AnsiString& code);
    bool CloseAllPositions();
    
    // 주문 관리
    std::vector<std::shared_ptr<TOrderInfo>> GetOrders();
    std::shared_ptr<TOrderInfo> GetOrder(const AnsiString& orderId);
    std::vector<std::shared_ptr<TOrderInfo>> GetActiveOrders();
    
    // 통계 정보
    double GetTotalProfitLoss();
    double GetTotalProfitRate();
    double GetTotalAsset();
    int GetTotalTradeCount();
    int GetWinRate();
    
    // 시뮬레이션 모드 (리플레이 기능)
    bool LoadReplayData(const AnsiString& dataFile);
    void SetReplaySpeed(double speed);
    void SeekReplayTime(TDateTime time);
    
    // 로깅 및 모니터링
    void WriteLog(TLogLevel level, const AnsiString& category, const AnsiString& message);
    std::vector<std::shared_ptr<TLogMessage>> GetLogs(TLogLevel minLevel = TLogLevel::INFO);
    
    // 이벤트 선언 (C++Builder 이벤트 시스템)
    __property TNotifyEvent OnSystemStarted = { read = FOnSystemStarted, write = FOnSystemStarted };
    __property TNotifyEvent OnSystemStopped = { read = FOnSystemStopped, write = FOnSystemStopped };
    __property TNotifyEvent OnAccountUpdated = { read = FOnAccountUpdated, write = FOnAccountUpdated };
    
private:
    // 이벤트 필드
    TNotifyEvent FOnSystemStarted;
    TNotifyEvent FOnSystemStopped;
    TNotifyEvent FOnAccountUpdated;
};

#endif // TINTEGRATEDTRADINGSYSTEM_H