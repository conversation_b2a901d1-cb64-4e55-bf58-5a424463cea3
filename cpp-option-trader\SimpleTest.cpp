// C++Builder 11 호환성 테스트용 단순 버전
#include <vcl.h>
#pragma hdrstop

// VCL 기본 헤더만 사용
#include <System.Classes.hpp>
#include <Vcl.Controls.hpp>
#include <Vcl.StdCtrls.hpp>
#include <Vcl.Forms.hpp>

class TTestForm : public TForm
{
__published:
    TButton* Button1;
    TLabel* Label1;
    
    void __fastcall Button1Click(TObject* Sender);
    
public:
    __fastcall TTestForm(TComponent* Owner);
};

__fastcall TTestForm::TTestForm(TComponent* Owner) : TForm(Owner)
{
    Caption = L"C++Builder 11 호환성 테스트";
    Width = 400;
    Height = 300;
    
    Button1 = new TButton(this);
    Button1->Parent = this;
    Button1->Caption = L"테스트";
    Button1->Left = 50;
    Button1->Top = 50;
    Button1->OnClick = Button1Click;
    
    Label1 = new TLabel(this);
    Label1->Parent = this;
    Label1->Caption = L"준비됨";
    Label1->Left = 50;
    Label1->Top = 100;
}

void __fastcall TTestForm::Button1Click(TObject* Sender)
{
    Label1->Caption = L"C++Builder 11 호환성 확인됨!";
}

TTestForm* TestForm;

int WINAPI WinMain(HINSTANCE, HINSTANCE, LPSTR, int)
{
    try
    {
        Application->Initialize();
        Application->CreateForm(__classid(TTestForm), &TestForm);
        Application->Run();
    }
    catch (Exception &exception)
    {
        Application->ShowException(&exception);
    }
    return 0;
}