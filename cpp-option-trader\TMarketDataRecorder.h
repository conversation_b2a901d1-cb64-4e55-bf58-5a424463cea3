#ifndef TMarketDataRecorderH
#define TMarketDataRecorderH

#include <System.Classes.hpp>
#include <memory>
#include <map>
#include <fstream>
#include <mutex>
#include "DataTypes.h"
#include "TUtils.h"

class TMarketDataRecorder : public TObject
{
private:
    std::unique_ptr<TUtils> FUtils;
    AnsiString FDataFolder;
    std::map<AnsiString, std::unique_ptr<std::ofstream>> FHogaFiles;
    std::map<AnsiString, std::unique_ptr<std::ofstream>> FJeobsuFiles;
    std::mutex FFileMutex;
    bool FIsRecording;
    TDateTime FCurrentDate;
    
    AnsiString GetHogaFileName(const AnsiString& code, TDateTime date);
    AnsiString GetJeobsuFileName(const AnsiString& code, TDateTime date);
    void CreateDirectoryIfNotExists(const AnsiString& path);
    void OpenFileForCode(const AnsiString& code);
    void CloseAllFiles();
    void CheckDateChange();
    AnsiString FormatHogaData(const THogaData& hoga);
    AnsiString FormatJeobsuData(const TJeobsuData& jeobsu);

public:
    __fastcall TMarketDataRecorder();
    __fastcall ~TMarketDataRecorder();
    
    void SetDataFolder(const AnsiString& folder);
    void StartRecording();
    void StopRecording();
    void RecordHogaData(const THogaData& hoga);
    void RecordJeobsuData(const TJeobsuData& jeobsu);
    void FlushAllFiles();
    
    bool IsRecording() const { return FIsRecording; }
    AnsiString GetDataFolder() const { return FDataFolder; }
};

#endif