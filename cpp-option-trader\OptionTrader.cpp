﻿// Use precompiled header to avoid conflicts
#include "OptionTraderPCH.h"
#pragma hdrstop
#include <tchar.h>

USEFORM("MainForm.cpp", MainForm);
USEFORM("SettingsForm.cpp", SettingsForm);

int WINAPI _tWinMain(HINSTANCE, HINSTANCE, LPTSTR, int)
{
    try
    {
        Application->Initialize();
        Application->MainFormOnTaskBar = true;
        Application->Title = L"옵션 트레이더 시스템 v1.0";
        Application->CreateForm(__classid(TMainForm), &MainForm);
        Application->Run();
    }
    catch (Exception &exception)
    {
        Application->ShowException(&exception);
    }
    catch (...)
    {
        try
        {
            throw Exception("");
        }
        catch (Exception &exception)
        {
            Application->ShowException(&exception);
        }
    }
    return 0;
}