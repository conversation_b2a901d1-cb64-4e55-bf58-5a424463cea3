object TestModeForm: TTestModeForm
  Left = 0
  Top = 0
  Caption = #53580#49828#53944' '#47784#46300' '#44288#47532
  ClientHeight = 700
  ClientWidth = 1000
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  PixelsPerInch = 96
  TextHeight = 15
  object GroupBoxMode: TGroupBox
    Left = 8
    Top = 8
    Width = 200
    Height = 120
    Caption = #47784#46300' '#49440#53469
    TabOrder = 0
    object RadioButtonLive: TRadioButton
      Left = 16
      Top = 24
      Width = 80
      Height = 17
      Caption = #49892#49884#44036
      Checked = True
      TabOrder = 0
      TabStop = True
      OnClick = RadioButtonModeClick
    end
    object RadioButtonReplay: TRadioButton
      Left = 16
      Top = 47
      Width = 80
      Height = 17
      Caption = #51116#49373' '#47784#46300
      TabOrder = 1
      OnClick = RadioButtonModeClick
    end
    object RadioButtonBacktest: TRadioButton
      Left = 16
      Top = 70
      Width = 80
      Height = 17
      Caption = #48177#53580#49828#53944
      TabOrder = 2
      OnClick = RadioButtonModeClick
    end
    object RadioButtonSimulation: TRadioButton
      Left = 16
      Top = 93
      Width = 80
      Height = 17
      Caption = #49884#48036#47112#51060#49496
      TabOrder = 3
      OnClick = RadioButtonModeClick
    end
  end
  object GroupBoxData: TGroupBox
    Left = 216
    Top = 8
    Width = 300
    Height = 120
    Caption = #45936#51060#53552' '#49444#51221
    TabOrder = 1
    object LabelDataFolder: TLabel
      Left = 16
      Top = 24
      Width = 72
      Height = 15
      Caption = #45936#51060#53552' '#54532#45908
    end
    object EditDataFolder: TEdit
      Left = 16
      Top = 40
      Width = 200
      Height = 23
      TabOrder = 0
    end
    object ButtonBrowseFolder: TButton
      Left = 222
      Top = 40
      Width = 60
      Height = 23
      Caption = #52286#50500'...'
      TabOrder = 1
      OnClick = ButtonBrowseFolderClick
    end
    object CheckBoxRecordData: TCheckBox
      Left = 16
      Top = 72
      Width = 100
      Height = 17
      Caption = #45936#51060#53552' '#44592#47197
      TabOrder = 2
    end
    object CheckBoxVirtualExecution: TCheckBox
      Left = 16
      Top = 95
      Width = 100
      Height = 17
      Caption = #44032#49345' '#52404#44208
      TabOrder = 3
    end
  end
  object GroupBoxPlayback: TGroupBox
    Left = 524
    Top = 8
    Width = 250
    Height = 120
    Caption = #51116#49373' '#49444#51221
    TabOrder = 2
    object LabelStartDate: TLabel
      Left = 16
      Top = 24
      Width = 48
      Height = 15
      Caption = #49884#51089#51068
    end
    object LabelEndDate: TLabel
      Left = 130
      Top = 24
      Width = 48
      Height = 15
      Caption = #51333#47308#51068
    end
    object LabelSpeed: TLabel
      Left = 16
      Top = 72
      Width = 48
      Height = 15
      Caption = #51116#49373' '#49549#46020
    end
    object DatePickerStart: TDateTimePicker
      Left = 16
      Top = 40
      Width = 100
      Height = 23
      Date = 45658.000000000000000000
      Time = 0.500000000000000000
      TabOrder = 0
    end
    object DatePickerEnd: TDateTimePicker
      Left = 130
      Top = 40
      Width = 100
      Height = 23
      Date = 45665.000000000000000000
      Time = 0.500000000000000000
      TabOrder = 1
    end
    object EditSpeed: TEdit
      Left = 16
      Top = 88
      Width = 60
      Height = 23
      TabOrder = 2
      Text = '1.0'
    end
    object UpDownSpeed: TUpDown
      Left = 76
      Top = 88
      Width = 16
      Height = 23
      Associate = EditSpeed
      Min = 1
      Max = 100
      Position = 1
      TabOrder = 3
    end
  end
  object GroupBoxCodes: TGroupBox
    Left = 8
    Top = 136
    Width = 300
    Height = 150
    Caption = #45824#49345' '#51333#47785
    TabOrder = 3
    object ListBoxCodes: TListBox
      Left = 16
      Top = 24
      Width = 150
      Height = 89
      ItemHeight = 15
      TabOrder = 0
    end
    object EditNewCode: TEdit
      Left = 16
      Top = 119
      Width = 100
      Height = 23
      TabOrder = 1
    end
    object ButtonAddCode: TButton
      Left = 122
      Top = 119
      Width = 44
      Height = 23
      Caption = #52628#44032
      TabOrder = 2
      OnClick = ButtonAddCodeClick
    end
    object ButtonRemoveCode: TButton
      Left = 180
      Top = 40
      Width = 60
      Height = 25
      Caption = #51228#44144
      TabOrder = 3
      OnClick = ButtonRemoveCodeClick
    end
  end
  object PanelControls: TPanel
    Left = 320
    Top = 136
    Width = 300
    Height = 150
    TabOrder = 4
    object ButtonStart: TButton
      Left = 16
      Top = 16
      Width = 75
      Height = 30
      Caption = #49884#51089
      TabOrder = 0
      OnClick = ButtonStartClick
    end
    object ButtonStop: TButton
      Left = 97
      Top = 16
      Width = 75
      Height = 30
      Caption = #51473#51648
      TabOrder = 1
      OnClick = ButtonStopClick
    end
    object ButtonPause: TButton
      Left = 178
      Top = 16
      Width = 75
      Height = 30
      Caption = #51068#49884#51221#51648
      TabOrder = 2
      OnClick = ButtonPauseClick
    end
    object ButtonApplyConfig: TButton
      Left = 16
      Top = 60
      Width = 100
      Height = 30
      Caption = #49444#51221' '#51201#50857
      TabOrder = 3
      OnClick = ButtonApplyConfigClick
    end
  end
  object GroupBoxStatus: TGroupBox
    Left = 8
    Top = 296
    Width = 400
    Height = 120
    Caption = #49345#53468
    TabOrder = 5
    object LabelCurrentMode: TLabel
      Left = 16
      Top = 24
      Width = 84
      Height = 15
      Caption = #54788#51116' '#47784#46300': '#49892#49884#44036
    end
    object LabelStatus: TLabel
      Left = 16
      Top = 48
      Width = 48
      Height = 15
      Caption = #49345#53468': '#45824#44592
    end
    object LabelProgress: TLabel
      Left = 16
      Top = 96
      Width = 72
      Height = 15
      Caption = #51652#54665#47456': 0.0%'
    end
    object ProgressBarPlayback: TProgressBar
      Left = 16
      Top = 72
      Width = 300
      Height = 17
      TabOrder = 0
    end
  end
  object PageControlResults: TPageControl
    Left = 8
    Top = 422
    Width = 984
    Height = 270
    ActivePage = TabSheetSummary
    TabOrder = 6
    Visible = False
    object TabSheetSummary: TTabSheet
      Caption = #50836#50557
      object GridSummary: TStringGrid
        Left = 0
        Top = 0
        Width = 976
        Height = 242
        Align = alClient
        ColCount = 2
        DefaultRowHeight = 20
        FixedCols = 1
        RowCount = 10
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        TabOrder = 0
      end
    end
    object TabSheetTrades: TTabSheet
      Caption = #44144#47000' '#45236#50669
      ImageIndex = 1
      object GridTrades: TStringGrid
        Left = 0
        Top = 0
        Width = 976
        Height = 242
        Align = alClient
        ColCount = 7
        DefaultRowHeight = 20
        FixedCols = 0
        RowCount = 1
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        TabOrder = 0
      end
    end
    object TabSheetChart: TTabSheet
      Caption = #52264#53944
      ImageIndex = 2
    end
  end
  object MemoLog: TMemo
    Left = 420
    Top = 296
    Width = 572
    Height = 120
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Consolas'
    Font.Style = []
    ParentFont = False
    ReadOnly = True
    ScrollBars = ssVertical
    TabOrder = 7
  end
  object TimerUpdate: TTimer
    Enabled = False
    OnTimer = TimerUpdateTimer
    Left = 944
    Top = 8
  end
end