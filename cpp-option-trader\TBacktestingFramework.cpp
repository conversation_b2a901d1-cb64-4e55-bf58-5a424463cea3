#include <vcl.h>
#pragma hdrstop
#include "TBacktestingFramework.h"
#include <SysUtils.hpp>
#include <Math.hpp>
#include <algorithm>
#include <numeric>

__fastcall TBacktestingFramework::TBacktestingFramework()
    : FIsRunning(false), FIsPaused(false), FCurrentCapital(0), FPeakCapital(0), FCurrentDrawdown(0)
{
    FUtils = std::make_unique<TUtils>();
    InitializeComponents();
}

__fastcall TBacktestingFramework::~TBacktestingFramework()
{
    StopBacktest();
}

void TBacktestingFramework::InitializeComponents()
{
    FVirtualMarket = std::make_unique<TVirtualMarket>();
    FExecutionEngine = std::make_unique<TVirtualExecutionEngine>();
    FStrategy = std::make_unique<TOptionStrategy>();
    FOrderManager = std::make_unique<TOrderManager>();
    FTrader = std::make_unique<TTrader>();
    
    SetupEventHandlers();
}

void TBacktestingFramework::SetupEventHandlers()
{
    // 가상 시장 이벤트
    FVirtualMarket->OnHogaReceived = OnVirtualHogaReceived;
    FVirtualMarket->OnJeobsuReceived = OnVirtualJeobsuReceived;
    FVirtualMarket->OnPlaybackFinished = OnBacktestFinished;
    
    // 가상 체결 엔진 이벤트
    FExecutionEngine->OnOrderFilled = OnVirtualOrderFilled;
    
    // 트레이더 이벤트
    FTrader->OnPositionUpdated = OnVirtualPositionUpdated;
}

void TBacktestingFramework::SetParameters(const TBacktestParameters& params)
{
    FParameters = params;
    
    // 컴포넌트 설정
    FVirtualMarket->SetDateRange(params.startDate, params.endDate);
    FVirtualMarket->SetTargetCodes(params.targetCodes);
    FVirtualMarket->SetPlaybackSpeed(params.playbackSpeed);
    
    FExecutionEngine->SetSlippageRate(params.slippageRate);
    FExecutionEngine->SetPartialFillRate(params.partialFillRate);
    FExecutionEngine->SetRealisticMode(params.useRealisticExecution);
    
    // 전략 파라미터 설정
    FStrategy->SetStochParams(params.stochK, params.stochD, params.stochSlowing);
    FStrategy->SetCCIPeriod(params.cciPeriod);
    FStrategy->SetDMIPeriod(params.dmiPeriod);
    FStrategy->SetBarInterval(params.barInterval);
    
    // 리스크 파라미터 설정
    FTrader->SetMaxPosition(params.maxPosition);
    FTrader->SetStopLoss(params.stopLoss);
    FTrader->SetTakeProfit(params.takeProfit);
    FTrader->SetMaxDailyLoss(params.maxDailyLoss);
    
    // 초기 자본 설정
    FCurrentCapital = params.initialCapital;
    FPeakCapital = params.initialCapital;
    FTrader->SetInitialCapital(params.initialCapital);
}

void TBacktestingFramework::StartBacktest()
{
    if (FIsRunning) return;
    
    try {
        FIsRunning = true;
        FIsPaused = false;
        
        // 결과 초기화
        FResult = TBacktestResult();
        FResult.startDate = FParameters.startDate;
        FResult.endDate = FParameters.endDate;
        FResult.initialCapital = FParameters.initialCapital;
        
        FCurrentCapital = FParameters.initialCapital;
        FPeakCapital = FParameters.initialCapital;
        FCurrentDrawdown = 0;
        FEquityCurve.clear();
        FDailyEquity.clear();
        
        // 컴포넌트 초기화
        FExecutionEngine->Reset();
        FTrader->Reset();
        FStrategy->Reset();
        
        // 가상 시장 시작
        FVirtualMarket->StartPlayback();
    }
    catch (const Exception& e) {
        FIsRunning = false;
        throw;
    }
}

void TBacktestingFramework::StopBacktest()
{
    if (!FIsRunning) return;
    
    FIsRunning = false;
    FIsPaused = false;
    
    FVirtualMarket->StopPlayback();
    
    // 최종 통계 계산
    CalculateStatistics();
}

void TBacktestingFramework::PauseBacktest()
{
    if (FIsRunning && !FIsPaused) {
        FIsPaused = true;
        FVirtualMarket->PausePlayback();
    }
}

void TBacktestingFramework::ResumeBacktest()
{
    if (FIsRunning && FIsPaused) {
        FIsPaused = false;
        FVirtualMarket->ResumePlayback();
    }
}

void TBacktestingFramework::OnVirtualHogaReceived(TObject* Sender, const THogaData& hoga)
{
    // 호가 데이터를 전략과 체결 엔진에 전달
    FStrategy->OnHogaReceived(Sender, hoga);
    FExecutionEngine->OnHogaUpdate(hoga);
}

void TBacktestingFramework::OnVirtualJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu)
{
    // 체결 데이터를 전략과 체결 엔진에 전달
    FStrategy->OnJeobsuReceived(Sender, jeobsu);
    FExecutionEngine->OnJeobsuUpdate(jeobsu);
    
    // 일일 수익률 계산을 위한 기준가 업데이트
    TDateTime currentDate = Date();
    if (FLastTradeDate != currentDate) {
        if (FLastTradeDate != 0) {
            double dailyReturn = (FCurrentCapital - FPeakCapital) / FPeakCapital;
            FResult.dailyReturns.push_back(dailyReturn);
        }
        FDailyEquity[currentDate] = FCurrentCapital;
        FLastTradeDate = currentDate;
    }
}

void TBacktestingFramework::OnVirtualOrderFilled(const TOrderInfo& order)
{
    // 체결된 주문을 트레이더에게 전달
    FTrader->OnOrderFilled(order);
    
    // 거래 기록 추가
    FResult.tradeHistory.push_back(order);
    FResult.totalTrades++;
    
    if (order.side == osLong) {
        // 매수 체결 시 자본 감소
        FCurrentCapital -= order.avgFillPrice * order.filledQty;
    } else {
        // 매도 체결 시 자본 증가
        FCurrentCapital += order.avgFillPrice * order.filledQty;
    }
    
    UpdatePerformanceMetrics();
}

void TBacktestingFramework::OnVirtualPositionUpdated(TObject* Sender, const TPositionInfo& position)
{
    // 포지션 업데이트 시 평가손익 반영
    FCurrentCapital = FParameters.initialCapital + position.realizedPnL + position.unrealizedPnL;
    UpdatePerformanceMetrics();
}

void TBacktestingFramework::OnBacktestFinished(TObject* Sender)
{
    FIsRunning = false;
    CalculateStatistics();
    
    if (OnBacktestCompleted) {
        OnBacktestCompleted(this);
    }
}

void TBacktestingFramework::UpdatePerformanceMetrics()
{
    // 최고점 자본 업데이트
    if (FCurrentCapital > FPeakCapital) {
        FPeakCapital = FCurrentCapital;
    }
    
    // 현재 드로우다운 계산
    FCurrentDrawdown = (FPeakCapital - FCurrentCapital) / FPeakCapital;
    
    // 최대 드로우다운 업데이트
    if (FCurrentDrawdown > FResult.maxDrawdown) {
        FResult.maxDrawdown = FCurrentDrawdown;
    }
    
    // 자본 곡선 추가
    FEquityCurve.push_back(FCurrentCapital);
}

void TBacktestingFramework::CalculateStatistics()
{
    FResult.finalCapital = FCurrentCapital;
    FResult.totalReturn = (FCurrentCapital - FParameters.initialCapital) / FParameters.initialCapital;
    
    // 승률 계산
    int winCount = 0;
    int lossCount = 0;
    double totalWin = 0;
    double totalLoss = 0;
    
    for (const auto& trade : FResult.tradeHistory) {
        if (trade.side == osShort) { // 청산 거래만 계산
            double pnl = trade.avgFillPrice * trade.filledQty; // 간단화된 계산
            if (pnl > 0) {
                winCount++;
                totalWin += pnl;
            } else if (pnl < 0) {
                lossCount++;
                totalLoss += abs(pnl);
            }
        }
    }
    
    FResult.winningTrades = winCount;
    FResult.losingTrades = lossCount;
    FResult.winRate = (winCount + lossCount) > 0 ? 
                     static_cast<double>(winCount) / (winCount + lossCount) : 0;
    FResult.avgWin = winCount > 0 ? totalWin / winCount : 0;
    FResult.avgLoss = lossCount > 0 ? totalLoss / lossCount : 0;
    FResult.profitFactor = totalLoss > 0 ? totalWin / totalLoss : 0;
    
    // 샤프 비율 계산
    FResult.sharpeRatio = CalculateSharpeRatio(FResult.dailyReturns);
}

double TBacktestingFramework::CalculateSharpeRatio(const std::vector<double>& returns)
{
    if (returns.empty()) return 0;
    
    double mean = std::accumulate(returns.begin(), returns.end(), 0.0) / returns.size();
    
    double variance = 0;
    for (double ret : returns) {
        variance += (ret - mean) * (ret - mean);
    }
    variance /= returns.size();
    
    double stdDev = sqrt(variance);
    
    return stdDev > 0 ? (mean * sqrt(252)) / (stdDev * sqrt(252)) : 0; // 연환산
}

double TBacktestingFramework::GetCurrentProgress() const
{
    if (!FIsRunning) return 0;
    
    TDateTime currentTime = FVirtualMarket->GetCurrentTime();
    double totalTime = FParameters.endDate - FParameters.startDate;
    double elapsedTime = currentTime - FParameters.startDate;
    
    return totalTime > 0 ? elapsedTime / totalTime : 0;
}

void TBacktestingFramework::SaveResultToFile(const AnsiString& fileName)
{
    try {
        TStringList* lines = new TStringList();
        
        lines->Add("=== 백테스트 결과 ===");
        lines->Add("시작일: " + FormatDateTime("yyyy-mm-dd", FResult.startDate));
        lines->Add("종료일: " + FormatDateTime("yyyy-mm-dd", FResult.endDate));
        lines->Add("초기 자본: " + FormatFloat("#,##0", FResult.initialCapital));
        lines->Add("최종 자본: " + FormatFloat("#,##0", FResult.finalCapital));
        lines->Add("총 수익률: " + FormatFloat("0.00%", FResult.totalReturn * 100));
        lines->Add("최대 드로우다운: " + FormatFloat("0.00%", FResult.maxDrawdown * 100));
        lines->Add("샤프 비율: " + FormatFloat("0.00", FResult.sharpeRatio));
        lines->Add("");
        lines->Add("=== 거래 통계 ===");
        lines->Add("총 거래 수: " + IntToStr(FResult.totalTrades));
        lines->Add("승리 거래: " + IntToStr(FResult.winningTrades));
        lines->Add("손실 거래: " + IntToStr(FResult.losingTrades));
        lines->Add("승률: " + FormatFloat("0.00%", FResult.winRate * 100));
        lines->Add("평균 승리: " + FormatFloat("#,##0", FResult.avgWin));
        lines->Add("평균 손실: " + FormatFloat("#,##0", FResult.avgLoss));
        lines->Add("손익비: " + FormatFloat("0.00", FResult.profitFactor));
        
        lines->SaveToFile(fileName);
        delete lines;
    }
    catch (const Exception& e) {
        throw Exception("결과 저장 오류: " + e.Message);
    }
}

void TBacktestingFramework::ExportTradeHistory(const AnsiString& fileName)
{
    try {
        TStringList* lines = new TStringList();
        
        // 헤더
        lines->Add("시간,코드,구분,수량,가격,상태");
        
        // 거래 기록
        for (const auto& trade : FResult.tradeHistory) {
            AnsiString line = Format("%s,%s,%s,%d,%.0f,%s",
                ARRAYOFCONST((
                    FormatDateTime("yyyy-mm-dd hh:nn:ss", trade.time),
                    trade.code,
                    trade.side == osLong ? "매수" : "매도",
                    trade.filledQty,
                    trade.avgFillPrice,
                    trade.status
                )));
            lines->Add(line);
        }
        
        lines->SaveToFile(fileName);
        delete lines;
    }
    catch (const Exception& e) {
        throw Exception("거래내역 저장 오류: " + e.Message);
    }
}

void TBacktestingFramework::ExportEquityCurve(const AnsiString& fileName)
{
    try {
        TStringList* lines = new TStringList();
        
        // 헤더
        lines->Add("Index,Equity");
        
        // 자본 곡선
        for (size_t i = 0; i < FEquityCurve.size(); i++) {
            lines->Add(IntToStr(i) + "," + FormatFloat("0.00", FEquityCurve[i]));
        }
        
        lines->SaveToFile(fileName);
        delete lines;
    }
    catch (const Exception& e) {
        throw Exception("자본곡선 저장 오류: " + e.Message);
    }
}

std::vector<double> TBacktestingFramework::GetMonthlyReturns()
{
    std::vector<double> monthlyReturns;
    
    // 일일 자본 데이터를 월별로 집계
    std::map<AnsiString, double> monthlyCapital;
    
    for (const auto& pair : FDailyEquity) {
        AnsiString monthKey = FormatDateTime("yyyy-mm", pair.first);
        monthlyCapital[monthKey] = pair.second; // 마지막 날 자본으로 덮어쓰기
    }
    
    double previousCapital = FParameters.initialCapital;
    for (const auto& pair : monthlyCapital) {
        double monthlyReturn = (pair.second - previousCapital) / previousCapital;
        monthlyReturns.push_back(monthlyReturn);
        previousCapital = pair.second;
    }
    
    return monthlyReturns;
}

TBacktestResult TBacktestingFramework::RunParameterOptimization(const std::vector<TBacktestParameters>& paramSets)
{
    TBacktestResult bestResult;
    bestResult.totalReturn = -1000; // 매우 낮은 초기값
    
    for (const auto& params : paramSets) {
        SetParameters(params);
        StartBacktest();
        
        // 백테스트 완료 대기 (간단화된 구현)
        while (FIsRunning) {
            Sleep(100);
            Application->ProcessMessages();
        }
        
        TBacktestResult currentResult = GetResult();
        if (currentResult.totalReturn > bestResult.totalReturn) {
            bestResult = currentResult;
        }
    }
    
    return bestResult;
}

void TBacktestingFramework::GenerateParameterSets(std::vector<TBacktestParameters>& paramSets,
                                                 const TBacktestParameters& baseParams,
                                                 const std::map<AnsiString, std::vector<double>>& ranges)
{
    paramSets.clear();
    
    // 단순한 격자 탐색 구현
    for (double stochK : ranges.at("stochK")) {
        for (double stochD : ranges.at("stochD")) {
            for (double cciPeriod : ranges.at("cciPeriod")) {
                TBacktestParameters params = baseParams;
                params.stochK = static_cast<int>(stochK);
                params.stochD = static_cast<int>(stochD);
                params.cciPeriod = static_cast<int>(cciPeriod);
                paramSets.push_back(params);
            }
        }
    }
}