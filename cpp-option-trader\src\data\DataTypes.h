#ifndef DATATYPES_H
#define DATATYPES_H

// C++Builder compatibility
#ifdef __BORLANDC__
  #include <vcl.h>
  #include <System.hpp>
  // Use STLport containers for C++Builder
  #include <vector>
  #include <map>
  #include <memory>
  
  // C++Builder specific type definitions
  #define std_vector std::vector
  #define std_map std::map
  #define std_unique_ptr std::unique_ptr
  #define std_make_unique std::make_unique
#else
  // Standard modern C++
  #include <vector>
  #include <map>
  #include <memory>
  #include <mutex>
  #include <thread>
  #include <chrono>
  
  #define std_vector std::vector
  #define std_map std::map
  #define std_unique_ptr std::unique_ptr
  #define std_make_unique std::make_unique
#endif

// 주문 상태 열거형
enum class TOrderState {
    WAITING,           // 대기 중
    PRE_ORDER,         // 사전 주문
    TRACKING,          // 1호가 추적 중
    EXECUTED,          // 체결 완료
    CANCELLED,         // 취소됨
    PROFIT_TRACKING    // 익절 추적 중
};

// 주문 타입 열거형
enum class TOrderType {
    LIMIT,    // 지정가
    MARKET,   // 시장가
    CANCEL    // 취소
};

// 거래 구분 열거형
enum class TTradeType {
    BUY,      // 매수
    SELL      // 매도
};

// 거래 모드 열거형
enum class TTradingMode {
    REAL_TIME,    // 실시간
    SIMULATION,   // 시뮬레이션
    REPLAY        // 리플레이
};

// 옵션 타입 열거형 (새로 추가)
enum class TOptionType {
    CALL = 0,     // 콜옵션
    PUT = 1,      // 풋옵션
    BOTH = 2      // 콜/풋 둘다
};

// 로그 레벨 열거형
enum class TLogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

// 주문 정보 구조체
struct TOrderInfo {
    AnsiString order_id;           // 주문 ID
    AnsiString code;               // 종목 코드
    TOrderType order_type;         // 주문 타입
    TTradeType trade_type;         // 거래 구분
    int quantity;                  // 수량
    double price;                  // 가격
    TOrderState state;             // 주문 상태
    int modify_count;              // 정정 횟수
    TDateTime created_time;        // 생성 시간
    TDateTime updated_time;        // 수정 시간
    double profit_target;          // 익절 목표가
    double stop_loss;              // 손절가
    AnsiString parent_order_id;    // 부모 주문 ID (익절 주문용)
    
    TOrderInfo() : 
        quantity(0), price(0.0), state(TOrderState::WAITING), 
        modify_count(0), profit_target(0.0), stop_loss(0.0) {}
};

// 호가 데이터 구조체
struct THogaData {
    AnsiString code;                        // 종목 코드
    std::vector<double> ask_prices;         // 매도 호가 (10단계)
    std::vector<double> bid_prices;         // 매수 호가 (10단계)
    std::vector<int> ask_quantities;        // 매도 잔량
    std::vector<int> bid_quantities;        // 매수 잔량
    TDateTime timestamp;                    // 시간
    double current_price;                   // 현재가
    double prev_price;                      // 전일 종가
    
    THogaData() : current_price(0.0), prev_price(0.0) {
        ask_prices.resize(10, 0.0);
        bid_prices.resize(10, 0.0);
        ask_quantities.resize(10, 0);
        bid_quantities.resize(10, 0);
    }
};

// 체결 데이터 구조체
struct TJeobsuData {
    AnsiString code;               // 종목 코드
    double price;                  // 체결가
    int quantity;                  // 체결량
    TDateTime timestamp;           // 체결 시간
    TTradeType trade_type;         // 매수/매도 구분
    
    TJeobsuData() : price(0.0), quantity(0), trade_type(TTradeType::BUY) {}
};

// 포지션 정보 구조체
struct TPositionInfo {
    AnsiString code;               // 종목 코드
    int quantity;                  // 보유 수량
    double average_price;          // 평균 매입가
    double current_price;          // 현재가
    double total_cost;             // 총 매입 비용
    double unrealized_pnl;         // 미실현 손익
    double realized_pnl;           // 실현 손익
    TDateTime open_time;           // 포지션 개시 시간
    TDateTime last_update;         // 마지막 업데이트 시간
    
    TPositionInfo() : 
        quantity(0), average_price(0.0), current_price(0.0), 
        total_cost(0.0), unrealized_pnl(0.0), realized_pnl(0.0) {}
};

// 계좌 정보 구조체
struct TAccountInfo {
    AnsiString account_no;         // 계좌번호
    double total_asset;            // 총 자산
    double available_cash;         // 주문 가능 현금
    double total_eval_amount;      // 총 평가 금액
    double total_profit_loss;      // 총 평가 손익
    double total_profit_rate;      // 총 수익률
    
    TAccountInfo() : 
        total_asset(0.0), available_cash(0.0), total_eval_amount(0.0),
        total_profit_loss(0.0), total_profit_rate(0.0) {}
};

// 옵션 선택 파라미터 구조체 (새로 추가)
struct TOptionSelectionParams {
    double priceMin;               // 최소 옵션 가격
    double priceMax;               // 최대 옵션 가격
    bool allowFallback;            // 폴백 허용 여부
    AnsiString fallbackCallCode;   // 폴백 콜옵션 코드
    AnsiString fallbackPutCode;    // 폴백 풋옵션 코드
    
    TOptionSelectionParams() :
        priceMin(1.0), priceMax(50.0), allowFallback(true) {}
};

// 주문 실행 정보 구조체 (새로 추가)
struct TExecutionInfo {
    TDateTime executionTime;       // 체결 시간
    int executedQuantity;          // 체결 수량
    double executedPrice;          // 체결 가격
    AnsiString executionId;        // 체결 ID
    
    TExecutionInfo() : executedQuantity(0), executedPrice(0.0) {}
};

// 주문 상태 구조체 (새로 추가)
struct TOrderStatus {
    AnsiString orderId;            // 주문 ID
    int totalQuantity;             // 총 주문 수량
    int executedQuantity;          // 체결된 수량
    int remainingQuantity;         // 미체결 수량
    std::vector<TExecutionInfo> executions;  // 체결 내역
    TDateTime lastUpdateTime;      // 마지막 업데이트 시간
    
    TOrderStatus() : totalQuantity(0), executedQuantity(0), remainingQuantity(0) {}
    
    void AddExecution(const TExecutionInfo& execution) {
        executions.push_back(execution);
        executedQuantity += execution.executedQuantity;
        remainingQuantity = totalQuantity - executedQuantity;
        lastUpdateTime = Now();
    }
};

// 전략 파라미터 구조체 (업데이트됨)
struct TStrategyParams {
    // Stochastic 파라미터
    int stoch_k_period;
    int stoch_d_period;
    double stoch_buy_threshold;
    double stoch_sell_threshold;
    
    // CCI 파라미터
    int cci_period;
    int scci_period;               // 짧은 CCI 기간 (새로 추가)
    double cci_buy_threshold;
    double cci_sell_threshold;
    int cci_level;                 // CCI 레벨 (새로 추가)
    int scci_level;                // 짧은 CCI 레벨 (새로 추가)
    
    // DMI/ADX 파라미터
    int dmi_period;
    int adx_period;                // ADX 별도 기간 (새로 추가)
    double adx_threshold;
    int adx_level;                 // ADX 레벨 (새로 추가)
    int stoch_level;               // Stochastic 레벨 (새로 추가)
    
    // 일반 설정
    double profit_target_rate;     // 익절 목표 수익률
    double stop_loss_rate;         // 손절 수익률
    double abs_profit_target;      // 절대 익절 목표 (새로 추가)
    int max_modify_count;          // 최대 정정 횟수
    int order_timeout_seconds;     // 주문 타임아웃
    
    // 전략 설정 (새로 추가)
    bool realMinStrategy;          // 실시간 분석 여부
    bool sellCondUse;              // 매도 조건 사용 여부
    bool dualOptionMode;           // 듀얼 옵션 모드
    TOptionType defaultOptionType; // 기본 옵션 타입
    
    TStrategyParams() :
        stoch_k_period(14), stoch_d_period(3),
        stoch_buy_threshold(20.0), stoch_sell_threshold(80.0),
        cci_period(14), scci_period(5), cci_buy_threshold(100.0), cci_sell_threshold(-100.0),
        cci_level(100), scci_level(-100),
        dmi_period(14), adx_period(14), adx_threshold(25.0), adx_level(25), stoch_level(20),
        profit_target_rate(5.0), stop_loss_rate(-2.0), abs_profit_target(20.0),
        max_modify_count(3), order_timeout_seconds(15),
        realMinStrategy(true), sellCondUse(true), dualOptionMode(false),
        defaultOptionType(TOptionType::CALL) {}
};

// 로그 레벨 열거형
enum class TLogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

// 분봉 데이터 구조체
struct TBarData {
    AnsiString code;               // 종목 코드
    TDateTime time;                // 봉 시간
    double open;                   // 시가
    double high;                   // 고가
    double low;                    // 저가
    double close;                  // 종가
    int volume;                    // 거래량
    double value;                  // 거래대금
    int tick_count;                // 틱 개수
    TDateTime last_update;         // 마지막 업데이트
    
    TBarData() : open(0.0), high(0.0), low(0.0), close(0.0), 
                volume(0), value(0.0), tick_count(0) {}
};

// 로그 메시지 구조체
struct TLogMessage {
    TDateTime timestamp;
    TLogLevel level;
    AnsiString category;
    AnsiString message;
    
    TLogMessage() : level(TLogLevel::INFO) {}
    TLogMessage(TLogLevel lvl, const AnsiString& cat, const AnsiString& msg) :
        timestamp(Now()), level(lvl), category(cat), message(msg) {}
};

// 이벤트 핸들러 타입 정의
typedef void __fastcall (__closure *TOrderFilledEvent)(TObject* Sender, const TOrderInfo& order);
typedef void __fastcall (__closure *TPositionUpdatedEvent)(TObject* Sender, const TPositionInfo& position);
typedef void __fastcall (__closure *TRiskLimitExceededEvent)(TObject* Sender, const AnsiString& message);
typedef void __fastcall (__closure *TBarCompletedEvent)(const AnsiString& code, const TBarData& bar);
typedef void __fastcall (__closure *TBarUpdatedEvent)(const AnsiString& code, const TBarData& bar);
typedef void __fastcall (__closure *TOrderStateChangedEvent)(TObject* Sender, const TOrderInfo& order);

// 전방 선언
class TDataLogger;

// 타입 별칭
typedef std::map<AnsiString, std::shared_ptr<TOrderInfo>> TOrderMap;
typedef std::map<AnsiString, std::shared_ptr<TPositionInfo>> TPositionMap;
typedef std::vector<std::shared_ptr<TLogMessage>> TLogList;
typedef std::map<AnsiString, TOrderStatus> TOrderStatusMap;

// 락 가드 클래스 (RAII)
class TLockGuard {
private:
    TCriticalSection* FSection;
public:
    __fastcall TLockGuard(TCriticalSection* section) : FSection(section) {
        if (FSection) FSection->Enter();
    }
    __fastcall ~TLockGuard() {
        if (FSection) FSection->Leave();
    }
};

#endif // DATATYPES_H