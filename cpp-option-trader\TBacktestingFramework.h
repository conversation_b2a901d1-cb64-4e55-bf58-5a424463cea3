#ifndef TBacktestingFrameworkH
#define TBacktestingFrameworkH

#include <System.Classes.hpp>
#include <memory>
#include <vector>
#include <map>
#include "DataTypes.h"
#include "TUtils.h"
#include "TVirtualMarket.h"
#include "TVirtualExecutionEngine.h"
#include "TOptionStrategy.h"
#include "TOrderManager.h"
#include "TTrader.h"

struct TBacktestResult {
    TDateTime startDate;
    TDateTime endDate;
    double initialCapital;
    double finalCapital;
    double totalReturn;
    double maxDrawdown;
    double sharpeRatio;
    int totalTrades;
    int winningTrades;
    int losingTrades;
    double winRate;
    double avgWin;
    double avgLoss;
    double profitFactor;
    std::vector<TOrderInfo> tradeHistory;
    std::vector<double> dailyReturns;
    std::map<AnsiString, double> monthlyReturns;
};

struct TBacktestParameters {
    TDateTime startDate;
    TDateTime endDate;
    std::vector<AnsiString> targetCodes;
    double initialCapital;
    double playbackSpeed;
    
    // 전략 파라미터
    int stochK;
    int stochD;
    int stochSlowing;
    int cciPeriod;
    int dmiPeriod;
    int barInterval;
    
    // 리스크 파라미터
    double maxPosition;
    double stopLoss;
    double takeProfit;
    double maxDailyLoss;
    
    // 실행 파라미터
    double slippageRate;
    double partialFillRate;
    bool useRealisticExecution;
};

class TBacktestingFramework : public TObject
{
private:
    std::unique_ptr<TUtils> FUtils;
    std::unique_ptr<TVirtualMarket> FVirtualMarket;
    std::unique_ptr<TVirtualExecutionEngine> FExecutionEngine;
    std::unique_ptr<TOptionStrategy> FStrategy;
    std::unique_ptr<TOrderManager> FOrderManager;
    std::unique_ptr<TTrader> FTrader;
    
    TBacktestParameters FParameters;
    TBacktestResult FResult;
    bool FIsRunning;
    bool FIsPaused;
    
    double FCurrentCapital;
    double FPeakCapital;
    double FCurrentDrawdown;
    TDateTime FLastTradeDate;
    std::vector<double> FEquityCurve;
    std::map<TDateTime, double> FDailyEquity;
    
    void InitializeComponents();
    void SetupEventHandlers();
    void UpdatePerformanceMetrics();
    void CalculateStatistics();
    double CalculateSharpeRatio(const std::vector<double>& returns);
    void OnVirtualHogaReceived(TObject* Sender, const THogaData& hoga);
    void OnVirtualJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu);
    void OnVirtualOrderFilled(const TOrderInfo& order);
    void OnVirtualPositionUpdated(TObject* Sender, const TPositionInfo& position);
    void OnBacktestFinished(TObject* Sender);
    
public:
    __fastcall TBacktestingFramework();
    __fastcall ~TBacktestingFramework();
    
    // 이벤트 핸들러
    TNotifyEvent OnProgressUpdate;
    TNotifyEvent OnBacktestCompleted;
    
    // 백테스트 실행
    void SetParameters(const TBacktestParameters& params);
    void StartBacktest();
    void StopBacktest();
    void PauseBacktest();
    void ResumeBacktest();
    
    // 결과 조회
    TBacktestResult GetResult() const { return FResult; }
    double GetCurrentProgress() const;
    bool IsRunning() const { return FIsRunning; }
    bool IsPaused() const { return FIsPaused; }
    
    // 분석 메서드
    void SaveResultToFile(const AnsiString& fileName);
    void ExportTradeHistory(const AnsiString& fileName);
    void ExportEquityCurve(const AnsiString& fileName);
    std::vector<double> GetMonthlyReturns();
    std::vector<double> GetDailyReturns() const { return FResult.dailyReturns; }
    
    // 최적화 지원
    TBacktestResult RunParameterOptimization(const std::vector<TBacktestParameters>& paramSets);
    void GenerateParameterSets(std::vector<TBacktestParameters>& paramSets,
                              const TBacktestParameters& baseParams,
                              const std::map<AnsiString, std::vector<double>>& ranges);
};

#endif