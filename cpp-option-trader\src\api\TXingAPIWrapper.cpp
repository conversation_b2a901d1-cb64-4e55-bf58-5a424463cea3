#include "TXingAPIWrapper.h"
#include "../utils/TUtils.h"
#include <System.JSON.hpp>
#include <System.SysUtils.hpp>
#include <System.Variants.hpp>

// 생성자
__fastcall TXingAPIWrapper::TXingAPIWrapper() {
    FIsConnected = false;
    FIsLoggedIn = false;
    FIsReal = false;
    FAutoReconnect = false;
    FReconnectAttempts = 0;
    FMaxReconnectAttempts = 3;
    FDebugMode = false;
    
    // 크리티컬 섹션 생성
    FCriticalSection = new TCriticalSection();
    
    // 타이머 생성
    FEventProcessTimer = new TTimer(NULL);
    FEventProcessTimer->Interval = 100;  // 100ms
    FEventProcessTimer->OnTimer = OnEventProcessTimer;
    FEventProcessTimer->Enabled = false;
    
    FReconnectTimer = new TTimer(NULL);
    FReconnectTimer->Interval = 5000;  // 5초
    FReconnectTimer->OnTimer = OnReconnectTimer;
    FReconnectTimer->Enabled = false;
    
    // 마지막 데이터 초기화
    FLastAccountInfo = std::make_shared<TAccountInfo>();
    
    InitializeCOMObjects();
}

// 소멸자
__fastcall TXingAPIWrapper::~TXingAPIWrapper() {
    StopReconnectTimer();
    
    if (FIsLoggedIn) {
        Logout();
    }
    
    if (FIsConnected) {
        DisconnectServer();
    }
    
    CleanupCOMObjects();
    
    delete FEventProcessTimer;
    delete FReconnectTimer;
    delete FCriticalSection;
}

// COM 객체 초기화
bool TXingAPIWrapper::InitializeCOMObjects() {
    try {
        // XASession 생성
        FXASession = CreateOleObject("XA_Session.XASession");
        
        WriteLog(TLogLevel::INFO, "COM 객체 초기화 완료");
        return true;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "COM 객체 초기화 실패: " + e.Message);
        return false;
    }
}

// COM 객체 정리
void TXingAPIWrapper::CleanupCOMObjects() {
    try {
        // 실시간 구독 해제
        UnsubscribeAll();
        
        // Query 객체들 정리
        for (auto& pair : FXAQueries) {
            try {
                pair.second = Unassigned;
            } catch (...) {}
        }
        FXAQueries.clear();
        
        // Real 객체들 정리
        for (auto& pair : FXAReals) {
            try {
                pair.second = Unassigned;
            } catch (...) {}
        }
        FXAReals.clear();
        
        // Session 객체 정리
        FXASession = Unassigned;
        
        WriteLog(TLogLevel::INFO, "COM 객체 정리 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::WARNING, "COM 객체 정리 중 오류: " + e.Message);
    }
}

// 서버 연결
bool TXingAPIWrapper::ConnectServer(bool isReal) {
    try {
        if (FIsConnected) {
            WriteLog(TLogLevel::WARNING, "이미 서버에 연결되어 있습니다");
            return true;
        }
        
        FIsReal = isReal;
        
        // 서버 정보 설정
        AnsiString serverIP = isReal ? "hts.ebestsec.co.kr" : "demo.ebestsec.co.kr";
        int port = isReal ? 20001 : 20001;
        
        // 연결 시도
        bool result = FXASession.OlePropertyGet("ConnectServer").AsType<bool>();
        
        if (result) {
            FIsConnected = true;
            FEventProcessTimer->Enabled = true;
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("서버 연결 완료 (%s:%d)", 
                    serverIP.c_str(), port));
            
            if (FOnConnected) {
                FOnConnected(this);
            }
            
            return true;
        } else {
            WriteLog(TLogLevel::ERROR, "서버 연결 실패");
            return false;
        }
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "서버 연결 중 오류: " + e.Message);
        return false;
    }
}

// 서버 연결 해제
void TXingAPIWrapper::DisconnectServer() {
    try {
        if (!FIsConnected) return;
        
        FEventProcessTimer->Enabled = false;
        
        // 로그아웃 먼저 수행
        if (FIsLoggedIn) {
            Logout();
        }
        
        // 연결 해제
        FXASession.OleProcedure("DisconnectServer");
        
        FIsConnected = false;
        
        WriteLog(TLogLevel::INFO, "서버 연결 해제 완료");
        
        if (FOnDisconnected) {
            FOnDisconnected(this);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "서버 연결 해제 중 오류: " + e.Message);
    }
}

// 로그인
bool TXingAPIWrapper::Login(const AnsiString& userId, const AnsiString& password, 
                           const AnsiString& certPassword) {
    try {
        if (!FIsConnected) {
            WriteLog(TLogLevel::ERROR, "서버에 연결되지 않음");
            return false;
        }
        
        if (FIsLoggedIn) {
            WriteLog(TLogLevel::WARNING, "이미 로그인되어 있습니다");
            return true;
        }
        
        FUserId = userId;
        
        // 로그인 실행
        bool result = FXASession.OleFunction("Login", 
            WideString(userId), 
            WideString(password), 
            WideString(certPassword), 
            0,  // 서버구분 (0: 실서버, 1: 모의투자)
            0   // 공인인증서 로그인 여부
        ).AsType<bool>();
        
        if (result) {
            FIsLoggedIn = true;
            
            // 계좌 목록 조회
            GetAccountList();
            
            WriteLog(TLogLevel::INFO, "로그인 성공: " + userId);
            
            if (FOnLoginResult) {
                FOnLoginResult(this, true, "로그인 성공");
            }
            
            return true;
        } else {
            AnsiString errorMsg = GetLastErrorMessage();
            WriteLog(TLogLevel::ERROR, "로그인 실패: " + errorMsg);
            
            if (FOnLoginResult) {
                FOnLoginResult(this, false, errorMsg);
            }
            
            return false;
        }
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "로그인 중 오류: " + e.Message);
        
        if (FOnLoginResult) {
            FOnLoginResult(this, false, e.Message);
        }
        
        return false;
    }
}

// 로그아웃
void TXingAPIWrapper::Logout() {
    try {
        if (!FIsLoggedIn) return;
        
        // 모든 구독 해제
        UnsubscribeAll();
        
        // 로그아웃 실행
        FXASession.OleProcedure("Logout");
        
        FIsLoggedIn = false;
        FUserId = "";
        FAccountNumber = "";
        FAccountList.clear();
        
        WriteLog(TLogLevel::INFO, "로그아웃 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "로그아웃 중 오류: " + e.Message);
    }
}

// 계좌 목록 조회
std::vector<AnsiString> TXingAPIWrapper::GetAccountList() {
    try {
        FAccountList.clear();
        
        if (!FIsLoggedIn) {
            WriteLog(TLogLevel::ERROR, "로그인이 필요합니다");
            return FAccountList;
        }
        
        // 계좌 개수 조회
        int accountCount = FXASession.OlePropertyGet("GetAccountListCount").AsType<int>();
        
        // 각 계좌 정보 조회
        for (int i = 0; i < accountCount; i++) {
            AnsiString account = FXASession.OleFunction("GetAccountList", i).AsType<AnsiString>();
            FAccountList.push_back(account);
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("계좌 목록 조회 완료: %d개", accountCount));
        
        return FAccountList;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "계좌 목록 조회 중 오류: " + e.Message);
        return FAccountList;
    }
}

// 계좌 설정
bool TXingAPIWrapper::SetAccount(const AnsiString& accountNumber) {
    try {
        // 유효한 계좌인지 확인
        bool found = false;
        for (const AnsiString& account : FAccountList) {
            if (account == accountNumber) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            WriteLog(TLogLevel::ERROR, "유효하지 않은 계좌번호: " + accountNumber);
            return false;
        }
        
        FAccountNumber = accountNumber;
        WriteLog(TLogLevel::INFO, "계좌 설정 완료: " + accountNumber);
        
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "계좌 설정 중 오류: " + e.Message);
        return false;
    }
}

// Query 객체 생성
TXAQuery TXingAPIWrapper::CreateQuery(const AnsiString& queryName) {
    try {
        TXAQuery query = CreateOleObject("XA_DataSet.XAQuery");
        FXAQueries[queryName] = query;
        
        // 이벤트 핸들러 연결 (실제 구현에서는 이벤트 싱크 필요)
        // query->OnReceiveData = ...
        
        return query;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "Query 생성 실패: " + e.Message);
        return Unassigned;
    }
}

// Real 객체 생성
TXAReal TXingAPIWrapper::CreateReal(const AnsiString& realName) {
    try {
        TXAReal real = CreateOleObject("XA_DataSet.XAReal");
        FXAReals[realName] = real;
        
        // 이벤트 핸들러 연결
        // real->OnReceiveRealData = ...
        
        return real;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "Real 생성 실패: " + e.Message);
        return Unassigned;
    }
}

// 주문 전송
AnsiString TXingAPIWrapper::SendOrder(const AnsiString& code, TTradeType tradeType, 
                                     int quantity, double price, TOrderType orderType) {
    try {
        if (!FIsLoggedIn) {
            WriteLog(TLogLevel::ERROR, "로그인이 필요합니다");
            return "";
        }
        
        if (FAccountNumber.IsEmpty()) {
            WriteLog(TLogLevel::ERROR, "계좌가 설정되지 않았습니다");
            return "";
        }
        
        // 주문 Query 생성
        TXAQuery orderQuery = CreateQuery("ORDER");
        
        // 주문 데이터 설정
        orderQuery.OleProcedure("SetFieldData", "InBlock", "AcntNo", 0, FAccountNumber);
        orderQuery.OleProcedure("SetFieldData", "InBlock", "IsuNo", 0, code);
        orderQuery.OleProcedure("SetFieldData", "InBlock", "OrdQty", 0, quantity);
        orderQuery.OleProcedure("SetFieldData", "InBlock", "OrdPrc", 0, price);
        
        // 매매구분 설정
        AnsiString bsType = (tradeType == TTradeType::BUY) ? "2" : "1";
        orderQuery.OleProcedure("SetFieldData", "InBlock", "BnsTpCode", 0, bsType);
        
        // 주문유형 설정
        AnsiString ordType = (orderType == TOrderType::MARKET) ? "03" : "00";
        orderQuery.OleProcedure("SetFieldData", "InBlock", "OrdprcPtnCode", 0, ordType);
        
        // 주문 전송
        int result = orderQuery.OleFunction("Request", false).AsType<int>();
        
        if (result < 0) {
            WriteLog(TLogLevel::ERROR, "주문 전송 실패");
            return "";
        }
        
        // 주문번호 생성 (실제로는 서버에서 받아와야 함)
        AnsiString orderId = AnsiString().sprintf("ORD%08d", GetTickCount());
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문 전송 완료 - ID: %s, 종목: %s, %s %d주, 가격: %.0f", 
                orderId.c_str(), code.c_str(), 
                TUtils::TradeTypeToString(tradeType).c_str(), 
                quantity, price));
        
        return orderId;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "주문 전송 중 오류: " + e.Message);
        return "";
    }
}

// 주문 취소
bool TXingAPIWrapper::CancelOrder(const AnsiString& orderId) {
    try {
        // 취소 주문 Query 생성
        TXAQuery cancelQuery = CreateQuery("CANCEL");
        
        // 취소 데이터 설정
        cancelQuery.OleProcedure("SetFieldData", "InBlock", "OrgOrdNo", 0, orderId);
        cancelQuery.OleProcedure("SetFieldData", "InBlock", "AcntNo", 0, FAccountNumber);
        
        // 취소 전송
        int result = cancelQuery.OleFunction("Request", false).AsType<int>();
        
        if (result < 0) {
            WriteLog(TLogLevel::ERROR, "주문 취소 실패: " + orderId);
            return false;
        }
        
        WriteLog(TLogLevel::INFO, "주문 취소 완료: " + orderId);
        return true;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "주문 취소 중 오류: " + e.Message);
        return false;
    }
}

// 주문 정정
bool TXingAPIWrapper::ModifyOrder(const AnsiString& orderId, double newPrice) {
    try {
        // 정정 주문 Query 생성
        TXAQuery modifyQuery = CreateQuery("MODIFY");
        
        // 정정 데이터 설정
        modifyQuery.OleProcedure("SetFieldData", "InBlock", "OrgOrdNo", 0, orderId);
        modifyQuery.OleProcedure("SetFieldData", "InBlock", "AcntNo", 0, FAccountNumber);
        modifyQuery.OleProcedure("SetFieldData", "InBlock", "OrdPrc", 0, newPrice);
        
        // 정정 전송
        int result = modifyQuery.OleFunction("Request", false).AsType<int>();
        
        if (result < 0) {
            WriteLog(TLogLevel::ERROR, "주문 정정 실패: " + orderId);
            return false;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문 정정 완료: %s, 새가격: %.0f", 
                orderId.c_str(), newPrice));
        return true;
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "주문 정정 중 오류: " + e.Message);
        return false;
    }
}

// 호가 구독
bool TXingAPIWrapper::SubscribeHoga(const AnsiString& code) {
    try {
        if (FSubscribedCodes.find(code) != FSubscribedCodes.end()) {
            WriteLog(TLogLevel::WARNING, "이미 구독 중인 종목: " + code);
            return true;
        }
        
        // 호가 Real 생성
        TXAReal hogaReal = CreateReal("HOGA_" + code);
        
        // 종목 코드 설정
        hogaReal.OleProcedure("SetFieldData", "InBlock", "shcode", code);
        
        // 구독 시작
        bool result = hogaReal.OleFunction("AdviseRealData").AsType<bool>();
        
        if (result) {
            FSubscribedCodes[code] = true;
            WriteLog(TLogLevel::INFO, "호가 구독 시작: " + code);
            return true;
        } else {
            WriteLog(TLogLevel::ERROR, "호가 구독 실패: " + code);
            return false;
        }
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "호가 구독 중 오류: " + e.Message);
        return false;
    }
}

// 체결 구독
bool TXingAPIWrapper::SubscribeJeobsu(const AnsiString& code) {
    try {
        // 체결 Real 생성
        TXAReal jeobsuReal = CreateReal("JEOBSU_" + code);
        
        // 종목 코드 설정
        jeobsuReal.OleProcedure("SetFieldData", "InBlock", "shcode", code);
        
        // 구독 시작
        bool result = jeobsuReal.OleFunction("AdviseRealData").AsType<bool>();
        
        if (result) {
            WriteLog(TLogLevel::INFO, "체결 구독 시작: " + code);
            return true;
        } else {
            WriteLog(TLogLevel::ERROR, "체결 구독 실패: " + code);
            return false;
        }
    }
    catch (const Exception& e) {
        HandleCOMError(e);
        WriteLog(TLogLevel::ERROR, "체결 구독 중 오류: " + e.Message);
        return false;
    }
}

// 모든 구독 해제
bool TXingAPIWrapper::UnsubscribeAll() {
    try {
        // Real 객체들의 구독 해제
        for (auto& pair : FXAReals) {
            try {
                pair.second.OleProcedure("UnadviseRealData");
            } catch (...) {}
        }
        
        FSubscribedCodes.clear();
        WriteLog(TLogLevel::INFO, "모든 구독 해제 완료");
        
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "구독 해제 중 오류: " + e.Message);
        return false;
    }
}

// 이벤트 처리 타이머
void __fastcall TXingAPIWrapper::OnEventProcessTimer(TObject* Sender) {
    try {
        // COM 이벤트 처리
        ProcessSessionEvent();
        
        // Query 결과 처리
        for (const auto& pair : FXAQueries) {
            ProcessQueryResult(pair.first);
        }
        
        // Real 데이터 처리
        for (const auto& pair : FXAReals) {
            ProcessRealData(pair.first);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "이벤트 처리 중 오류: " + e.Message);
    }
}

// 재연결 타이머
void __fastcall TXingAPIWrapper::OnReconnectTimer(TObject* Sender) {
    try {
        WriteLog(TLogLevel::INFO, "자동 재연결 시도 중...");
        
        if (Reconnect()) {
            StopReconnectTimer();
            FReconnectAttempts = 0;
        } else {
            FReconnectAttempts++;
            if (FReconnectAttempts >= FMaxReconnectAttempts) {
                StopReconnectTimer();
                WriteLog(TLogLevel::ERROR, "최대 재연결 시도 횟수 초과");
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "재연결 중 오류: " + e.Message);
    }
}

// 재연결
bool TXingAPIWrapper::Reconnect() {
    try {
        // 기존 연결 정리
        if (FIsConnected) {
            DisconnectServer();
        }
        
        // 잠시 대기
        Sleep(1000);
        
        // 다시 연결
        return ConnectServer(FIsReal);
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "재연결 실패: " + e.Message);
        return false;
    }
}

// 로깅
void TXingAPIWrapper::WriteLog(TLogLevel level, const AnsiString& message) {
    if (FDebugMode || level >= TLogLevel::INFO) {
        // 실제 로거 연결 필요
        AnsiString logMsg = AnsiString().sprintf("[%s] %s: %s",
            TUtils::FormatDateTime(Now()).c_str(),
            TUtils::LogLevelToString(level).c_str(),
            message.c_str());
        
        // 콘솔 출력 (디버그용)
        #ifdef _DEBUG
        OutputDebugStringA(logMsg.c_str());
        #endif
    }
}

// COM 에러 처리
void TXingAPIWrapper::HandleCOMError(const Exception& e) {
    TUtils::HandleException(e, "XingAPI COM");
    
    if (FOnError) {
        FOnError(this, e.Message);
    }
}

// 마지막 에러 메시지 조회
AnsiString TXingAPIWrapper::GetLastErrorMessage() {
    try {
        if (!FXASession.IsEmpty()) {
            return FXASession.OlePropertyGet("GetErrorMessage").AsType<AnsiString>();
        }
    } catch (...) {}
    
    return TUtils::GetLastErrorMessage();
}

// 재연결 타이머 시작
void TXingAPIWrapper::StartReconnectTimer() {
    if (FAutoReconnect && !FReconnectTimer->Enabled) {
        FReconnectTimer->Enabled = true;
    }
}

// 재연결 타이머 중지
void TXingAPIWrapper::StopReconnectTimer() {
    FReconnectTimer->Enabled = false;
}