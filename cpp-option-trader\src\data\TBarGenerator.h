#ifndef TBARGENERATOR_H
#define TBARGENERATOR_H

#include <vcl.h>
#include <System.hpp>
#include <Classes.hpp>
#include <map>
#include <vector>
#include <memory>
#include "DataTypes.h"

/**
 * TBarGenerator
 * 틱 데이터에서 분봉을 생성하는 클래스
 * Python의 MakeTimeBar 기능을 C++Builder로 포팅
 */
class TBarGenerator : public TObject {
private:
    // 분봉 데이터 저장
    std::map<AnsiString, std::vector<std::shared_ptr<TBarData>>> FBarDataMap;
    std::map<AnsiString, std::shared_ptr<TBarData>> FCurrentBars;
    
    // 분봉 간격 (분)
    int FBarInterval;
    
    // 봉 완성 알림
    TBarCompletedEvent FOnBarCompleted;
    TBarUpdatedEvent FOnBarUpdated;
    
    // 동기화
    TCriticalSection* FCriticalSection;
    
    // 내부 메서드
    std::shared_ptr<TBarData> CreateNewBar(const AnsiString& code, const TJeobsuData& tick);
    void UpdateCurrentBar(std::shared_ptr<TBarData> bar, const TJeobsuData& tick);
    bool ShouldCreateNewBar(std::shared_ptr<TBarData> currentBar, const TJeobsuData& tick);
    TDateTime GetBarTime(TDateTime tickTime);
    void WriteLog(TLogLevel level, const AnsiString& message);
    
public:
    // 생성자/소멸자
    __fastcall TBarGenerator(int barInterval = 1);
    __fastcall ~TBarGenerator();
    
    // 틱 데이터 입력
    void AddTick(const TJeobsuData& tick);
    void AddTick(const AnsiString& code, TDateTime time, double price, int volume);
    
    // 분봉 데이터 조회
    std::vector<std::shared_ptr<TBarData>> GetBars(const AnsiString& code, int count = 0);
    std::shared_ptr<TBarData> GetCurrentBar(const AnsiString& code);
    std::shared_ptr<TBarData> GetLastCompletedBar(const AnsiString& code);
    
    // 설정
    void SetBarInterval(int minutes) { FBarInterval = minutes; }
    int GetBarInterval() const { return FBarInterval; }
    
    // 데이터 관리
    void ClearData(const AnsiString& code = "");
    int GetBarCount(const AnsiString& code);
    std::vector<AnsiString> GetCodes();
    
    // 봉 완성 시간 예측
    TDateTime GetNextBarTime(const AnsiString& code);
    int GetSecondsToNextBar(const AnsiString& code);
    bool IsBarNearCompletion(const AnsiString& code, int secondsBefore = 15);
    
    // 수동 봉 완성
    void ForceCompleteBar(const AnsiString& code);
    void ForceCompleteAllBars();
    
    // 이벤트 속성
    __property TBarCompletedEvent OnBarCompleted = { read = FOnBarCompleted, write = FOnBarCompleted };
    __property TBarUpdatedEvent OnBarUpdated = { read = FOnBarUpdated, write = FOnBarUpdated };
};

// 유틸리티 함수들
class TBarUtils {
public:
    // 시간 관련
    static TDateTime AlignToMinute(TDateTime time, int minutes = 1);
    static TDateTime GetNextAlignedTime(TDateTime time, int minutes = 1);
    static bool IsSameBar(TDateTime time1, TDateTime time2, int minutes = 1);
    
    // 분봉 계산
    static double CalculateTypicalPrice(double high, double low, double close);
    static double CalculateWeightedPrice(double price, int volume, double totalValue, int totalVolume);
    
    // 분봉 검증
    static bool ValidateBar(const TBarData& bar);
    static void FixBarData(TBarData& bar);
    
    // 분봉 변환
    static std::vector<std::shared_ptr<TBarData>> ConvertTimeFrame(
        const std::vector<std::shared_ptr<TBarData>>& sourceBars, 
        int targetMinutes);
};

#endif // TBARGENERATOR_H