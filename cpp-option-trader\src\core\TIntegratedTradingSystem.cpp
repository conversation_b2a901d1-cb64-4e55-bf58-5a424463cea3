#include "TIntegratedTradingSystem.h"
#include "TOrderManager.h"
#include "TTrader.h"
#include "TOptionStrategy.h"
#include "../api/TXingAPIAdapter.h"
#include "../data/TDataLogger.h"
#include "../threading/TRealTimeDataThread.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>
#include <IniFiles.hpp>

// 생성자
__fastcall TIntegratedTradingSystem::TIntegratedTradingSystem() : TObject() {
    FTradingMode = TTradingMode::SIMULATION;
    FIsRunning = false;
    FIsInitialized = false;
    FStartTime = 0;
    FEndTime = 0;
    FConfigPath = "";
    FDataPath = "";
    
    // 기본 설정 경로
    FConfigPath = TUtils::GetConfigPath() + "trading_config.ini";
    FDataPath = TUtils::GetDataPath();
    
    WriteLog(TLogLevel::INFO, "System", "통합 거래 시스템 생성");
}

// 소멸자
__fastcall TIntegratedTradingSystem::~TIntegratedTradingSystem() {
    Shutdown();
    WriteLog(TLogLevel::INFO, "System", "통합 거래 시스템 종료");
}

// 초기화
bool TIntegratedTradingSystem::Initialize(const AnsiString& configPath) {
    try {
        if (FIsInitialized) {
            WriteLog(TLogLevel::WARNING, "System", "이미 초기화된 시스템");
            return true;
        }
        
        // 설정 파일 경로 설정
        if (!configPath.IsEmpty()) {
            FConfigPath = configPath;
        }
        
        // 설정 로드
        if (!LoadConfiguration()) {
            WriteLog(TLogLevel::ERROR, "System", "설정 로드 실패");
            return false;
        }
        
        // 컴포넌트 초기화
        InitializeComponents();
        
        FIsInitialized = true;
        WriteLog(TLogLevel::INFO, "System", "시스템 초기화 완료");
        
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "초기화 중 오류: " + e.Message);
        return false;
    }
}

// 종료
void TIntegratedTradingSystem::Shutdown() {
    try {
        if (FIsRunning) {
            StopTrading();
        }
        
        CleanupComponents();
        
        FIsInitialized = false;
        WriteLog(TLogLevel::INFO, "System", "시스템 종료 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "종료 중 오류: " + e.Message);
    }
}

// 거래 시작
bool TIntegratedTradingSystem::StartTrading(TTradingMode mode) {
    try {
        if (!FIsInitialized) {
            WriteLog(TLogLevel::ERROR, "System", "시스템이 초기화되지 않음");
            return false;
        }
        
        if (FIsRunning) {
            WriteLog(TLogLevel::WARNING, "System", "이미 거래 중");
            return true;
        }
        
        FTradingMode = mode;
        
        // XingAPI 연결 및 로그인
        if (!FXingAPI->IsConnected()) {
            bool isReal = (mode == TTradingMode::REAL_TIME);
            if (!FXingAPI->Connect(isReal)) {
                WriteLog(TLogLevel::ERROR, "System", "XingAPI 연결 실패");
                return false;
            }
        }
        
        if (!FXingAPI->IsLoggedIn()) {
            WriteLog(TLogLevel::ERROR, "System", "로그인 필요");
            return false;
        }
        
        // 계좌 정보 업데이트
        UpdateAccountInfo();
        
        // 컴포넌트 시작
        if (FDataThread) {
            FDataThread->Start();
        }
        
        if (FOrderManager) {
            FOrderManager->Start();
        }
        
        if (FTrader) {
            FTrader->Start();
        }
        
        if (FStrategy) {
            FStrategy->Start();
        }
        
        if (FDataLogger) {
            FDataLogger->Start();
        }
        
        FIsRunning = true;
        FStartTime = Now();
        
        WriteLog(TLogLevel::INFO, "System", 
            AnsiString().sprintf("거래 시작: %s 모드", 
                (mode == TTradingMode::REAL_TIME) ? "실거래" : "시뮬레이션"));
        
        // 이벤트 발생
        if (FOnSystemStarted) {
            FOnSystemStarted(this);
        }
        
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "거래 시작 중 오류: " + e.Message);
        return false;
    }
}

// 거래 중지
void TIntegratedTradingSystem::StopTrading() {
    try {
        if (!FIsRunning) {
            return;
        }
        
        // 컴포넌트 중지
        if (FStrategy) {
            FStrategy->Stop();
        }
        
        if (FTrader) {
            FTrader->Stop();
        }
        
        if (FOrderManager) {
            FOrderManager->Stop();
        }
        
        if (FDataThread) {
            FDataThread->Stop();
        }
        
        if (FDataLogger) {
            FDataLogger->Stop();
        }
        
        FIsRunning = false;
        FEndTime = Now();
        
        WriteLog(TLogLevel::INFO, "System", "거래 중지 완료");
        
        // 이벤트 발생
        if (FOnSystemStopped) {
            FOnSystemStopped(this);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "거래 중지 중 오류: " + e.Message);
    }
}

// 거래 일시정지
void TIntegratedTradingSystem::PauseTrading() {
    try {
        if (!FIsRunning) {
            return;
        }
        
        if (FStrategy) {
            FStrategy->Pause();
        }
        
        if (FOrderManager) {
            FOrderManager->Pause();
        }
        
        WriteLog(TLogLevel::INFO, "System", "거래 일시정지");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "일시정지 중 오류: " + e.Message);
    }
}

// 거래 재개
void TIntegratedTradingSystem::ResumeTrading() {
    try {
        if (!FIsRunning) {
            return;
        }
        
        if (FStrategy) {
            FStrategy->Resume();
        }
        
        if (FOrderManager) {
            FOrderManager->Resume();
        }
        
        WriteLog(TLogLevel::INFO, "System", "거래 재개");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "재개 중 오류: " + e.Message);
    }
}

// 거래 모드 설정
void TIntegratedTradingSystem::SetTradingMode(TTradingMode mode) {
    FTradingMode = mode;
    WriteLog(TLogLevel::INFO, "System", 
        AnsiString().sprintf("거래 모드 변경: %s", 
            (mode == TTradingMode::REAL_TIME) ? "실거래" : "시뮬레이션"));
}

// 실행 시간 조회
TDateTime TIntegratedTradingSystem::GetRunTime() const {
    if (FIsRunning) {
        return Now() - FStartTime;
    }
    else if (FEndTime > 0) {
        return FEndTime - FStartTime;
    }
    return 0;
}

// 전략 파라미터 설정
void TIntegratedTradingSystem::SetStrategyParams(const TStrategyParams& params) {
    FStrategyParams = params;
    
    if (FStrategy) {
        FStrategy->SetParams(params);
    }
    
    if (FOrderManager) {
        FOrderManager->UpdateStrategyParams(params);
    }
    
    WriteLog(TLogLevel::INFO, "System", "전략 파라미터 업데이트");
}

// 수동 주문
bool TIntegratedTradingSystem::PlaceOrder(const AnsiString& code, TTradeType tradeType, 
                                         int quantity, double price, TOrderType orderType) {
    try {
        if (!FIsRunning || !FOrderManager) {
            WriteLog(TLogLevel::ERROR, "Order", "시스템이 실행 중이 아님");
            return false;
        }
        
        AnsiString orderId = FOrderManager->PlaceOrder(code, tradeType, quantity, price, orderType);
        
        if (!orderId.IsEmpty()) {
            WriteLog(TLogLevel::INFO, "Order", 
                AnsiString().sprintf("수동 주문: %s, %s %d주, 가격: %.0f", 
                    code.c_str(), TUtils::TradeTypeToString(tradeType).c_str(), 
                    quantity, price));
            return true;
        }
        
        return false;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Order", "수동 주문 중 오류: " + e.Message);
        return false;
    }
}

// 주문 취소
bool TIntegratedTradingSystem::CancelOrder(const AnsiString& orderId) {
    try {
        if (!FOrderManager) {
            return false;
        }
        
        bool result = FOrderManager->CancelOrder(orderId);
        
        if (result) {
            WriteLog(TLogLevel::INFO, "Order", "주문 취소: " + orderId);
        }
        
        return result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Order", "주문 취소 중 오류: " + e.Message);
        return false;
    }
}

// 주문 수정
bool TIntegratedTradingSystem::ModifyOrder(const AnsiString& orderId, double newPrice) {
    try {
        if (!FOrderManager) {
            return false;
        }
        
        bool result = FOrderManager->ModifyOrder(orderId, newPrice);
        
        if (result) {
            WriteLog(TLogLevel::INFO, "Order", 
                AnsiString().sprintf("주문 수정: %s, 새 가격: %.0f", 
                    orderId.c_str(), newPrice));
        }
        
        return result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Order", "주문 수정 중 오류: " + e.Message);
        return false;
    }
}

// 포지션 조회
std::vector<std::shared_ptr<TPositionInfo>> TIntegratedTradingSystem::GetPositions() {
    if (FTrader) {
        return FTrader->GetPositions();
    }
    return std::vector<std::shared_ptr<TPositionInfo>>();
}

// 특정 포지션 조회
std::shared_ptr<TPositionInfo> TIntegratedTradingSystem::GetPosition(const AnsiString& code) {
    if (FTrader) {
        return FTrader->GetPosition(code);
    }
    return nullptr;
}

// 포지션 청산
bool TIntegratedTradingSystem::ClosePosition(const AnsiString& code) {
    try {
        if (!FTrader) {
            return false;
        }
        
        bool result = FTrader->ClosePosition(code);
        
        if (result) {
            WriteLog(TLogLevel::INFO, "Position", "포지션 청산: " + code);
        }
        
        return result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Position", "포지션 청산 중 오류: " + e.Message);
        return false;
    }
}

// 모든 포지션 청산
bool TIntegratedTradingSystem::CloseAllPositions() {
    try {
        if (!FTrader) {
            return false;
        }
        
        bool result = FTrader->CloseAllPositions();
        
        if (result) {
            WriteLog(TLogLevel::INFO, "Position", "모든 포지션 청산");
        }
        
        return result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Position", "전체 포지션 청산 중 오류: " + e.Message);
        return false;
    }
}

// 주문 조회
std::vector<std::shared_ptr<TOrderInfo>> TIntegratedTradingSystem::GetOrders() {
    if (FOrderManager) {
        return FOrderManager->GetOrders();
    }
    return std::vector<std::shared_ptr<TOrderInfo>>();
}

// 특정 주문 조회
std::shared_ptr<TOrderInfo> TIntegratedTradingSystem::GetOrder(const AnsiString& orderId) {
    if (FOrderManager) {
        return FOrderManager->GetOrder(orderId);
    }
    return nullptr;
}

// 활성 주문 조회
std::vector<std::shared_ptr<TOrderInfo>> TIntegratedTradingSystem::GetActiveOrders() {
    if (FOrderManager) {
        return FOrderManager->GetActiveOrders();
    }
    return std::vector<std::shared_ptr<TOrderInfo>>();
}

// 통계 정보
double TIntegratedTradingSystem::GetTotalProfitLoss() {
    if (FTrader) {
        return FTrader->GetTotalProfitLoss();
    }
    return 0.0;
}

double TIntegratedTradingSystem::GetTotalProfitRate() {
    if (FTrader) {
        return FTrader->GetTotalProfitRate();
    }
    return 0.0;
}

double TIntegratedTradingSystem::GetTotalAsset() {
    if (FTrader) {
        return FTrader->GetTotalAsset();
    }
    return 0.0;
}

int TIntegratedTradingSystem::GetTotalTradeCount() {
    if (FTrader) {
        return FTrader->GetTotalTradeCount();
    }
    return 0;
}

int TIntegratedTradingSystem::GetWinRate() {
    if (FTrader) {
        return FTrader->GetWinRate();
    }
    return 0;
}

// 설정 로드
bool TIntegratedTradingSystem::LoadConfiguration() {
    try {
        if (!TUtils::FileExists(FConfigPath)) {
            // 기본 설정 파일 생성
            SaveConfiguration();
            WriteLog(TLogLevel::INFO, "Config", "기본 설정 파일 생성");
        }
        
        std::unique_ptr<TIniFile> ini(new TIniFile(FConfigPath));
        
        // 전략 파라미터 로드
        FStrategyParams.stoch_k = ini->ReadInteger("Strategy", "stoch_k", 14);
        FStrategyParams.stoch_d = ini->ReadInteger("Strategy", "stoch_d", 3);
        FStrategyParams.cci_period = ini->ReadInteger("Strategy", "cci_period", 14);
        FStrategyParams.dmi_period = ini->ReadInteger("Strategy", "dmi_period", 14);
        FStrategyParams.adx_period = ini->ReadInteger("Strategy", "adx_period", 14);
        
        // 거래 파라미터
        FStrategyParams.profit_target = ini->ReadFloat("Trading", "profit_target", 0.03);
        FStrategyParams.stop_loss = ini->ReadFloat("Trading", "stop_loss", 0.02);
        FStrategyParams.position_size = ini->ReadInteger("Trading", "position_size", 1);
        
        // 데이터 경로
        FDataPath = ini->ReadString("System", "data_path", TUtils::GetDataPath());
        
        WriteLog(TLogLevel::INFO, "Config", "설정 로드 완료");
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Config", "설정 로드 중 오류: " + e.Message);
        return false;
    }
}

// 설정 저장
bool TIntegratedTradingSystem::SaveConfiguration() {
    try {
        // 디렉토리 생성
        AnsiString configDir = ExtractFileDir(FConfigPath);
        if (!TUtils::DirectoryExists(configDir)) {
            TUtils::CreateDirectoryRecursive(configDir);
        }
        
        std::unique_ptr<TIniFile> ini(new TIniFile(FConfigPath));
        
        // 전략 파라미터 저장
        ini->WriteInteger("Strategy", "stoch_k", FStrategyParams.stoch_k);
        ini->WriteInteger("Strategy", "stoch_d", FStrategyParams.stoch_d);
        ini->WriteInteger("Strategy", "cci_period", FStrategyParams.cci_period);
        ini->WriteInteger("Strategy", "dmi_period", FStrategyParams.dmi_period);
        ini->WriteInteger("Strategy", "adx_period", FStrategyParams.adx_period);
        
        // 거래 파라미터
        ini->WriteFloat("Trading", "profit_target", FStrategyParams.profit_target);
        ini->WriteFloat("Trading", "stop_loss", FStrategyParams.stop_loss);
        ini->WriteInteger("Trading", "position_size", FStrategyParams.position_size);
        
        // 시스템 설정
        ini->WriteString("System", "data_path", FDataPath);
        
        WriteLog(TLogLevel::INFO, "Config", "설정 저장 완료");
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Config", "설정 저장 중 오류: " + e.Message);
        return false;
    }
}

// 컴포넌트 초기화
void TIntegratedTradingSystem::InitializeComponents() {
    try {
        // XingAPI 어댑터 생성
        FXingAPI = std::make_unique<TXingAPIAdapter>();
        
        // 데이터 스레드 생성
        FDataThread = std::make_unique<TRealTimeDataThread>(FXingAPI.get());
        
        // 주문 관리자 생성
        FOrderManager = std::make_unique<TOrderManager>(FXingAPI.get());
        FOrderManager->Initialize(FStrategyParams);
        
        // 트레이더 생성
        FTrader = std::make_unique<TTrader>(FXingAPI.get(), FOrderManager.get());
        FTrader->Initialize(FStrategyParams);
        
        // 전략 생성
        FStrategy = std::make_unique<TOptionStrategy>(FDataThread.get(), FOrderManager.get());
        FStrategy->Initialize(FStrategyParams);
        
        // 데이터 로거 생성
        FDataLogger = std::make_unique<TDataLogger>(FDataPath);
        
        // 이벤트 연결
        if (FOrderManager) {
            FOrderManager->OnOrderStateChanged = OnOrderStateChanged;
        }
        
        if (FTrader) {
            FTrader->OnPositionUpdated = OnPositionUpdated;
        }
        
        if (FXingAPI) {
            FXingAPI->OnHoga = OnHogaReceived;
            FXingAPI->OnJeobsu = OnJeobsuReceived;
            FXingAPI->OnError = OnError;
        }
        
        WriteLog(TLogLevel::INFO, "System", "컴포넌트 초기화 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "컴포넌트 초기화 중 오류: " + e.Message);
        throw;
    }
}

// 컴포넌트 정리
void TIntegratedTradingSystem::CleanupComponents() {
    try {
        FDataLogger.reset();
        FStrategy.reset();
        FTrader.reset();
        FOrderManager.reset();
        FDataThread.reset();
        FXingAPI.reset();
        
        WriteLog(TLogLevel::INFO, "System", "컴포넌트 정리 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "System", "컴포넌트 정리 중 오류: " + e.Message);
    }
}

// 계좌 정보 업데이트
void TIntegratedTradingSystem::UpdateAccountInfo() {
    try {
        if (FXingAPI && FXingAPI->IsLoggedIn()) {
            FAccountInfo = FXingAPI->GetAccountInfo();
            
            if (FOnAccountUpdated) {
                FOnAccountUpdated(this);
            }
            
            WriteLog(TLogLevel::INFO, "Account", "계좌 정보 업데이트");
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Account", "계좌 정보 업데이트 중 오류: " + e.Message);
    }
}

// 이벤트 핸들러들
void __fastcall TIntegratedTradingSystem::OnOrderStateChanged(TObject* Sender, const TOrderInfo& order) {
    WriteLog(TLogLevel::INFO, "Order", 
        AnsiString().sprintf("주문 상태 변경: %s -> %s", 
            order.order_id.c_str(), TUtils::OrderStateToString(order.state).c_str()));
}

void __fastcall TIntegratedTradingSystem::OnPositionUpdated(TObject* Sender, const TPositionInfo& position) {
    WriteLog(TLogLevel::INFO, "Position", 
        AnsiString().sprintf("포지션 업데이트: %s, 수량: %d, 손익: %.2f", 
            position.code.c_str(), position.quantity, position.unrealized_pnl));
}

void __fastcall TIntegratedTradingSystem::OnHogaReceived(TObject* Sender, const THogaData& hoga) {
    // 호가 데이터 처리 (필요시 로깅)
}

void __fastcall TIntegratedTradingSystem::OnJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu) {
    // 체결 데이터 처리 (필요시 로깅)
}

void __fastcall TIntegratedTradingSystem::OnError(TObject* Sender, const AnsiString& error) {
    WriteLog(TLogLevel::ERROR, "API", "XingAPI 오류: " + error);
}

// 로깅
void TIntegratedTradingSystem::WriteLog(TLogLevel level, const AnsiString& category, const AnsiString& message) {
    try {
        AnsiString logMsg = AnsiString().sprintf("[%s] %s: %s", 
            FormatDateTime("hh:nn:ss", Now()).c_str(),
            category.c_str(), 
            message.c_str());
        
        // 파일 로깅
        if (FDataLogger) {
            FDataLogger->WriteLog(level, category, message);
        }
        
        // 디버그 출력
        #ifdef _DEBUG
        OutputDebugStringA(logMsg.c_str());
        #endif
    }
    catch (...) {
        // 로깅 실패는 무시
    }
}

// 로그 조회
std::vector<std::shared_ptr<TLogMessage>> TIntegratedTradingSystem::GetLogs(TLogLevel minLevel) {
    if (FDataLogger) {
        return FDataLogger->GetLogs(minLevel);
    }
    return std::vector<std::shared_ptr<TLogMessage>>();
}