import sys
import time
from datetime import datetime
from PyQt5.QAxContainer import QAxWidget
from PyQt5.QtCore import QEvent<PERSON>oop, QTimer, QObject
from PyQt5.QtWidgets import QApplication

class OptionMonitor(QObject):
    def __init__(self):
        super().__init__()
        self.kiwoom = QAxWidget("KHOPENAPI.KHOpenAPICtrl.1")
        self.kiwoom.OnEventConnect.connect(self._on_login)
        self.kiwoom.OnReceiveRealData.connect(self._on_real_data)
        self.login_loop = QEventLoop()
        self.market_open = False
        self.option_codes = []
        self.siga_dict = {}  # 종목코드: 시가
        self.watch_list = set()
        self.screen_no = "9000"

        # 키움 옵션 코드 표기법 매핑
        self.OptFutY = {
            '2025': 'W', '2026': '6', '2027': '7', '2028': '8', '2029': '9',
            '2030': '0', '2031': '1', '2032': '2', '2033': '3', '2034': '4',
            '2035': '5', '2036': '6', '2037': '7', '2038': '8'
        }
        self.OptFutM = {
            '1': '1', '2': '2', '3': '3', '4': '4', '5': '5', '6': '6',
            '7': '7', '8': '8', '9': '9', '10': 'A', '11': 'B', '12': 'C'
        }

    def login(self):
        self.kiwoom.dynamicCall("CommConnect()")
        self.login_loop.exec_()

    def _on_login(self, err_code):
        if err_code == 0:
            print("로그인 성공")
            self._register_market_time()
            self._after_market_open()
        else:
            print("로그인 실패:", err_code)
            sys.exit()
        self.login_loop.quit()

    def _register_market_time(self):
        # 장운영구분(215), 시간(20) 실시간 등록
        self.kiwoom.dynamicCall(
            "SetRealReg(QString, QString, QString, QString)",
            "9999", "", "215;20", "1"
        )
        print("장 시작 감지 대기 중...")

    def _on_real_data(self, code, real_type, real_data):
        if real_type == "장시작시간":
            market_status = self.kiwoom.dynamicCall("GetCommRealData(QString, int)", code, 215)
            if market_status == "3" and not self.market_open:
                self.market_open = True
                print("장 시작 감지!")
                self._after_market_open()
        elif real_type == "선옵시세" and code in self.option_codes:
            open_price = abs(int(self.kiwoom.dynamicCall("GetCommRealData(QString, int)", code, 16)))
            if open_price > 0 and code not in self.siga_dict:
                self.siga_dict[code] = open_price
                print(f"[시가] {code}: {open_price/100:.2f}")
                self._dynamic_filter_and_update()

    def _after_market_open(self):
        print("장 시작 후 옵션 코드 조회 시작...")

        # 1. ATM 산출 (장 시작 후) - 에러 처리 추가
        try:
            atm_price = int(self.kiwoom.dynamicCall("GetOptionATM()"))
            print("ATM 행사가격:", atm_price)
            base_price = atm_price / 100
            print("ATM 행사가격 / 100:", base_price)
        except Exception as e:
            print(f"ATM 조회 실패: {e}")
            return

        try:
            month_list = self.kiwoom.dynamicCall("GetMonthList()").split(';')
            raw_month = min(month_list)  # 가장 가까운 월물
            print("원본 월물:", raw_month)

            # 키움 표기법으로 월물 변환 (예: "202508" -> "W8")
            if len(raw_month) == 6:
                year = raw_month[:4]
                month_num = str(int(raw_month[4:6]))  # "08" -> "8"

                if year in self.OptFutY and month_num in self.OptFutM:
                    year_code = self.OptFutY[year]
                    month_code = self.OptFutM[month_num]
                    print(f"년도코드: {year} -> {year_code}, 월코드: {month_num} -> {month_code}")
                else:
                    print(f"지원하지 않는 년도/월: {year}/{month_num}")
                    return
            else:
                print(f"잘못된 월물 형식: {raw_month}")
                return

        except Exception as e:
            print(f"월물 조회 실패: {e}")
            return

        # 2. ±5개 행사가씩 콜/풋 종목코드 직접 생성 (키움 표기법)
        codes = []
        for i in range(-5, 6):
            strike_price = base_price + i * 2.5
            # 행사가에서 소수점 제거 (예: 247.5 -> 2475)
            strike_int = int(strike_price * 10)
            print(f"행사가: {strike_price} -> {strike_int}")

            # 콜옵션 종목코드 생성: 201 + 년도코드 + 월코드 + 행사가
            call_code = f"201{year_code}{month_code}{strike_int}"
            codes.append(call_code)
            print(f"✓ 콜옵션 코드: {strike_price} -> {call_code}")

            # 풋옵션 종목코드 생성: 301 + 년도코드 + 월코드 + 행사가
            put_code = f"301{year_code}{month_code}{strike_int}"
            codes.append(put_code)
            print(f"✓ 풋옵션 코드: {strike_price} -> {put_code}")
        self.option_codes = codes
        print(f"옵션 종목코드 총 {len(codes)}개 조회 완료:", self.option_codes)

        # 3. 실시간 시세 등록 (22개 종목)
        code_list = ";".join(self.option_codes)
        self.kiwoom.dynamicCall(
            "SetRealReg(QString, QString, QString, QString)",
            self.screen_no, code_list, "10;11;16", "0"
        )
        print("22개 옵션 실시간 등록 완료")

    def _dynamic_filter_and_update(self):
        # 1.00~5.00(100~500) 구간 시가 종목만 감시 대상으로 선정
        filtered = [c for c, p in self.siga_dict.items() if 100 <= p <= 500]
        if set(filtered) != self.watch_list:
            self.watch_list = set(filtered)
            # 실시간 등록 갱신: 감시 대상만 남기기
            code_list = ";".join(filtered)
            self.kiwoom.dynamicCall("SetRealReg(QString, QString, QString, QString)",
                                    self.screen_no, code_list, "10;11;16", "0")
            print(f"감시 대상 종목({len(filtered)}개) 실시간 등록 갱신:", filtered)

def main():
    app = QApplication(sys.argv)
    monitor = OptionMonitor()
    monitor.login()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
