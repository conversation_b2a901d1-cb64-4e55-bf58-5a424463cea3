#include "TOptionStrategy.h"
#include "../utils/TUtils.h"
#include <System.SysUtils.hpp>
#include <algorithm>
#include <numeric>
#include <cmath>

// 생성자
__fastcall TOptionStrategy::TOptionStrategy() {
    FMaxDataPoints = 200;  // 최대 200개 데이터 포인트
    FIsEnabled = false;
    
    FCriticalSection = new TCriticalSection();
}

// 소멸자
__fastcall TOptionStrategy::~TOptionStrategy() {
    ClearAllData();
    delete FCriticalSection;
}

// 초기화
void TOptionStrategy::Initialize(const TStrategyParams& params) {
    TLockGuard lock(FCriticalSection);
    
    FParams = params;
    FIsEnabled = true;
    
    WriteLog(TLogLevel::INFO, "전략 엔진 초기화 완료");
}

// 전략 파라미터 설정
void TOptionStrategy::SetStrategyParams(const TStrategyParams& params) {
    TLockGuard lock(FCriticalSection);
    FParams = params;
    
    // 캐시된 지표 데이터 무효화
    FStochasticCache.clear();
    FCCICache.clear();
    FDMICache.clear();
    
    WriteLog(TLogLevel::INFO, "전략 파라미터 업데이트");
}

// 캔들 데이터 추가
void TOptionStrategy::AddCandle(const AnsiString& code, const TCandleData& candle) {
    TLockGuard lock(FCriticalSection);
    
    AddPriceData(code, candle);
    
    // 충분한 데이터가 있으면 지표 계산
    if (HasSufficientData(code, std::max({FParams.stoch_k_period, FParams.cci_period, FParams.dmi_period}))) {
        CalculateAllIndicators(code);
    }
    
    FLastUpdateTimes[code] = Now();
}

// 가격 데이터 추가
void TOptionStrategy::AddPrice(const AnsiString& code, double price, int volume) {
    TCandleData candle;
    candle.timestamp = Now();
    candle.close = price;
    candle.volume = volume;
    
    // 기존 캔들이 있으면 업데이트, 없으면 새로 생성
    if (FPriceData.find(code) != FPriceData.end() && !FPriceData[code].empty()) {
        TCandleData& lastCandle = FPriceData[code].back();
        
        // 같은 분 내라면 기존 캔들 업데이트
        if (abs(candle.timestamp - lastCandle.timestamp) < (1.0 / (24 * 60))) {
            lastCandle.close = price;
            lastCandle.high = std::max(lastCandle.high, price);
            lastCandle.low = std::min(lastCandle.low, price);
            lastCandle.volume += volume;
        } else {
            // 새 캔들 생성
            candle.open = lastCandle.close;
            candle.high = price;
            candle.low = price;
            AddCandle(code, candle);
        }
    } else {
        // 첫 번째 캔들
        candle.open = price;
        candle.high = price;
        candle.low = price;
        AddCandle(code, candle);
    }
}

// 현재 캔들 업데이트
void TOptionStrategy::UpdateCurrentCandle(const AnsiString& code, double price, int volume) {
    TLockGuard lock(FCriticalSection);
    
    if (FPriceData.find(code) != FPriceData.end() && !FPriceData[code].empty()) {
        TCandleData& lastCandle = FPriceData[code].back();
        lastCandle.close = price;
        lastCandle.high = std::max(lastCandle.high, price);
        lastCandle.low = std::min(lastCandle.low, price);
        lastCandle.volume += volume;
        
        // 지표 재계산
        CalculateAllIndicators(code);
    }
}

// 매수 신호 생성
bool TOptionStrategy::GenerateBuySignal(const AnsiString& code) {
    if (!FIsEnabled || !IsDataSufficient(code)) {
        return false;
    }
    
    try {
        bool buyCondition = CheckBuyConditions(code);
        
        // 이전 신호와 비교하여 새로운 신호인지 확인
        bool isNewSignal = false;
        if (FLastBuySignals.find(code) != FLastBuySignals.end()) {
            isNewSignal = (buyCondition && !FLastBuySignals[code]);
        } else {
            isNewSignal = buyCondition;
        }
        
        FLastBuySignals[code] = buyCondition;
        
        if (isNewSignal) {
            WriteLog(TLogLevel::INFO, "매수 신호 발생: " + code);
            
            if (FOnBuySignal) {
                FOnBuySignal(this);
            }
        }
        
        return isNewSignal;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "매수 신호 생성 오류: " + e.Message);
        return false;
    }
}

// 매도 신호 생성
bool TOptionStrategy::GenerateSellSignal(const AnsiString& code) {
    if (!FIsEnabled || !IsDataSufficient(code)) {
        return false;
    }
    
    try {
        bool sellCondition = CheckSellConditions(code);
        
        bool isNewSignal = false;
        if (FLastSellSignals.find(code) != FLastSellSignals.end()) {
            isNewSignal = (sellCondition && !FLastSellSignals[code]);
        } else {
            isNewSignal = sellCondition;
        }
        
        FLastSellSignals[code] = sellCondition;
        
        if (isNewSignal) {
            WriteLog(TLogLevel::INFO, "매도 신호 발생: " + code);
            
            if (FOnSellSignal) {
                FOnSellSignal(this);
            }
        }
        
        return isNewSignal;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "매도 신호 생성 오류: " + e.Message);
        return false;
    }
}

// 포지션 진입 여부
bool TOptionStrategy::ShouldEnterPosition(const AnsiString& code) {
    return GenerateBuySignal(code) && IsTrendFavorable(code) && IsVolatilityAcceptable(code);
}

// 포지션 청산 여부
bool TOptionStrategy::ShouldExitPosition(const AnsiString& code) {
    return GenerateSellSignal(code);
}

// Stochastic 계산
TStochasticData TOptionStrategy::CalculateStochastic(const AnsiString& code) {
    TStochasticData result;
    
    if (!HasSufficientData(code, FParams.stoch_k_period)) {
        return result;
    }
    
    try {
        auto& candles = FPriceData[code];
        int period = FParams.stoch_k_period;
        
        if (candles.size() < period) {
            return result;
        }
        
        // 최근 period 개의 데이터에서 최고가/최저가 찾기
        double highest = candles[candles.size() - period].high;
        double lowest = candles[candles.size() - period].low;
        
        for (int i = candles.size() - period; i < candles.size(); i++) {
            highest = std::max(highest, candles[i].high);
            lowest = std::min(lowest, candles[i].low);
        }
        
        // %K 계산
        double currentClose = candles.back().close;
        if (highest != lowest) {
            result.k_percent = ((currentClose - lowest) / (highest - lowest)) * 100.0;
        }
        
        // %D 계산 (단순 이동평균)
        if (candles.size() >= period + FParams.stoch_d_period - 1) {
            std::vector<double> kValues;
            for (int i = 0; i < FParams.stoch_d_period; i++) {
                // 이전 %K 값들 계산 (간단화)
                kValues.push_back(result.k_percent);  // 실제로는 각 기간별로 계산해야 함
            }
            result.d_percent = TUtils::CalculateMean(kValues);
        }
        
        // 골든크로스/데드크로스 판단
        // 이전 값들과 비교 필요 (간단화)
        result.is_golden_cross = (result.k_percent > result.d_percent) && 
                                (result.k_percent < FParams.stoch_buy_threshold);
        result.is_dead_cross = (result.k_percent < result.d_percent) && 
                              (result.k_percent > FParams.stoch_sell_threshold);
        
        FStochasticCache[code] = result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Stochastic 계산 오류: " + e.Message);
    }
    
    return result;
}

// CCI 계산
TCCIData TOptionStrategy::CalculateCCI(const AnsiString& code) {
    TCCIData result;
    
    if (!HasSufficientData(code, FParams.cci_period)) {
        return result;
    }
    
    try {
        auto& candles = FPriceData[code];
        int period = FParams.cci_period;
        
        if (candles.size() < period) {
            return result;
        }
        
        // Typical Price 계산
        std::vector<double> typicalPrices;
        for (int i = candles.size() - period; i < candles.size(); i++) {
            double tp = CalculateTypicalPrice(candles[i]);
            typicalPrices.push_back(tp);
        }
        
        // SMA 계산
        double sma = TUtils::CalculateMean(typicalPrices);
        
        // Mean Deviation 계산
        double meanDeviation = 0.0;
        for (double tp : typicalPrices) {
            meanDeviation += abs(tp - sma);
        }
        meanDeviation /= period;
        
        // CCI 계산
        if (meanDeviation != 0.0) {
            double currentTP = CalculateTypicalPrice(candles.back());
            result.cci_value = (currentTP - sma) / (0.015 * meanDeviation);
        }
        
        // 매매 신호 판단
        result.is_buy_signal = result.cci_value > FParams.cci_buy_threshold;
        result.is_sell_signal = result.cci_value < FParams.cci_sell_threshold;
        
        FCCICache[code] = result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "CCI 계산 오류: " + e.Message);
    }
    
    return result;
}

// DMI 계산
TDMIData TOptionStrategy::CalculateDMI(const AnsiString& code) {
    TDMIData result;
    
    if (!HasSufficientData(code, FParams.dmi_period)) {
        return result;
    }
    
    try {
        auto& candles = FPriceData[code];
        int period = FParams.dmi_period;
        
        if (candles.size() < period + 1) {
            return result;
        }
        
        std::vector<double> trueRanges;
        std::vector<double> plusDMs;
        std::vector<double> minusDMs;
        
        // TR, +DM, -DM 계산
        for (int i = candles.size() - period; i < candles.size(); i++) {
            if (i > 0) {
                double tr = CalculateTrueRange(candles[i], candles[i-1]);
                double plusDM = CalculateDirectionalMovement(candles[i], candles[i-1], true);
                double minusDM = CalculateDirectionalMovement(candles[i], candles[i-1], false);
                
                trueRanges.push_back(tr);
                plusDMs.push_back(plusDM);
                minusDMs.push_back(minusDM);
            }
        }
        
        // 평활화된 값들 계산 (EMA 사용)
        double smoothedTR = CalculateEMA(trueRanges, period);
        double smoothedPlusDM = CalculateEMA(plusDMs, period);
        double smoothedMinusDM = CalculateEMA(minusDMs, period);
        
        // DI+ 및 DI- 계산
        if (smoothedTR != 0.0) {
            result.di_plus = (smoothedPlusDM / smoothedTR) * 100.0;
            result.di_minus = (smoothedMinusDM / smoothedTR) * 100.0;
        }
        
        // ADX 계산
        if (result.di_plus + result.di_minus != 0.0) {
            double dx = abs(result.di_plus - result.di_minus) / (result.di_plus + result.di_minus) * 100.0;
            result.adx = dx;  // 실제로는 DX의 이동평균
        }
        
        // 추세 판단
        result.trend_strong = result.adx > FParams.adx_threshold;
        result.bullish_trend = result.di_plus > result.di_minus;
        
        FDMICache[code] = result;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "DMI 계산 오류: " + e.Message);
    }
    
    return result;
}

// Typical Price 계산
double TOptionStrategy::CalculateTypicalPrice(const TCandleData& candle) {
    return (candle.high + candle.low + candle.close) / 3.0;
}

// True Range 계산
double TOptionStrategy::CalculateTrueRange(const TCandleData& current, const TCandleData& previous) {
    double tr1 = current.high - current.low;
    double tr2 = abs(current.high - previous.close);
    double tr3 = abs(current.low - previous.close);
    
    return std::max({tr1, tr2, tr3});
}

// Directional Movement 계산
double TOptionStrategy::CalculateDirectionalMovement(const TCandleData& current, 
                                                   const TCandleData& previous, bool isPlus) {
    double upMove = current.high - previous.high;
    double downMove = previous.low - current.low;
    
    if (isPlus) {
        return (upMove > downMove && upMove > 0) ? upMove : 0.0;
    } else {
        return (downMove > upMove && downMove > 0) ? downMove : 0.0;
    }
}

// EMA 계산
double TOptionStrategy::CalculateEMA(const std::vector<double>& data, int period) {
    if (data.empty()) return 0.0;
    
    double alpha = 2.0 / (period + 1);
    double ema = data[0];
    
    for (int i = 1; i < data.size(); i++) {
        ema = alpha * data[i] + (1 - alpha) * ema;
    }
    
    return ema;
}

// SMA 계산
double TOptionStrategy::CalculateSMA(const std::vector<double>& data, int period) {
    if (data.size() < period) return 0.0;
    
    double sum = 0.0;
    for (int i = data.size() - period; i < data.size(); i++) {
        sum += data[i];
    }
    
    return sum / period;
}

// 매수 조건 확인
bool TOptionStrategy::CheckBuyConditions(const AnsiString& code) {
    TStochasticData stoch = GetStochastic(code);
    TCCIData cci = GetCCI(code);
    TDMIData dmi = GetDMI(code);
    
    // 매수 조건: DI+ > DI-, CCI > 임계값, ADX > 25, Stochastic 골든크로스
    bool condition1 = dmi.bullish_trend && dmi.trend_strong;
    bool condition2 = cci.is_buy_signal;
    bool condition3 = stoch.is_golden_cross || (stoch.k_percent < FParams.stoch_buy_threshold);
    
    return condition1 && condition2 && condition3;
}

// 매도 조건 확인
bool TOptionStrategy::CheckSellConditions(const AnsiString& code) {
    TStochasticData stoch = GetStochastic(code);
    TCCIData cci = GetCCI(code);
    TDMIData dmi = GetDMI(code);
    
    // 매도 조건: DI+ < DI-, CCI < 임계값, Stochastic 데드크로스
    bool condition1 = !dmi.bullish_trend;
    bool condition2 = cci.is_sell_signal;
    bool condition3 = stoch.is_dead_cross || (stoch.k_percent > FParams.stoch_sell_threshold);
    
    return condition1 || condition2 || condition3;
}

// 추세 유리함 확인
bool TOptionStrategy::IsTrendFavorable(const AnsiString& code) {
    TDMIData dmi = GetDMI(code);
    return dmi.trend_strong && dmi.bullish_trend;
}

// 변동성 허용 범위 확인
bool TOptionStrategy::IsVolatilityAcceptable(const AnsiString& code) {
    double volatility = GetAverageVolatility(code, 20);
    return volatility > 0.5 && volatility < 5.0;  // 0.5% ~ 5% 변동성
}

// 가격 데이터 추가 (내부)
void TOptionStrategy::AddPriceData(const AnsiString& code, const TCandleData& candle) {
    auto& data = FPriceData[code];
    data.push_back(candle);
    
    // 최대 데이터 포인트 초과 시 오래된 데이터 제거
    if (data.size() > FMaxDataPoints) {
        data.pop_front();
    }
    
    // 종가 데이터도 업데이트
    auto& closePrices = FClosePrices[code];
    closePrices.push_back(candle.close);
    if (closePrices.size() > FMaxDataPoints) {
        closePrices.pop_front();
    }
}

// 충분한 데이터 여부 확인
bool TOptionStrategy::HasSufficientData(const AnsiString& code, int requiredPeriod) {
    auto it = FPriceData.find(code);
    return (it != FPriceData.end()) && (it->second.size() >= requiredPeriod);
}

// 지표 조회 메서드들
TStochasticData TOptionStrategy::GetStochastic(const AnsiString& code) {
    auto it = FStochasticCache.find(code);
    if (it != FStochasticCache.end()) {
        return it->second;
    }
    return CalculateStochastic(code);
}

TCCIData TOptionStrategy::GetCCI(const AnsiString& code) {
    auto it = FCCICache.find(code);
    if (it != FCCICache.end()) {
        return it->second;
    }
    return CalculateCCI(code);
}

TDMIData TOptionStrategy::GetDMI(const AnsiString& code) {
    auto it = FDMICache.find(code);
    if (it != FDMICache.end()) {
        return it->second;
    }
    return CalculateDMI(code);
}

// 모든 지표 계산
void TOptionStrategy::CalculateAllIndicators(const AnsiString& code) {
    CalculateStochastic(code);
    CalculateCCI(code);
    CalculateDMI(code);
    
    if (FOnIndicatorUpdated) {
        FOnIndicatorUpdated(this);
    }
}

// 평균 변동성 계산
double TOptionStrategy::GetAverageVolatility(const AnsiString& code, int period) {
    if (!HasSufficientData(code, period + 1)) {
        return 0.0;
    }
    
    auto& candles = FPriceData[code];
    std::vector<double> changes;
    
    for (int i = candles.size() - period; i < candles.size() - 1; i++) {
        double change = abs((candles[i+1].close - candles[i].close) / candles[i].close) * 100.0;
        changes.push_back(change);
    }
    
    return TUtils::CalculateMean(changes);
}

// 데이터 상태 조회
bool TOptionStrategy::IsDataSufficient(const AnsiString& code) {
    int maxPeriod = std::max({FParams.stoch_k_period, FParams.cci_period, FParams.dmi_period});
    return HasSufficientData(code, maxPeriod);
}

// 모든 데이터 정리
void TOptionStrategy::ClearAllData() {
    TLockGuard lock(FCriticalSection);
    
    FPriceData.clear();
    FClosePrices.clear();
    FStochasticCache.clear();
    FCCICache.clear();
    FDMICache.clear();
    FLastUpdateTimes.clear();
    FLastBuySignals.clear();
    FLastSellSignals.clear();
}

// 로깅
void TOptionStrategy::WriteLog(TLogLevel level, const AnsiString& message) {
    // 실제 로거 연결 시 구현
    #ifdef _DEBUG
    AnsiString logMsg = AnsiString().sprintf("[OptionStrategy] %s", message.c_str());
    OutputDebugStringA(logMsg.c_str());
    #endif
}