# SBCPP.png UI 분석 및 VCL Form 설계

## UI 구성 요소 분석

### 1. 상단 정보 패널
- **달일설정순익**: 1,172,500 (총 수익 표시)
- **평가손익**: 0 (현재 평가손익)
- **수익률**: 0.00 (수익률 %)
- **매입금액**: 0 (매입 금액)
- **평가총금액**: 0 (총 평가금액)

### 2. 중앙 테이블 헤더
- **종목코드**: 옵션 종목 코드
- **보유수량**: 보유 수량
- **매입가**: 매입 가격
- **현재가**: 현재 가격
- **수익률**: 개별 종목 수익률
- **평가금액**: 평가 금액
- **매입금액**: 매입 금액
- **평가금액**: 최종 평가 금액
- **구분**: 거래 구분

### 3. 거래 내역 테이블
- 시간별 거래 내역 표시
- 매수/매도 정보
- 체결 정보 및 수량
- 실시간 업데이트

### 4. 우측 상세 정보 패널
- 상세 거래 정보
- 종목별 세부 데이터
- 호가 정보 등

## C++Builder VCL Form 설계

### TMainForm 클래스 구성

```cpp
class TMainForm : public TForm
{
__published:
    // 상단 정보 패널
    TPanel *InfoPanel;
    TLabel *lblTotalProfit;      // 달일설정순익
    TLabel *lblEvalProfit;       // 평가손익
    TLabel *lblProfitRate;       // 수익률
    TLabel *lblPurchaseAmount;   // 매입금액
    TLabel *lblTotalEvalAmount;  // 평가총금액
    
    // 메인 그리드
    TStringGrid *MainGrid;       // 보유 종목 정보
    TStringGrid *TradeGrid;      // 거래 내역
    
    // 우측 패널
    TPanel *DetailPanel;
    TMemo *DetailMemo;           // 상세 정보 표시
    
    // 메뉴 및 툴바
    TMainMenu *MainMenu;
    TToolBar *ToolBar;
    TButton *btnStart;           // 시작 버튼
    TButton *btnStop;            // 정지 버튼
    TButton *btnSettings;        // 설정 버튼
    
    // 타이머
    TTimer *RefreshTimer;        // UI 갱신 타이머
    
    // 이벤트 핸들러
    void __fastcall FormCreate(TObject *Sender);
    void __fastcall FormDestroy(TObject *Sender);
    void __fastcall btnStartClick(TObject *Sender);
    void __fastcall btnStopClick(TObject *Sender);
    void __fastcall btnSettingsClick(TObject *Sender);
    void __fastcall RefreshTimerTimer(TObject *Sender);
    void __fastcall MainGridDrawCell(TObject *Sender, int ACol, int ARow, 
                                     TRect &Rect, TGridDrawState State);

private:
    // 멤버 변수
    std::unique_ptr<TIntegratedTradingSystem> FTradingSystem;
    std::unique_ptr<TOrderManager> FOrderManager;
    std::unique_ptr<TXingAPIWrapper> FXingAPI;
    
    // UI 업데이트 메서드
    void UpdateTotalInfo();
    void UpdateHoldingsGrid();
    void UpdateTradeGrid();
    void UpdateDetailInfo();
    
    // 그리드 설정
    void SetupMainGrid();
    void SetupTradeGrid();
    
    // 색상 관리
    TColor GetProfitColor(double profitRate);
    
public:
    __fastcall TMainForm(TComponent* Owner);
    
    // 외부 호출 메서드
    void AddTradeRecord(const String& time, const String& code, 
                       const String& type, int quantity, double price);
    void UpdateHolding(const String& code, int quantity, double price);
    void ShowMessage(const String& message, int level);
};
```

### Form 레이아웃 (.dfm 파일 구성)

```pascal
object MainForm: TMainForm
  Left = 0
  Top = 0
  Caption = 'LS\uC99D\uAD8C WeeklyOption (\uC8FC\uAC04\uC635\uC158 \uB9E4\uB9E4) | 2025-05-28 13:06:04'
  ClientHeight = 600
  ClientWidth = 900
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  
  object InfoPanel: TPanel
    Left = 0
    Top = 0
    Width = 900
    Height = 60
    Align = alTop
    BevelOuter = bvRaised
    Color = clGray
    TabOrder = 0
    
    object lblTotalProfit: TLabel
      Left = 10
      Top = 10
      Width = 80
      Height = 13
      Caption = '\uB2EC\uC77C\uC124\uC815\uC21C\uC775'
      Color = clWhite
      Font.Style = [fsBold]
    end
    
    // ... 기타 라벨들
  end
  
  object MainGrid: TStringGrid
    Left = 0
    Top = 60
    Width = 600
    Height = 300
    Align = alClient
    ColCount = 9
    RowCount = 2
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
    TabOrder = 1
    OnDrawCell = MainGridDrawCell
  end
  
  object TradeGrid: TStringGrid
    Left = 0
    Top = 360
    Width = 600
    Height = 240
    Align = alBottom
    ColCount = 8
    RowCount = 2
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
    TabOrder = 2
  end
  
  object DetailPanel: TPanel
    Left = 600
    Top = 60
    Width = 300
    Height = 540
    Align = alRight
    BevelOuter = bvRaised
    TabOrder = 3
    
    object DetailMemo: TMemo
      Left = 1
      Top = 1
      Width = 298
      Height = 538
      Align = alClient
      ScrollBars = ssVertical
      TabOrder = 0
    end
  end
  
  object RefreshTimer: TTimer
    Interval = 1000
    OnTimer = RefreshTimerTimer
    Left = 850
    Top = 10
  end
end
```

## UI 업데이트 로직

### 1. 실시간 데이터 표시
- RefreshTimer를 통한 주기적 UI 갱신
- 스레드 안전한 데이터 접근
- 색상 코딩을 통한 수익/손실 구분

### 2. 그리드 관리
- StringGrid의 OnDrawCell 이벤트로 셀 색상 제어
- 동적 행 추가/삭제
- 정렬 및 필터링 기능

### 3. 이벤트 기반 업데이트
- COM 이벤트를 VCL 이벤트로 변환
- 메시지 큐를 통한 스레드 간 통신
- UI 응답성 보장을 위한 비동기 처리