#include "TLSXingAPI.h"
#include <System.SysUtils.hpp>
#include <System.DateUtils.hpp>

// 생성자
__fastcall TLSXingAPI::TLSXingAPI() : TObject() {
    FConnectionState = TConnectionState::DISCONNECTED;
    FXASession = nullptr;
    FIsRealServer = false;
    FServerAddress = "api.ls-sec.co.kr";
    FServerPort = 20001;
    FAutoReconnect = true;
    FReconnectAttempts = 0;
    FMaxReconnectAttempts = 3;
    FRequestCounter = 0;
    
    // 동기화 객체 생성
    FRequestLock = new TCriticalSection();
    FRealDataLock = new TCriticalSection();
    
    // 타이머 생성
    FRequestTimer = new TTimer(nullptr);
    FRequestTimer->Interval = 200;  // 200ms
    FRequestTimer->OnTimer = OnRequestTimer;
    FRequestTimer->Enabled = false;
    
    FReconnectTimer = new TTimer(nullptr);
    FReconnectTimer->Interval = 5000;  // 5초
    FReconnectTimer->OnTimer = OnReconnectTimer;
    FReconnectTimer->Enabled = false;
    
    // 기본 설정 경로
    FConfigPath = TUtils::GetConfigPath() + "ls_config.ini";
    
    InitializeComponents();
    WriteLog(TLogLevel::INFO, "TLSXingAPI 초기화 완료");
}

// 소멸자
__fastcall TLSXingAPI::~TLSXingAPI() {
    StopReconnectTimer();
    
    if (IsLoggedIn()) {
        Logout();
    }
    
    if (IsConnected()) {
        DisconnectServer();
    }
    
    CleanupComponents();
    
    delete FRequestTimer;
    delete FReconnectTimer;
    delete FRequestLock;
    delete FRealDataLock;
    
    WriteLog(TLogLevel::INFO, "TLSXingAPI 정리 완료");
}

// COM 컴포넌트 초기화
bool TLSXingAPI::InitializeComponents() {
    try {
        // XASession 생성
        FXASession = new TXASession(nullptr);
        FXASession->OnLogin = OnSessionLogin;
        FXASession->OnDisconnect = OnSessionDisconnect;
        
        WriteLog(TLogLevel::INFO, "XingAPI COM 컴포넌트 초기화 완료");
        return true;
    }
    catch (const Exception& e) {
        FLastError = "COM 컴포넌트 초기화 실패: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return false;
    }
}

// COM 컴포넌트 정리
void TLSXingAPI::CleanupComponents() {
    try {
        // 모든 실시간 구독 해제
        UnsubscribeAll();
        
        // Query 객체들 정리
        for (auto& pair : FXAQueries) {
            if (pair.second) {
                delete pair.second;
            }
        }
        FXAQueries.clear();
        
        // Real 객체들 정리
        for (auto& pair : FXAReals) {
            if (pair.second) {
                delete pair.second;
            }
        }
        FXAReals.clear();
        
        // Session 객체 정리
        if (FXASession) {
            delete FXASession;
            FXASession = nullptr;
        }
        
        WriteLog(TLogLevel::INFO, "COM 컴포넌트 정리 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "COM 컴포넌트 정리 중 오류: " + e.Message);
    }
}

// 서버 연결
bool TLSXingAPI::ConnectServer(bool isRealServer, const AnsiString& serverAddr, int port) {
    try {
        if (FConnectionState >= TConnectionState::CONNECTED) {
            WriteLog(TLogLevel::WARNING, "이미 서버에 연결되어 있습니다");
            return true;
        }
        
        FIsRealServer = isRealServer;
        FServerAddress = serverAddr;
        FServerPort = port;
        FConnectionState = TConnectionState::CONNECTING;
        
        // 서버 연결 시도
        bool result = FXASession->ConnectServer(WideString(FServerAddress), FServerPort);
        
        if (result) {
            FConnectionState = TConnectionState::CONNECTED;
            FLastConnectTime = Now();
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("서버 연결 완료: %s:%d (%s)", 
                    FServerAddress.c_str(), FServerPort, 
                    FIsRealServer ? "실서버" : "모의서버"));
            
            if (FOnConnected) {
                FOnConnected(this, true);
            }
            
            return true;
        } else {
            FConnectionState = TConnectionState::ERROR_STATE;
            FLastError = "서버 연결 실패";
            WriteLog(TLogLevel::ERROR, FLastError);
            
            if (FOnConnected) {
                FOnConnected(this, false);
            }
            
            return false;
        }
    }
    catch (const Exception& e) {
        FConnectionState = TConnectionState::ERROR_STATE;
        FLastError = "서버 연결 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return false;
    }
}

// 서버 연결 해제
void TLSXingAPI::DisconnectServer() {
    try {
        if (FConnectionState == TConnectionState::DISCONNECTED) {
            return;
        }
        
        // 로그아웃 먼저 수행
        if (IsLoggedIn()) {
            Logout();
        }
        
        // 실시간 구독 해제
        UnsubscribeAll();
        
        // 서버 연결 해제
        if (FXASession) {
            FXASession->DisconnectServer();
        }
        
        FConnectionState = TConnectionState::DISCONNECTED;
        WriteLog(TLogLevel::INFO, "서버 연결 해제 완료");
        
        if (FOnDisconnected) {
            FOnDisconnected(this, true);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "서버 연결 해제 중 오류: " + e.Message);
    }
}

// 로그인
bool TLSXingAPI::Login(const AnsiString& userId, const AnsiString& password, 
                      const AnsiString& certPassword) {
    try {
        if (!IsConnected()) {
            FLastError = "서버에 연결되지 않음";
            WriteLog(TLogLevel::ERROR, FLastError);
            return false;
        }
        
        if (IsLoggedIn()) {
            WriteLog(TLogLevel::WARNING, "이미 로그인되어 있습니다");
            return true;
        }
        
        FUserId = userId;
        FPassword = password;
        FCertPassword = certPassword;
        FConnectionState = TConnectionState::LOGIN_IN_PROGRESS;
        
        // 설정 파일에서 로그인 정보 로드 (빈 값인 경우)
        if (FUserId.IsEmpty() && LoadConfiguration()) {
            // 설정에서 로드됨
        }
        
        // 로그인 실행
        bool result = FXASession->Login(
            WideString(FUserId), 
            WideString(FPassword), 
            WideString(FCertPassword), 
            FIsRealServer ? 1 : 0,  // 서버구분
            0                       // 공인인증서 로그인 여부
        );
        
        if (result) {
            WriteLog(TLogLevel::INFO, "로그인 요청 전송 완료: " + FUserId);
            // 실제 로그인 결과는 OnSessionLogin 이벤트에서 처리
            return true;
        } else {
            FConnectionState = TConnectionState::CONNECTED;
            FLastError = "로그인 요청 실패";
            WriteLog(TLogLevel::ERROR, FLastError);
            
            if (FOnLoginResult) {
                FOnLoginResult(this, false, FLastError);
            }
            
            return false;
        }
    }
    catch (const Exception& e) {
        FConnectionState = TConnectionState::CONNECTED;
        FLastError = "로그인 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        
        if (FOnLoginResult) {
            FOnLoginResult(this, false, FLastError);
        }
        
        return false;
    }
}

// 로그아웃
void TLSXingAPI::Logout() {
    try {
        if (!IsLoggedIn()) {
            return;
        }
        
        // 모든 구독 해제
        UnsubscribeAll();
        
        // 로그아웃 실행
        if (FXASession) {
            FXASession->Logout();
        }
        
        FConnectionState = TConnectionState::CONNECTED;
        FUserId = "";
        FPassword = "";
        FCertPassword = "";
        FAccountNumber = "";
        FAccountList.clear();
        
        WriteLog(TLogLevel::INFO, "로그아웃 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "로그아웃 중 오류: " + e.Message);
    }
}

// 설정 로드
bool TLSXingAPI::LoadConfiguration() {
    try {
        if (!TUtils::FileExists(FConfigPath)) {
            WriteLog(TLogLevel::WARNING, "설정 파일이 없습니다: " + FConfigPath);
            return false;
        }
        
        std::unique_ptr<TIniFile> ini(new TIniFile(FConfigPath));
        
        if (FUserId.IsEmpty()) {
            FUserId = ini->ReadString("LOGIN", "ID", "");
        }
        if (FPassword.IsEmpty()) {
            FPassword = ini->ReadString("LOGIN", "PASSWORD", "");
        }
        if (FCertPassword.IsEmpty()) {
            FCertPassword = ini->ReadString("LOGIN", "CERT_PASSWORD", "");
        }
        
        FServerAddress = ini->ReadString("CONNECTION", "SERVER", "api.ls-sec.co.kr");
        FServerPort = ini->ReadInteger("CONNECTION", "PORT", 20001);
        FAutoReconnect = ini->ReadBool("CONNECTION", "AUTO_RECONNECT", true);
        
        WriteLog(TLogLevel::INFO, "설정 로드 완료");
        return true;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "설정 로드 중 오류: " + e.Message);
        return false;
    }
}

// 설정 저장
void TLSXingAPI::SaveConfiguration() {
    try {
        // 디렉토리 생성
        AnsiString configDir = ExtractFileDir(FConfigPath);
        if (!TUtils::DirectoryExists(configDir)) {
            TUtils::CreateDirectoryRecursive(configDir);
        }
        
        std::unique_ptr<TIniFile> ini(new TIniFile(FConfigPath));
        
        // 보안상 패스워드는 저장하지 않음
        ini->WriteString("LOGIN", "ID", FUserId);
        ini->WriteString("CONNECTION", "SERVER", FServerAddress);
        ini->WriteInteger("CONNECTION", "PORT", FServerPort);
        ini->WriteBool("CONNECTION", "AUTO_RECONNECT", FAutoReconnect);
        
        WriteLog(TLogLevel::INFO, "설정 저장 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "설정 저장 중 오류: " + e.Message);
    }
}

// 옵션 호가 구독
bool TLSXingAPI::SubscribeOptionHoga(const AnsiString& code) {
    try {
        TLockGuard lock(FRealDataLock);
        
        // 이미 구독 중인지 확인
        if (FRealSubscriptions.find(code) != FRealSubscriptions.end()) {
            WriteLog(TLogLevel::WARNING, "이미 구독 중인 종목: " + code);
            return true;
        }
        
        // Real 객체 생성
        AnsiString realKey = "HOGA_" + code;
        TXAReal* real = new TXAReal(nullptr);
        real->OnReceiveRealData = OnRealReceiveData;
        
        // 옵션 호가 TR 설정 (실제 TR 코드는 XingAPI 문서 참조)
        real->SetFieldData("InBlock", "optcode", code);
        
        // 구독 시작
        bool result = real->AdviseRealData();
        
        if (result) {
            FXAReals[realKey] = real;
            FRealSubscriptions[code] = TRealDataType::OPTION_HOGA;
            
            WriteLog(TLogLevel::INFO, "옵션 호가 구독 시작: " + code);
            return true;
        } else {
            delete real;
            WriteLog(TLogLevel::ERROR, "옵션 호가 구독 실패: " + code);
            return false;
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "옵션 호가 구독 중 오류: " + e.Message);
        return false;
    }
}

// 주문 전송
AnsiString TLSXingAPI::SendOrder(const AnsiString& code, TTradeType tradeType, 
                                 int quantity, double price, TOrderType orderType) {
    try {
        if (!IsLoggedIn()) {
            FLastError = "로그인이 필요합니다";
            WriteLog(TLogLevel::ERROR, FLastError);
            return "";
        }
        
        if (FAccountNumber.IsEmpty()) {
            FLastError = "계좌가 설정되지 않았습니다";
            WriteLog(TLogLevel::ERROR, FLastError);
            return "";
        }
        
        // TR 요청 생성
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::ORDER_SEND;
        request->trCode = "CFOAT00100";  // 옵션 주문 TR (실제 코드는 문서 확인 필요)
        request->requestId = GenerateRequestId();
        
        // 입력 데이터 설정
        request->inputData["accno"] = FAccountNumber;           // 계좌번호
        request->inputData["expcode"] = code;                   // 종목코드
        request->inputData["qty"] = IntToStr(quantity);         // 주문수량
        request->inputData["price"] = FloatToStrF(price, ffFixed, 15, 2);  // 주문가격
        request->inputData["medosu"] = (tradeType == TTradeType::BUY) ? "1" : "2";  // 매매구분
        request->inputData["hogagb"] = (orderType == TOrderType::MARKET) ? "03" : "00";  // 호가유형
        
        // 요청 큐에 추가
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        // 요청 처리 타이머 시작
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문 요청 등록: %s, %s %d주, 가격: %.2f", 
                code.c_str(), TUtils::TradeTypeToString(tradeType).c_str(), 
                quantity, price));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "주문 전송 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// TR 요청 처리 타이머
void __fastcall TLSXingAPI::OnRequestTimer(TObject* Sender) {
    ProcessNextRequest();
}

// 다음 요청 처리
void TLSXingAPI::ProcessNextRequest() {
    try {
        std::shared_ptr<TTRRequest> request;
        
        {
            TLockGuard lock(FRequestLock);
            if (FRequestQueue.empty()) {
                FRequestTimer->Enabled = false;
                return;
            }
            
            request = FRequestQueue.front();
            FRequestQueue.pop();
        }
        
        if (!request) return;
        
        // Query 객체 생성 또는 재사용
        TXAQuery* query = nullptr;
        AnsiString queryKey = request->trCode + "_" + request->requestId;
        
        auto it = FXAQueries.find(queryKey);
        if (it != FXAQueries.end()) {
            query = it->second;
        } else {
            query = new TXAQuery(nullptr);
            query->OnReceiveData = OnQueryReceiveData;
            FXAQueries[queryKey] = query;
        }
        
        // TR 파일 설정
        query->ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\" + request->trCode + ".res";
        
        // 입력 데이터 설정
        for (const auto& pair : request->inputData) {
            query->SetFieldData("InBlock", pair.first, 0, pair.second);
        }
        
        // 요청 전송
        int result = query->Request(false);
        
        if (result < 0) {
            WriteLog(TLogLevel::ERROR, "TR 요청 실패: " + request->trCode);
            request->isCompleted = true;
        } else {
            WriteLog(TLogLevel::INFO, "TR 요청 전송: " + request->trCode + " (" + request->requestId + ")");
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "TR 요청 처리 중 오류: " + e.Message);
    }
}

// 세션 로그인 이벤트
void __fastcall TLSXingAPI::OnSessionLogin(TObject* Sender, OLECHAR* szCode, OLECHAR* szMsg) {
    try {
        AnsiString code = AnsiString(szCode);
        AnsiString message = AnsiString(szMsg);
        
        if (code == "0000") {  // 성공
            FConnectionState = TConnectionState::LOGGED_IN;
            
            // 계좌 목록 조회
            GetAccountList();
            
            // 설정 저장
            SaveConfiguration();
            
            WriteLog(TLogLevel::INFO, "로그인 성공: " + FUserId);
            
            if (FOnLoginResult) {
                FOnLoginResult(this, true, "로그인 성공");
            }
        } else {
            FConnectionState = TConnectionState::CONNECTED;
            FLastError = "로그인 실패: " + code + " - " + message;
            WriteLog(TLogLevel::ERROR, FLastError);
            
            if (FOnLoginResult) {
                FOnLoginResult(this, false, FLastError);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "로그인 이벤트 처리 중 오류: " + e.Message);
    }
}

// Query 수신 이벤트 처리
void __fastcall TLSXingAPI::OnQueryReceiveData(TObject* Sender, OLECHAR* szTrCode) {
    try {
        TXAQuery* query = static_cast<TXAQuery*>(Sender);
        if (!query) return;
        
        AnsiString trCode = AnsiString(szTrCode);
        int blockCount = query->GetBlockCount();
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("Query 수신: TR=%s, BlockCount=%d", 
                trCode.c_str(), blockCount));
        
        // TR 코드별 처리 - 실제 reference 코드에 맞게 수정
        if (trCode == "t2301") {  // 옵션 전광판
            HandleOptionBoard(query, blockCount);
        }
        else if (trCode == "t0434") {  // 체결/미체결 조회
            HandleOrderList(query, blockCount);
        }
        else if (trCode == "t0441") {  // 잔고평가
            HandlePositionDetail(query, blockCount);
        }
        else if (trCode == "t8415") {  // 선물/옵션 차트
            HandleOptionChart(query, blockCount);
        }
        else if (trCode == "CFOAT00100") {  // 선물옵션 정상주문
            HandleOrderResult(query, blockCount);
        }
        else if (trCode == "CFOAT00200") {  // 선물옵션 정정주문
            HandleModifyOrderResult(query, blockCount);
        }
        else if (trCode == "CFOAT00300") {  // 선물옵션 취소주문
            HandleCancelOrderResult(query, blockCount);
        }
        
        // 콜백 호출
        if (FOnQueryReceived) {
            FOnQueryReceived(trCode, blockCount);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "Query 수신 처리 중 오류: " + e.Message);
    }
}

// 실시간 데이터 수신 이벤트
void __fastcall TLSXingAPI::OnRealReceiveData(TObject* Sender, OLECHAR* szTrCode) {
    try {
        TXAReal* real = static_cast<TXAReal*>(Sender);
        if (!real) return;
        
        AnsiString trCode = AnsiString(szTrCode);
        
        WriteLog(TLogLevel::DEBUG, "실시간 데이터 수신: " + trCode);
        
        // TR 코드별 실시간 데이터 처리 - reference 코드에 맞게 수정
        if (trCode == "OH0") {  // KOSPI200옵션호가
            HandleRealOptionHoga(real);
        }
        else if (trCode == "OC0") {  // KOSPI200옵션체결
            HandleRealOptionExecution(real);
        }
        else if (trCode == "C01") {  // 선물주문체결
            HandleRealOrderExecution(real);
        }
        else if (trCode == "H01") {  // 선물주문정정취소
            HandleRealOrderModifyCancel(real);
        }
        else if (trCode == "O01") {  // 선물접수
            HandleRealOrderAccept(real);
        }
        
        // 콜백 호출
        if (FOnRealDataReceived) {
            FOnRealDataReceived(trCode);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 데이터 처리 중 오류: " + e.Message);
    }
}

// 세션 연결 해제 이벤트
void __fastcall TLSXingAPI::OnSessionDisconnect(TObject* Sender) {
    try {
        WriteLog(TLogLevel::WARNING, "서버 연결이 끊어졌습니다");
        
        FConnectionState = TConnectionState::DISCONNECTED;
        
        if (FOnDisconnected) {
            FOnDisconnected(this, false);
        }
        
        // 자동 재연결 시도
        if (FAutoReconnect && FReconnectAttempts < FMaxReconnectAttempts) {
            StartReconnectTimer();
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "연결 해제 이벤트 처리 중 오류: " + e.Message);
    }
}

// 재연결 타이머 이벤트
void __fastcall TLSXingAPI::OnReconnectTimer(TObject* Sender) {
    try {
        StopReconnectTimer();
        
        FReconnectAttempts++;
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("재연결 시도 (%d/%d)", 
                FReconnectAttempts, FMaxReconnectAttempts));
        
        // 연결 시도
        if (ConnectServer(FIsRealServer, FServerAddress, FServerPort)) {
            // 로그인 시도
            if (!FUserId.IsEmpty() && !FPassword.IsEmpty()) {
                Login(FUserId, FPassword, FCertPassword);
            }
            FReconnectAttempts = 0;
        }
        else if (FReconnectAttempts < FMaxReconnectAttempts) {
            StartReconnectTimer();
        }
        else {
            WriteLog(TLogLevel::ERROR, "재연결 횟수 초과. 자동 재연결을 중단합니다");
            FConnectionState = TConnectionState::ERROR_STATE;
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "재연결 처리 중 오류: " + e.Message);
    }
}

// 요청 ID 생성
AnsiString TLSXingAPI::GenerateRequestId() {
    FRequestCounter++;
    return AnsiString().sprintf("REQ%08d_%08X", FRequestCounter, GetTickCount());
}

// 로깅
void TLSXingAPI::WriteLog(TLogLevel level, const AnsiString& message) {
    try {
        AnsiString logMsg = AnsiString().sprintf("[LSXingAPI] %s", message.c_str());
        
        // 실제 로거가 구현되면 연결
        #ifdef _DEBUG
        OutputDebugStringA(logMsg.c_str());
        #endif
        
        // 파일 로깅 (global.cpp의 DebugLog 함수 활용 가능)
        // DebugLog("LSXingAPI", logMsg);
    }
    catch (...) {
        // 로깅 실패는 무시
    }
}

// 상태 설명 반환
AnsiString TLSXingAPI::GetStateDescription() const {
    switch (FConnectionState) {
        case TConnectionState::DISCONNECTED:
            return "연결 해제됨";
        case TConnectionState::CONNECTING:
            return "연결 중";
        case TConnectionState::CONNECTED:
            return "연결됨";
        case TConnectionState::LOGIN_IN_PROGRESS:
            return "로그인 중";
        case TConnectionState::LOGGED_IN:
            return "로그인 완료";
        case TConnectionState::ERROR_STATE:
            return "오류 상태";
        default:
            return "알 수 없는 상태";
    }
}

// 계좌 목록 조회
std::vector<AnsiString> TLSXingAPI::GetAccountList() {
    try {
        if (!IsLoggedIn()) {
            return FAccountList;
        }
        
        FAccountList.clear();
        
        // XASession에서 계좌 목록 조회
        int accountCount = FXASession->GetAccountListCount();
        
        for (int i = 0; i < accountCount; i++) {
            AnsiString account = AnsiString(FXASession->GetAccountList(i));
            FAccountList.push_back(account);
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("계좌 목록 조회 완료: %d개", accountCount));
        
        return FAccountList;
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "계좌 목록 조회 중 오류: " + e.Message);
        return FAccountList;
    }
}

// 옵션 전광판 요청 (t2301)
AnsiString TLSXingAPI::RequestOptionBoard(const AnsiString& yyyymm, char gubun) {
    try {
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::OPTION_CODE_LIST;
        request->trCode = "t2301";
        request->requestId = GenerateRequestId();
        
        request->inputData["yyyymm"] = yyyymm;
        request->inputData["gubun"] = AnsiString(gubun);
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("옵션 전광판 요청: %s, 구분: %c", 
                yyyymm.c_str(), gubun));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "옵션 전광판 요청 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 체결/미체결 조회 (t0434)
AnsiString TLSXingAPI::RequestOrderList(const AnsiString& accno, const AnsiString& passwd,
                                       const AnsiString& expcode, char chegb) {
    try {
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::POSITION_INFO;
        request->trCode = "t0434";
        request->requestId = GenerateRequestId();
        
        request->inputData["accno"] = accno;
        request->inputData["passwd"] = passwd;
        request->inputData["expcode"] = expcode;
        request->inputData["chegb"] = AnsiString(chegb);
        request->inputData["sortgb"] = "1"; // 기본 정렬
        request->inputData["cts_ordno"] = ""; // 첫 요청
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("체결/미체결 조회 요청: %s", accno.c_str()));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "체결/미체결 조회 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 선물/옵션 잔고평가 (t0441)
AnsiString TLSXingAPI::RequestPositionDetail(const AnsiString& accno, const AnsiString& passwd) {
    try {
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::POSITION_INFO;
        request->trCode = "t0441";
        request->requestId = GenerateRequestId();
        
        request->inputData["accno"] = accno;
        request->inputData["passwd"] = passwd;
        request->inputData["cts_expcode"] = ""; // 첫 요청
        request->inputData["cts_medocd"] = ""; // 첫 요청
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("잔고평가 조회 요청: %s", accno.c_str()));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "잔고평가 조회 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 선물/옵션 차트 요청 (t8415)
AnsiString TLSXingAPI::RequestOptionChart(const AnsiString& shcode, int ncnt, 
                                         int qrycnt, const AnsiString& sdate) {
    try {
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::CHART_DATA;
        request->trCode = "t8415";
        request->requestId = GenerateRequestId();
        
        request->inputData["shcode"] = shcode;
        request->inputData["ncnt"] = IntToStr(ncnt);
        request->inputData["qrycnt"] = IntToStr(qrycnt);
        request->inputData["nday"] = "0"; // 미사용
        request->inputData["sdate"] = sdate.IsEmpty() ? FormatDateTime("yyyymmdd", Now()) : sdate;
        request->inputData["stime"] = "";
        request->inputData["edate"] = "";
        request->inputData["etime"] = "";
        request->inputData["cts_date"] = ""; // 첫 요청
        request->inputData["cts_time"] = ""; // 첫 요청
        request->inputData["comp_yn"] = "N"; // 비압축
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("옵션 차트 요청: %s, %d분, %d건", 
                shcode.c_str(), ncnt, qrycnt));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "옵션 차트 요청 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 선물옵션 정상주문 (CFOAT00100)
AnsiString TLSXingAPI::SendOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                                      const AnsiString& fnoIsuNo, char bnsTpCode,
                                      const AnsiString& fnoOrdprcPtnCode, double fnoOrdPrc, 
                                      long ordQty) {
    try {
        if (!IsLoggedIn()) {
            FLastError = "로그인이 필요합니다";
            WriteLog(TLogLevel::ERROR, FLastError);
            return "";
        }
        
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::ORDER_SEND;
        request->trCode = "CFOAT00100";
        request->requestId = GenerateRequestId();
        
        request->inputData["AcntNo"] = acntNo;
        request->inputData["Pwd"] = pwd;
        request->inputData["FnoIsuNo"] = fnoIsuNo;
        request->inputData["BnsTpCode"] = AnsiString(bnsTpCode);
        request->inputData["FnoOrdprcPtnCode"] = fnoOrdprcPtnCode;
        request->inputData["FnoOrdPrc"] = FloatToStrF(fnoOrdPrc, ffFixed, 15, 8);
        request->inputData["OrdQty"] = IntToStr(ordQty);
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("옵션 주문 요청: %s, %s %ld주, 가격: %.2f", 
                fnoIsuNo.c_str(), (bnsTpCode == '1') ? "매도" : "매수", 
                ordQty, fnoOrdPrc));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "옵션 주문 전송 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 선물옵션 정정주문 (CFOAT00200)
AnsiString TLSXingAPI::ModifyOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                                        const AnsiString& fnoIsuNo, const AnsiString& orgOrdNo,
                                        const AnsiString& fnoOrdprcPtnCode, double fnoOrdPrc,
                                        long mdfyQty) {
    try {
        if (!IsLoggedIn()) {
            FLastError = "로그인이 필요합니다";
            WriteLog(TLogLevel::ERROR, FLastError);
            return "";
        }
        
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::ORDER_MODIFY;
        request->trCode = "CFOAT00200";
        request->requestId = GenerateRequestId();
        
        request->inputData["AcntNo"] = acntNo;
        request->inputData["Pwd"] = pwd;
        request->inputData["FnoIsuNo"] = fnoIsuNo;
        request->inputData["OrgOrdNo"] = orgOrdNo;
        request->inputData["FnoOrdprcPtnCode"] = fnoOrdprcPtnCode;
        request->inputData["FnoOrdPrc"] = FloatToStrF(fnoOrdPrc, ffFixed, 15, 8);
        request->inputData["MdfyQty"] = IntToStr(mdfyQty);
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("옵션 정정 요청: %s -> %s, %ld주, 가격: %.2f", 
                orgOrdNo.c_str(), fnoIsuNo.c_str(), mdfyQty, fnoOrdPrc));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "옵션 주문 정정 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 선물옵션 취소주문 (CFOAT00300)
AnsiString TLSXingAPI::CancelOptionOrder(const AnsiString& acntNo, const AnsiString& pwd,
                                        const AnsiString& fnoIsuNo, const AnsiString& orgOrdNo,
                                        long cancQty) {
    try {
        if (!IsLoggedIn()) {
            FLastError = "로그인이 필요합니다";
            WriteLog(TLogLevel::ERROR, FLastError);
            return "";
        }
        
        auto request = std::make_shared<TTRRequest>();
        request->type = TTRType::ORDER_CANCEL;
        request->trCode = "CFOAT00300";
        request->requestId = GenerateRequestId();
        
        request->inputData["AcntNo"] = acntNo;
        request->inputData["Pwd"] = pwd;
        request->inputData["FnoIsuNo"] = fnoIsuNo;
        request->inputData["OrgOrdNo"] = orgOrdNo;
        request->inputData["CancQty"] = IntToStr(cancQty);
        
        {
            TLockGuard lock(FRequestLock);
            FPendingRequests[request->requestId] = request;
            FRequestQueue.push(request);
        }
        
        if (!FRequestTimer->Enabled) {
            FRequestTimer->Enabled = true;
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("옵션 취소 요청: %s -> %s, %ld주", 
                orgOrdNo.c_str(), fnoIsuNo.c_str(), cancQty));
        
        return request->requestId;
    }
    catch (const Exception& e) {
        FLastError = "옵션 주문 취소 중 오류: " + e.Message;
        WriteLog(TLogLevel::ERROR, FLastError);
        return "";
    }
}

// 옵션 현재가 처리
void TLSXingAPI::HandleOptionCurrentPrice(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOptionCurrentPrice data;
            data.code = AnsiString(query->GetFieldData("OutBlock", "optcode", i));
            data.price = query->GetFieldData("OutBlock", "price", i).ToDouble();
            data.change = query->GetFieldData("OutBlock", "change", i).ToDouble();
            data.changeRate = query->GetFieldData("OutBlock", "changerate", i).ToDouble();
            data.volume = query->GetFieldData("OutBlock", "volume", i).ToInt();
            data.openInterest = query->GetFieldData("OutBlock", "openinterest", i).ToInt();
            data.theoreticalPrice = query->GetFieldData("OutBlock", "theoryprice", i).ToDouble();
            data.impliedVolatility = query->GetFieldData("OutBlock", "iv", i).ToDouble();
            data.delta = query->GetFieldData("OutBlock", "delta", i).ToDouble();
            data.gamma = query->GetFieldData("OutBlock", "gamma", i).ToDouble();
            data.theta = query->GetFieldData("OutBlock", "theta", i).ToDouble();
            data.vega = query->GetFieldData("OutBlock", "vega", i).ToDouble();
            
            // 콜백 호출
            if (FOnOptionCurrentPrice) {
                FOnOptionCurrentPrice(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "옵션 현재가 처리 중 오류: " + e.Message);
    }
}

// 옵션 호가 처리
void TLSXingAPI::HandleOptionHoga(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOptionHoga data;
            data.code = AnsiString(query->GetFieldData("OutBlock", "optcode", i));
            
            // 매도 호가
            for (int j = 0; j < 10; j++) {
                AnsiString priceField = AnsiString().sprintf("offerho%d", j + 1);
                AnsiString qtyField = AnsiString().sprintf("offerrem%d", j + 1);
                data.sellPrices[j] = query->GetFieldData("OutBlock", priceField, i).ToDouble();
                data.sellQuantities[j] = query->GetFieldData("OutBlock", qtyField, i).ToInt();
            }
            
            // 매수 호가
            for (int j = 0; j < 10; j++) {
                AnsiString priceField = AnsiString().sprintf("bidho%d", j + 1);
                AnsiString qtyField = AnsiString().sprintf("bidrem%d", j + 1);
                data.buyPrices[j] = query->GetFieldData("OutBlock", priceField, i).ToDouble();
                data.buyQuantities[j] = query->GetFieldData("OutBlock", qtyField, i).ToInt();
            }
            
            data.totalSellQuantity = query->GetFieldData("OutBlock", "totofferrem", i).ToInt();
            data.totalBuyQuantity = query->GetFieldData("OutBlock", "totbidrem", i).ToInt();
            
            // 콜백 호출
            if (FOnOptionHoga) {
                FOnOptionHoga(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "옵션 호가 처리 중 오류: " + e.Message);
    }
}

// 옵션 체결 처리
void TLSXingAPI::HandleOptionExecution(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOptionExecution data;
            data.code = AnsiString(query->GetFieldData("OutBlock", "optcode", i));
            data.time = AnsiString(query->GetFieldData("OutBlock", "chetime", i));
            data.price = query->GetFieldData("OutBlock", "cheprice", i).ToDouble();
            data.quantity = query->GetFieldData("OutBlock", "cheqty", i).ToInt();
            data.change = query->GetFieldData("OutBlock", "change", i).ToDouble();
            data.volume = query->GetFieldData("OutBlock", "volume", i).ToInt();
            
            // 콜백 호출
            if (FOnOptionExecution) {
                FOnOptionExecution(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "옵션 체결 처리 중 오류: " + e.Message);
    }
}

// 주문 결과 처리
void TLSXingAPI::HandleOrderResult(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOrderResult data;
            data.orderId = AnsiString(query->GetFieldData("OutBlock", "ordno", i));
            data.code = AnsiString(query->GetFieldData("OutBlock", "expcode", i));
            data.quantity = query->GetFieldData("OutBlock", "qty", i).ToInt();
            data.price = query->GetFieldData("OutBlock", "price", i).ToDouble();
            data.result = AnsiString(query->GetFieldData("OutBlock", "result", i));
            data.message = AnsiString(query->GetFieldData("OutBlock", "ordmsg", i));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("주문 결과: %s, %s, %s", 
                    data.orderId.c_str(), data.result.c_str(), data.message.c_str()));
            
            // 콜백 호출
            if (FOnOrderResult) {
                FOnOrderResult(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "주문 결과 처리 중 오류: " + e.Message);
    }
}

// 주문 정정 결과 처리
void TLSXingAPI::HandleModifyOrderResult(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOrderResult data;
            data.orderId = AnsiString(query->GetFieldData("OutBlock", "ordno", i));
            data.result = AnsiString(query->GetFieldData("OutBlock", "result", i));
            data.message = AnsiString(query->GetFieldData("OutBlock", "ordmsg", i));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("정정 결과: %s, %s, %s", 
                    data.orderId.c_str(), data.result.c_str(), data.message.c_str()));
            
            // 콜백 호출
            if (FOnOrderModifyResult) {
                FOnOrderModifyResult(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "정정 결과 처리 중 오류: " + e.Message);
    }
}

// 주문 취소 결과 처리
void TLSXingAPI::HandleCancelOrderResult(TXAQuery* query, int blockCount) {
    try {
        for (int i = 0; i < blockCount; i++) {
            TOrderResult data;
            data.orderId = AnsiString(query->GetFieldData("OutBlock", "ordno", i));
            data.result = AnsiString(query->GetFieldData("OutBlock", "result", i));
            data.message = AnsiString(query->GetFieldData("OutBlock", "ordmsg", i));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("취소 결과: %s, %s, %s", 
                    data.orderId.c_str(), data.result.c_str(), data.message.c_str()));
            
            // 콜백 호출
            if (FOnOrderCancelResult) {
                FOnOrderCancelResult(data);
            }
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "취소 결과 처리 중 오류: " + e.Message);
    }
}

// 실시간 옵션 호가 처리
void TLSXingAPI::HandleRealOptionHoga(TXAReal* real) {
    try {
        TOptionHoga data;
        data.code = AnsiString(real->GetFieldData("OutBlock", "optcode"));
        
        // 실시간 호가 데이터 파싱
        for (int i = 0; i < 10; i++) {
            AnsiString sellPriceField = AnsiString().sprintf("offerho%d", i + 1);
            AnsiString sellQtyField = AnsiString().sprintf("offerrem%d", i + 1);
            AnsiString buyPriceField = AnsiString().sprintf("bidho%d", i + 1);
            AnsiString buyQtyField = AnsiString().sprintf("bidrem%d", i + 1);
            
            data.sellPrices[i] = real->GetFieldData("OutBlock", sellPriceField).ToDouble();
            data.sellQuantities[i] = real->GetFieldData("OutBlock", sellQtyField).ToInt();
            data.buyPrices[i] = real->GetFieldData("OutBlock", buyPriceField).ToDouble();
            data.buyQuantities[i] = real->GetFieldData("OutBlock", buyQtyField).ToInt();
        }
        
        data.totalSellQuantity = real->GetFieldData("OutBlock", "totofferrem").ToInt();
        data.totalBuyQuantity = real->GetFieldData("OutBlock", "totbidrem").ToInt();
        
        // 콜백 호출
        if (FOnOptionHoga) {
            FOnOptionHoga(data);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 옵션 호가 처리 중 오류: " + e.Message);
    }
}

// 실시간 옵션 체결 처리
void TLSXingAPI::HandleRealOptionExecution(TXAReal* real) {
    try {
        TOptionExecution data;
        data.code = AnsiString(real->GetFieldData("OutBlock", "optcode"));
        data.time = AnsiString(real->GetFieldData("OutBlock", "chetime"));
        data.price = real->GetFieldData("OutBlock", "cheprice").ToDouble();
        data.quantity = real->GetFieldData("OutBlock", "cheqty").ToInt();
        data.change = real->GetFieldData("OutBlock", "change").ToDouble();
        data.volume = real->GetFieldData("OutBlock", "volume").ToInt();
        
        // 콜백 호출
        if (FOnOptionExecution) {
            FOnOptionExecution(data);
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 옵션 체결 처리 중 오류: " + e.Message);
    }
}

// 모든 실시간 구독 해제
void TLSXingAPI::UnsubscribeAll() {
    try {
        TLockGuard lock(FRealDataLock);
        
        for (auto& pair : FXAReals) {
            if (pair.second) {
                pair.second->UnadviseRealData();
            }
        }
        
        FRealSubscriptions.clear();
        WriteLog(TLogLevel::INFO, "모든 실시간 구독 해제 완료");
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 구독 해제 중 오류: " + e.Message);
    }
}

// 재연결 타이머 시작
void TLSXingAPI::StartReconnectTimer() {
    if (FReconnectTimer && !FReconnectTimer->Enabled) {
        FReconnectTimer->Enabled = true;
        WriteLog(TLogLevel::INFO, "재연결 타이머 시작");
    }
}

// 재연결 타이머 중지
void TLSXingAPI::StopReconnectTimer() {
    if (FReconnectTimer && FReconnectTimer->Enabled) {
        FReconnectTimer->Enabled = false;
        WriteLog(TLogLevel::INFO, "재연결 타이머 중지");
    }
}

// Reference 코드 기반 새로운 TR 처리 메서드들

// 옵션 전광판 처리 (t2301)
void TLSXingAPI::HandleOptionBoard(TXAQuery* query, int blockCount) {
    try {
        // t2301OutBlock 기본 정보
        t2301OutBlock outBlock;
        memset(&outBlock, 0, sizeof(outBlock));
        
        if (blockCount > 0) {
            // 기본 정보 파싱
            AnsiString histimpv = AnsiString(query->GetFieldData("t2301OutBlock", "histimpv", 0));
            AnsiString jandatecnt = AnsiString(query->GetFieldData("t2301OutBlock", "jandatecnt", 0));
            AnsiString cimpv = AnsiString(query->GetFieldData("t2301OutBlock", "cimpv", 0));
            AnsiString pimpv = AnsiString(query->GetFieldData("t2301OutBlock", "pimpv", 0));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("옵션 전광판: 역사적변동성=%s, 잔존일=%s, 콜IV=%s, 풋IV=%s", 
                    histimpv.c_str(), jandatecnt.c_str(), cimpv.c_str(), pimpv.c_str()));
        }
        
        // 콜옵션 데이터 처리 (t2301OutBlock1)
        int callCount = query->GetBlockCount("t2301OutBlock1");
        for (int i = 0; i < callCount; i++) {
            t2301OutBlock1 callData;
            memset(&callData, 0, sizeof(callData));
            
            AnsiString actprice = AnsiString(query->GetFieldData("t2301OutBlock1", "actprice", i));
            AnsiString optcode = AnsiString(query->GetFieldData("t2301OutBlock1", "optcode", i));
            AnsiString price = AnsiString(query->GetFieldData("t2301OutBlock1", "price", i));
            
            WriteLog(TLogLevel::DEBUG, 
                AnsiString().sprintf("콜옵션: 행사가=%s, 코드=%s, 현재가=%s", 
                    actprice.c_str(), optcode.c_str(), price.c_str()));
        }
        
        // 풋옵션 데이터 처리 (t2301OutBlock2)
        int putCount = query->GetBlockCount("t2301OutBlock2");
        for (int i = 0; i < putCount; i++) {
            t2301OutBlock2 putData;
            memset(&putData, 0, sizeof(putData));
            
            AnsiString actprice = AnsiString(query->GetFieldData("t2301OutBlock2", "actprice", i));
            AnsiString optcode = AnsiString(query->GetFieldData("t2301OutBlock2", "optcode", i));
            AnsiString price = AnsiString(query->GetFieldData("t2301OutBlock2", "price", i));
            
            WriteLog(TLogLevel::DEBUG, 
                AnsiString().sprintf("풋옵션: 행사가=%s, 코드=%s, 현재가=%s", 
                    actprice.c_str(), optcode.c_str(), price.c_str()));
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "옵션 전광판 처리 중 오류: " + e.Message);
    }
}

// 체결/미체결 조회 처리 (t0434)
void TLSXingAPI::HandleOrderList(TXAQuery* query, int blockCount) {
    try {
        // t0434OutBlock1 주문 리스트 처리
        int orderCount = query->GetBlockCount("t0434OutBlock1");
        for (int i = 0; i < orderCount; i++) {
            t0434OutBlock1 orderData;
            memset(&orderData, 0, sizeof(orderData));
            
            AnsiString ordno = AnsiString(query->GetFieldData("t0434OutBlock1", "ordno", i));
            AnsiString medosu = AnsiString(query->GetFieldData("t0434OutBlock1", "medosu", i));
            AnsiString qty = AnsiString(query->GetFieldData("t0434OutBlock1", "qty", i));
            AnsiString price = AnsiString(query->GetFieldData("t0434OutBlock1", "price", i));
            AnsiString cheqty = AnsiString(query->GetFieldData("t0434OutBlock1", "cheqty", i));
            AnsiString ordrem = AnsiString(query->GetFieldData("t0434OutBlock1", "ordrem", i));
            AnsiString status = AnsiString(query->GetFieldData("t0434OutBlock1", "status", i));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("주문정보: %s, %s, 수량=%s, 가격=%s, 체결=%s, 잔량=%s, 상태=%s", 
                    ordno.c_str(), medosu.c_str(), qty.c_str(), price.c_str(), 
                    cheqty.c_str(), ordrem.c_str(), status.c_str()));
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "체결/미체결 조회 처리 중 오류: " + e.Message);
    }
}

// 잔고평가 처리 (t0441)
void TLSXingAPI::HandlePositionDetail(TXAQuery* query, int blockCount) {
    try {
        // t0441OutBlock 요약 정보
        if (blockCount > 0) {
            AnsiString tdtsunik = AnsiString(query->GetFieldData("t0441OutBlock", "tdtsunik", 0));
            AnsiString tappamt = AnsiString(query->GetFieldData("t0441OutBlock", "tappamt", 0));
            AnsiString tsunik = AnsiString(query->GetFieldData("t0441OutBlock", "tsunik", 0));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("잔고요약: 매매손익=%s, 평가금액=%s, 평가손익=%s", 
                    tdtsunik.c_str(), tappamt.c_str(), tsunik.c_str()));
        }
        
        // t0441OutBlock1 상세 잔고
        int positionCount = query->GetBlockCount("t0441OutBlock1");
        for (int i = 0; i < positionCount; i++) {
            t0441OutBlock1 posData;
            memset(&posData, 0, sizeof(posData));
            
            AnsiString expcode = AnsiString(query->GetFieldData("t0441OutBlock1", "expcode", i));
            AnsiString medosu = AnsiString(query->GetFieldData("t0441OutBlock1", "medosu", i));
            AnsiString jqty = AnsiString(query->GetFieldData("t0441OutBlock1", "jqty", i));
            AnsiString pamt = AnsiString(query->GetFieldData("t0441OutBlock1", "pamt", i));
            AnsiString price = AnsiString(query->GetFieldData("t0441OutBlock1", "price", i));
            AnsiString dtsunik1 = AnsiString(query->GetFieldData("t0441OutBlock1", "dtsunik1", i));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("잔고상세: %s, %s, 수량=%s, 평균가=%s, 현재가=%s, 손익=%s", 
                    expcode.c_str(), medosu.c_str(), jqty.c_str(), 
                    pamt.c_str(), price.c_str(), dtsunik1.c_str()));
        }
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "잔고평가 처리 중 오류: " + e.Message);
    }
}

// 선물/옵션 차트 처리 (t8415)
void TLSXingAPI::HandleOptionChart(TXAQuery* query, int blockCount) {
    try {
        // t8415OutBlock 기본 정보
        if (blockCount > 0) {
            AnsiString shcode = AnsiString(query->GetFieldData("t8415OutBlock", "shcode", 0));
            AnsiString jisiga = AnsiString(query->GetFieldData("t8415OutBlock", "jisiga", 0));
            AnsiString jihigh = AnsiString(query->GetFieldData("t8415OutBlock", "jihigh", 0));
            AnsiString jilow = AnsiString(query->GetFieldData("t8415OutBlock", "jilow", 0));
            AnsiString jiclose = AnsiString(query->GetFieldData("t8415OutBlock", "jiclose", 0));
            
            WriteLog(TLogLevel::INFO, 
                AnsiString().sprintf("차트기본: %s, 전일 시=%s 고=%s 저=%s 종=%s", 
                    shcode.c_str(), jisiga.c_str(), jihigh.c_str(), 
                    jilow.c_str(), jiclose.c_str()));
        }
        
        // t8415OutBlock1 차트 데이터
        int chartCount = query->GetBlockCount("t8415OutBlock1");
        for (int i = 0; i < chartCount; i++) {
            t8415OutBlock1 chartData;
            memset(&chartData, 0, sizeof(chartData));
            
            AnsiString date = AnsiString(query->GetFieldData("t8415OutBlock1", "date", i));
            AnsiString time = AnsiString(query->GetFieldData("t8415OutBlock1", "time", i));
            AnsiString open = AnsiString(query->GetFieldData("t8415OutBlock1", "open", i));
            AnsiString high = AnsiString(query->GetFieldData("t8415OutBlock1", "high", i));
            AnsiString low = AnsiString(query->GetFieldData("t8415OutBlock1", "low", i));
            AnsiString close = AnsiString(query->GetFieldData("t8415OutBlock1", "close", i));
            AnsiString volume = AnsiString(query->GetFieldData("t8415OutBlock1", "jdiff_vol", i));
            
            if (i < 5) { // 처음 5개만 로그
                WriteLog(TLogLevel::DEBUG, 
                    AnsiString().sprintf("차트: %s %s OHLC=%s,%s,%s,%s V=%s", 
                        date.c_str(), time.c_str(), open.c_str(), high.c_str(), 
                        low.c_str(), close.c_str(), volume.c_str()));
            }
        }
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("차트 데이터 수신 완료: %d건", chartCount));
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "차트 데이터 처리 중 오류: " + e.Message);
    }
}

// 실시간 주문체결 처리 (C01)
void TLSXingAPI::HandleRealOrderExecution(TXAReal* real) {
    try {
        AnsiString ordno = AnsiString(real->GetFieldData("OutBlock", "ordno"));
        AnsiString accno = AnsiString(real->GetFieldData("OutBlock", "accno"));
        AnsiString expcode = AnsiString(real->GetFieldData("OutBlock", "expcode"));
        AnsiString cheprice = AnsiString(real->GetFieldData("OutBlock", "cheprice"));
        AnsiString chevol = AnsiString(real->GetFieldData("OutBlock", "chevol"));
        AnsiString chedate = AnsiString(real->GetFieldData("OutBlock", "chedate"));
        AnsiString chetime = AnsiString(real->GetFieldData("OutBlock", "chetime"));
        AnsiString dosugb = AnsiString(real->GetFieldData("OutBlock", "dosugb"));
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문체결: %s, %s, %s, 가격=%s, 수량=%s, %s %s", 
                ordno.c_str(), accno.c_str(), expcode.c_str(), 
                cheprice.c_str(), chevol.c_str(), chedate.c_str(), chetime.c_str()));
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 주문체결 처리 중 오류: " + e.Message);
    }
}

// 실시간 주문정정취소 처리 (H01)
void TLSXingAPI::HandleRealOrderModifyCancel(TXAReal* real) {
    try {
        AnsiString ordno = AnsiString(real->GetFieldData("OutBlock", "ordno"));
        AnsiString orgordno = AnsiString(real->GetFieldData("OutBlock", "orgordno"));
        AnsiString expcode = AnsiString(real->GetFieldData("OutBlock", "expcode"));
        AnsiString mocagb = AnsiString(real->GetFieldData("OutBlock", "mocagb"));
        AnsiString qty = AnsiString(real->GetFieldData("OutBlock", "qty"));
        AnsiString rejcode = AnsiString(real->GetFieldData("OutBlock", "rejcode"));
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문정정취소: %s->%s, %s, 구분=%s, 수량=%s, 거부코드=%s", 
                orgordno.c_str(), ordno.c_str(), expcode.c_str(), 
                mocagb.c_str(), qty.c_str(), rejcode.c_str()));
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 주문정정취소 처리 중 오류: " + e.Message);
    }
}

// 실시간 주문접수 처리 (O01)
void TLSXingAPI::HandleRealOrderAccept(TXAReal* real) {
    try {
        AnsiString ordno = AnsiString(real->GetFieldData("OutBlock", "ordno"));
        AnsiString acntno = AnsiString(real->GetFieldData("OutBlock", "acntno"));
        AnsiString isuno = AnsiString(real->GetFieldData("OutBlock", "isuno"));
        AnsiString ordqty = AnsiString(real->GetFieldData("OutBlock", "ordqty"));
        AnsiString ordprc = AnsiString(real->GetFieldData("OutBlock", "ordprc"));
        AnsiString bnstp = AnsiString(real->GetFieldData("OutBlock", "bnstp"));
        AnsiString rjtcode = AnsiString(real->GetFieldData("OutBlock", "rjtcode"));
        
        WriteLog(TLogLevel::INFO, 
            AnsiString().sprintf("주문접수: %s, %s, %s, 수량=%s, 가격=%s, 매매=%s, 거부=%s", 
                ordno.c_str(), acntno.c_str(), isuno.c_str(), 
                ordqty.c_str(), ordprc.c_str(), bnstp.c_str(), rjtcode.c_str()));
    }
    catch (const Exception& e) {
        WriteLog(TLogLevel::ERROR, "실시간 주문접수 처리 중 오류: " + e.Message);
    }
}