#include <vcl.h>
#pragma hdrstop
#include "TTestModeManager.h"
#include <SysUtils.hpp>
#include <IOUtils.hpp>

__fastcall TTestModeManager::TTestModeManager()
    : FCurrentMode(tmLive), FIsInitialized(false)
{
    FUtils = std::make_unique<TUtils>();
    
    // 기본 설정
    FConfig.mode = tmLive;
    FConfig.recordData = false;
    FConfig.useVirtualExecution = false;
    FConfig.dataFolder = ExtractFilePath(Application->ExeName) + "dataFolder";
    FConfig.playbackSpeed = 1.0;
}

__fastcall TTestModeManager::~TTestModeManager()
{
    CleanupCurrentMode();
}

void TTestModeManager::SetConfig(const TTestModeConfig& config)
{
    FConfig = config;
    
    if (FIsInitialized && FCurrentMode != config.mode) {
        SwitchMode(config.mode);
    }
}

void TTestModeManager::SwitchMode(TTestMode newMode)
{
    if (FCurrentMode == newMode && FIsInitialized) return;
    
    // 현재 모드 정리
    CleanupCurrentMode();
    
    FCurrentMode = newMode;
    
    // 새 모드 초기화
    switch (newMode) {
        case tmLive:
            InitializeLiveMode();
            break;
        case tmReplay:
            InitializeReplayMode();
            break;
        case tmBacktest:
            InitializeBacktestMode();
            break;
        case tmSimulation:
            InitializeSimulationMode();
            break;
    }
    
    FIsInitialized = true;
    
    if (OnModeChanged) {
        OnModeChanged(this);
    }
}

void TTestModeManager::InitializeLiveMode()
{
    // 실시간 모드: 데이터 기록 기능만 필요시 활성화
    if (FConfig.recordData) {
        FDataRecorder = std::make_unique<TMarketDataRecorder>();
        FDataRecorder->SetDataFolder(FConfig.dataFolder);
    }
    
    // 가상 체결 사용시 가상 체결 엔진 초기화
    if (FConfig.useVirtualExecution) {
        FVirtualExecution = std::make_unique<TVirtualExecutionEngine>();
        FVirtualExecution->OnOrderStatusChanged = OnOrderStatusChanged;
        FVirtualExecution->OnOrderFilled = OnOrderFilled;
    }
}

void TTestModeManager::InitializeReplayMode()
{
    // 재생 모드: 가상 시장과 가상 체결 엔진 초기화
    FVirtualMarket = std::make_unique<TVirtualMarket>();
    FVirtualMarket->SetDataFolder(FConfig.dataFolder);
    FVirtualMarket->SetDateRange(FConfig.replayStartDate, FConfig.replayEndDate);
    FVirtualMarket->SetTargetCodes(FConfig.targetCodes);
    FVirtualMarket->SetPlaybackSpeed(FConfig.playbackSpeed);
    
    // 이벤트 핸들러 연결
    FVirtualMarket->OnHogaReceived = [this](TObject* Sender, const THogaData& hoga) {
        if (OnHogaReceived) OnHogaReceived(Sender, hoga);
        if (FVirtualExecution) FVirtualExecution->OnHogaUpdate(hoga);
    };
    
    FVirtualMarket->OnJeobsuReceived = [this](TObject* Sender, const TJeobsuData& jeobsu) {
        if (OnJeobsuReceived) OnJeobsuReceived(Sender, jeobsu);
        if (FVirtualExecution) FVirtualExecution->OnJeobsuUpdate(jeobsu);
    };
    
    // 가상 체결 엔진 초기화
    FVirtualExecution = std::make_unique<TVirtualExecutionEngine>();
    FVirtualExecution->SetRealisticMode(true);
    FVirtualExecution->OnOrderStatusChanged = OnOrderStatusChanged;
    FVirtualExecution->OnOrderFilled = OnOrderFilled;
}

void TTestModeManager::InitializeBacktestMode()
{
    // 백테스트 모드: 백테스트 프레임워크 초기화
    FBacktestFramework = std::make_unique<TBacktestingFramework>();
    
    // 백테스트 이벤트를 외부로 전달
    FBacktestFramework->OnProgressUpdate = [this](TObject* Sender) {
        // 진행률 업데이트 이벤트
    };
    
    FBacktestFramework->OnBacktestCompleted = [this](TObject* Sender) {
        // 백테스트 완료 이벤트
    };
}

void TTestModeManager::InitializeSimulationMode()
{
    // 시뮬레이션 모드: 재생 모드와 유사하지만 더 빠른 속도
    InitializeReplayMode();
    
    if (FVirtualMarket) {
        FVirtualMarket->SetPlaybackSpeed(10.0); // 10배속
    }
    
    if (FVirtualExecution) {
        FVirtualExecution->SetLatency(1); // 최소 지연
        FVirtualExecution->SetRealisticMode(false); // 단순화된 체결
    }
}

void TTestModeManager::CleanupCurrentMode()
{
    // 모든 컴포넌트 정리
    if (FDataRecorder) {
        FDataRecorder->StopRecording();
        FDataRecorder.reset();
    }
    
    if (FVirtualMarket) {
        FVirtualMarket->StopPlayback();
        FVirtualMarket.reset();
    }
    
    if (FVirtualExecution) {
        FVirtualExecution.reset();
    }
    
    if (FBacktestFramework) {
        FBacktestFramework->StopBacktest();
        FBacktestFramework.reset();
    }
    
    FIsInitialized = false;
}

bool TTestModeManager::IsVirtualMode() const
{
    return FCurrentMode != tmLive;
}

void TTestModeManager::StartDataRecording()
{
    if (FDataRecorder && FCurrentMode == tmLive) {
        FDataRecorder->StartRecording();
    }
}

void TTestModeManager::StopDataRecording()
{
    if (FDataRecorder) {
        FDataRecorder->StopRecording();
    }
}

void TTestModeManager::RecordHoga(const THogaData& hoga)
{
    if (FDataRecorder && FDataRecorder->IsRecording()) {
        FDataRecorder->RecordHogaData(hoga);
    }
}

void TTestModeManager::RecordJeobsu(const TJeobsuData& jeobsu)
{
    if (FDataRecorder && FDataRecorder->IsRecording()) {
        FDataRecorder->RecordJeobsuData(jeobsu);
    }
}

void TTestModeManager::StartPlayback()
{
    if (FVirtualMarket) {
        FVirtualMarket->StartPlayback();
    }
}

void TTestModeManager::StopPlayback()
{
    if (FVirtualMarket) {
        FVirtualMarket->StopPlayback();
    }
}

void TTestModeManager::PausePlayback()
{
    if (FVirtualMarket) {
        FVirtualMarket->PausePlayback();
    }
}

void TTestModeManager::ResumePlayback()
{
    if (FVirtualMarket) {
        FVirtualMarket->ResumePlayback();
    }
}

void TTestModeManager::SeekTo(TDateTime time)
{
    if (FVirtualMarket) {
        FVirtualMarket->SeekTo(time);
    }
}

void TTestModeManager::SetPlaybackSpeed(double speed)
{
    if (FVirtualMarket) {
        FVirtualMarket->SetPlaybackSpeed(speed);
    }
}

AnsiString TTestModeManager::SubmitOrder(const TOrderInfo& order)
{
    if (FVirtualExecution) {
        // 가상 체결 모드
        return FVirtualExecution->SubmitOrder(order);
    } else {
        // 실제 주문 처리 (라이브 모드에서는 실제 브로커 API 호출)
        // 여기서는 실제 주문 매니저 호출이 필요
        return ""; // 실제 구현 필요
    }
}

bool TTestModeManager::CancelOrder(const AnsiString& orderNo)
{
    if (FVirtualExecution) {
        return FVirtualExecution->CancelOrder(orderNo);
    } else {
        // 실제 주문 취소
        return false; // 실제 구현 필요
    }
}

bool TTestModeManager::ModifyOrder(const AnsiString& orderNo, double newPrice, int newQuantity)
{
    if (FVirtualExecution) {
        return FVirtualExecution->ModifyOrder(orderNo, newPrice, newQuantity);
    } else {
        // 실제 주문 수정
        return false; // 실제 구현 필요
    }
}

THogaData TTestModeManager::GetCurrentHoga(const AnsiString& code)
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetCurrentHoga(code);
    } else {
        // 실제 호가 데이터 조회
        return THogaData(); // 실제 구현 필요
    }
}

TJeobsuData TTestModeManager::GetCurrentJeobsu(const AnsiString& code)
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetCurrentJeobsu(code);
    } else {
        // 실제 체결 데이터 조회
        return TJeobsuData(); // 실제 구현 필요
    }
}

double TTestModeManager::GetCurrentPrice(const AnsiString& code)
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetCurrentPrice(code);
    } else {
        // 실제 현재가 조회
        return 0.0; // 실제 구현 필요
    }
}

void TTestModeManager::SetBacktestParameters(const TBacktestParameters& params)
{
    if (FBacktestFramework) {
        FBacktestFramework->SetParameters(params);
    }
}

void TTestModeManager::StartBacktest()
{
    if (FBacktestFramework) {
        FBacktestFramework->StartBacktest();
    }
}

TBacktestResult TTestModeManager::GetBacktestResult()
{
    if (FBacktestFramework) {
        return FBacktestFramework->GetResult();
    }
    return TBacktestResult();
}

bool TTestModeManager::IsRecording() const
{
    return FDataRecorder && FDataRecorder->IsRecording();
}

bool TTestModeManager::IsPlaying() const
{
    if (FVirtualMarket) {
        return FVirtualMarket->IsPlaying();
    }
    if (FBacktestFramework) {
        return FBacktestFramework->IsRunning();
    }
    return false;
}

double TTestModeManager::GetPlaybackProgress() const
{
    if (FBacktestFramework) {
        return FBacktestFramework->GetCurrentProgress();
    }
    return 0.0;
}

TDateTime TTestModeManager::GetCurrentTime() const
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetCurrentTime();
    }
    return Now();
}

std::vector<AnsiString> TTestModeManager::GetAvailableDates()
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetAvailableDates();
    }
    return std::vector<AnsiString>();
}

std::vector<AnsiString> TTestModeManager::GetAvailableCodes(TDateTime date)
{
    if (FVirtualMarket) {
        return FVirtualMarket->GetAvailableCodes(date);
    }
    return std::vector<AnsiString>();
}

void TTestModeManager::CleanupOldData(int daysToKeep)
{
    try {
        TDateTime cutoffDate = Date() - daysToKeep;
        AnsiString cutoffStr = FormatDateTime("yyyymmdd", cutoffDate);
        
        // 호가 데이터 정리
        TSearchRec sr;
        AnsiString hogaPath = FConfig.dataFolder + "\\hoga\\*.txt";
        
        if (FindFirst(hogaPath, faAnyFile, sr) == 0) {
            do {
                AnsiString fileName = sr.Name;
                // 파일명에서 날짜 추출하여 비교
                int pos = fileName.LastDelimiter("_");
                if (pos > 0) {
                    AnsiString dateStr = fileName.SubString(pos + 1, 8);
                    if (dateStr < cutoffStr) {
                        DeleteFile(FConfig.dataFolder + "\\hoga\\" + fileName);
                    }
                }
            } while (FindNext(sr) == 0);
            FindClose(sr);
        }
        
        // 체결 데이터 정리
        AnsiString jeobsuPath = FConfig.dataFolder + "\\jeobsu\\*.txt";
        
        if (FindFirst(jeobsuPath, faAnyFile, sr) == 0) {
            do {
                AnsiString fileName = sr.Name;
                int pos = fileName.LastDelimiter("_");
                if (pos > 0) {
                    AnsiString dateStr = fileName.SubString(pos + 1, 8);
                    if (dateStr < cutoffStr) {
                        DeleteFile(FConfig.dataFolder + "\\jeobsu\\" + fileName);
                    }
                }
            } while (FindNext(sr) == 0);
            FindClose(sr);
        }
    }
    catch (const Exception& e) {
        // 정리 오류 처리
    }
}

void TTestModeManager::SaveConfig(const AnsiString& fileName)
{
    try {
        TIniFile* ini = new TIniFile(fileName);
        
        ini->WriteInteger("TestMode", "Mode", static_cast<int>(FConfig.mode));
        ini->WriteBool("TestMode", "RecordData", FConfig.recordData);
        ini->WriteBool("TestMode", "UseVirtualExecution", FConfig.useVirtualExecution);
        ini->WriteString("TestMode", "DataFolder", FConfig.dataFolder);
        ini->WriteDateTime("TestMode", "ReplayStartDate", FConfig.replayStartDate);
        ini->WriteDateTime("TestMode", "ReplayEndDate", FConfig.replayEndDate);
        ini->WriteFloat("TestMode", "PlaybackSpeed", FConfig.playbackSpeed);
        
        // 대상 종목 저장
        AnsiString codes = "";
        for (size_t i = 0; i < FConfig.targetCodes.size(); i++) {
            if (i > 0) codes += ",";
            codes += FConfig.targetCodes[i];
        }
        ini->WriteString("TestMode", "TargetCodes", codes);
        
        delete ini;
    }
    catch (const Exception& e) {
        throw Exception("설정 저장 오류: " + e.Message);
    }
}

void TTestModeManager::LoadConfig(const AnsiString& fileName)
{
    try {
        if (!FileExists(fileName)) return;
        
        TIniFile* ini = new TIniFile(fileName);
        
        FConfig.mode = static_cast<TTestMode>(ini->ReadInteger("TestMode", "Mode", 0));
        FConfig.recordData = ini->ReadBool("TestMode", "RecordData", false);
        FConfig.useVirtualExecution = ini->ReadBool("TestMode", "UseVirtualExecution", false);
        FConfig.dataFolder = ini->ReadString("TestMode", "DataFolder", 
                                            ExtractFilePath(Application->ExeName) + "dataFolder");
        FConfig.replayStartDate = ini->ReadDateTime("TestMode", "ReplayStartDate", Date() - 7);
        FConfig.replayEndDate = ini->ReadDateTime("TestMode", "ReplayEndDate", Date());
        FConfig.playbackSpeed = ini->ReadFloat("TestMode", "PlaybackSpeed", 1.0);
        
        // 대상 종목 로드
        AnsiString codes = ini->ReadString("TestMode", "TargetCodes", "");
        FConfig.targetCodes.clear();
        if (!codes.IsEmpty()) {
            TStringList* codeList = new TStringList();
            codeList->Delimiter = ',';
            codeList->DelimitedText = codes;
            
            for (int i = 0; i < codeList->Count; i++) {
                FConfig.targetCodes.push_back(codeList->Strings[i]);
            }
            
            delete codeList;
        }
        
        delete ini;
    }
    catch (const Exception& e) {
        throw Exception("설정 로드 오류: " + e.Message);
    }
}