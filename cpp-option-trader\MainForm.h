#ifndef MainFormH
#define MainFormH

#include <System.Classes.hpp>
#include <Vcl.Controls.hpp>
#include <Vcl.StdCtrls.hpp>
#include <Vcl.Forms.hpp>
#include <Vcl.ExtCtrls.hpp>
#include <Vcl.Grids.hpp>
#include <Vcl.ComCtrls.hpp>
#include <Vcl.Menus.hpp>
#include <memory>
#include "TIntegratedTradingSystem.h"
#include "DataTypes.h"

class TMainForm : public TForm
{
__published:
    // Menu
    TMainMenu* MainMenu1;
    TMenuItem* MenuFile;
    TMenuItem* MenuConnect;
    TMenuItem* MenuDisconnect;
    TMenuItem* MenuSettings;
    TMenuItem* MenuExit;
    TMenuItem* MenuTrading;
    TMenuItem* MenuStartTrading;
    TMenuItem* MenuStopTrading;
    TMenuItem* MenuHelp;
    TMenuItem* MenuAbout;

    // Panels
    TPanel* PanelTop;
    TPanel* PanelBottom;
    TPanel* PanelLeft;
    TPanel* PanelRight;
    TPanel* PanelCenter;

    // Connection Status
    TLabel* LabelConnectionStatus;
    TLabel* LabelTradingStatus;
    TButton* ButtonConnect;
    TButton* ButtonDisconnect;

    // Trading Controls
    TGroupBox* GroupBoxTrading;
    TLabel* LabelOptionCode;
    TEdit* EditOptionCode;
    TLabel* LabelQuantity;
    TEdit* EditQuantity;
    TButton* ButtonStartTrading;
    TButton* ButtonStopTrading;
    TCheckBox* CheckBoxAutoTrading;

    // Market Data Grid
    TGroupBox* GroupBoxMarketData;
    TStringGrid* GridMarketData;

    // Position Grid
    TGroupBox* GroupBoxPositions;
    TStringGrid* GridPositions;

    // Order Grid
    TGroupBox* GroupBoxOrders;
    TStringGrid* GridOrders;

    // Log
    TGroupBox* GroupBoxLog;
    TMemo* MemoLog;

    // Status Bar
    TStatusBar* StatusBar1;

    // Timer
    TTimer* TimerUpdate;

    // Event Handlers
    void __fastcall FormCreate(TObject* Sender);
    void __fastcall FormDestroy(TObject* Sender);
    void __fastcall FormClose(TObject* Sender, TCloseAction& Action);
    void __fastcall ButtonConnectClick(TObject* Sender);
    void __fastcall ButtonDisconnectClick(TObject* Sender);
    void __fastcall ButtonStartTradingClick(TObject* Sender);
    void __fastcall ButtonStopTradingClick(TObject* Sender);
    void __fastcall MenuConnectClick(TObject* Sender);
    void __fastcall MenuDisconnectClick(TObject* Sender);
    void __fastcall MenuSettingsClick(TObject* Sender);
    void __fastcall MenuExitClick(TObject* Sender);
    void __fastcall MenuStartTradingClick(TObject* Sender);
    void __fastcall MenuStopTradingClick(TObject* Sender);
    void __fastcall MenuAboutClick(TObject* Sender);
    void __fastcall TimerUpdateTimer(TObject* Sender);

private:
    std::unique_ptr<TIntegratedTradingSystem> FTradingSystem;
    bool FIsConnected;
    bool FIsTrading;

    void UpdateConnectionStatus();
    void UpdateTradingStatus();
    void UpdateMarketDataGrid();
    void UpdatePositionsGrid();
    void UpdateOrdersGrid();
    void InitializeGrids();
    void LogMessage(const AnsiString& message);

    // Event Handlers for Trading System
    void OnConnectionStatusChanged(TObject* Sender, bool connected);
    void OnHogaReceived(TObject* Sender, const THogaData& hoga);
    void OnJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu);
    void OnOrderStatusChanged(TObject* Sender, const TOrderInfo& order);
    void OnPositionUpdated(TObject* Sender, const TPositionInfo& position);
    void OnErrorOccurred(TObject* Sender, const AnsiString& error);
    void OnBarCompleted(const AnsiString& code, const TBarData& bar);

public:
    __fastcall TMainForm(TComponent* Owner);
};

extern PACKAGE TMainForm* MainForm;

#endif