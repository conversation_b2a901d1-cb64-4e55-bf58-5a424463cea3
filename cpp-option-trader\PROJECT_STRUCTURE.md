# OptionTrader C++Builder 포팅 프로젝트

## 프로젝트 구조

```
project_OptWTrader/
├── src/                     # 소스 코드
│   ├── core/               # 핵심 클래스
│   │   ├── TIntegratedTradingSystem.h/cpp
│   │   ├── TOrderManager.h/cpp
│   │   ├── TTrader.h/cpp
│   │   └── TOptionStrategy.h/cpp
│   ├── api/                # API 관련
│   │   ├── TXingAPIWrapper.h/cpp
│   │   └── TAPIEventHandler.h/cpp
│   ├── data/               # 데이터 처리
│   │   ├── DataTypes.h     # 데이터 구조체 및 Enum
│   │   ├── TDataLogger.h/cpp
│   │   └── TDataProcessor.h/cpp
│   ├── ui/                 # UI 관련
│   │   ├── TMainForm.h/cpp
│   │   ├── TSettingsForm.h/cpp
│   │   └── TLogForm.h/cpp
│   ├── utils/              # 유틸리티
│   │   ├── TConfigManager.h/cpp
│   │   ├── TLogger.h/cpp
│   │   └── TUtils.h/cpp
│   └── threading/          # 멀티스레딩
│       ├── TRealTimeDataThread.h/cpp
│       └── TOrderProcessThread.h/cpp
├── forms/                  # VCL Form 파일
│   ├── MainForm.dfm
│   ├── SettingsForm.dfm
│   └── LogForm.dfm
├── config/                 # 설정 파일
│   ├── settings.ini
│   └── strategy_params.ini
├── docs/                   # 문서
│   ├── API_GUIDE.md
│   ├── CLASS_DIAGRAM.md
│   └── CONVERSION_NOTES.md
└── README.md
```

## 주요 변환 포인트

### 1. Python → C++Builder 매핑
- Python 클래스 → C++Builder 클래스 (T prefix)
- pandas DataFrame → TClientDataSet 또는 커스텀 컨테이너
- PyQt5 → VCL Forms
- threading → TThread
- win32com → COM 인터페이스

### 2. 핵심 아키텍처
- **메인 스레드**: UI 및 이벤트 처리
- **데이터 스레드**: 실시간 데이터 수신 및 처리
- **주문 스레드**: 주문 처리 및 상태 관리
- **로깅 스레드**: 비동기 로그 기록

### 3. COM 인터페이스 연동
- XASession, XAQuery, XAReal COM 객체 활용
- 이벤트 핸들러를 통한 비동기 처리
- COM 오류 처리 및 재연결 로직

### 4. 데이터 관리
- STL 컨테이너 활용 (std::map, std::vector, std::queue)
- 스레드 안전성을 위한 TCriticalSection
- 메모리 관리를 위한 스마트 포인터