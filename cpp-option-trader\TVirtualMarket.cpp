#include <vcl.h>
#pragma hdrstop
#include "TVirtualMarket.h"
#include <SysUtils.hpp>
#include <IOUtils.hpp>
#include <algorithm>

__fastcall TVirtualMarket::TVirtualMarket()
    : FPlaybackSpeed(1.0), FIsPlaying(false), FIsPaused(false)
{
    FUtils = std::make_unique<TUtils>();
    FDataFolder = ExtractFilePath(Application->ExeName) + "dataFolder";
}

__fastcall TVirtualMarket::~TVirtualMarket()
{
    StopPlayback();
}

void TVirtualMarket::SetDataFolder(const AnsiString& folder)
{
    FDataFolder = folder;
}

void TVirtualMarket::SetDateRange(TDateTime startDate, TDateTime endDate)
{
    FStartDate = startDate;
    FEndDate = endDate;
}

void TVirtualMarket::SetPlaybackSpeed(double speed)
{
    std::lock_guard<std::mutex> lock(FPlaybackMutex);
    FPlaybackSpeed = speed;
}

void TVirtualMarket::SetTargetCodes(const std::vector<AnsiString>& codes)
{
    FTargetCodes = codes;
}

void TVirtualMarket::StartPlayback()
{
    if (FIsPlaying) return;
    
    try {
        // 데이터 로드
        LoadMarketData();
        
        FIsPlaying = true;
        FIsPaused = false;
        FCurrentTime = FStartDate;
        
        // 재생 스레드 시작
        FPlaybackThread = std::thread(&TVirtualMarket::PlaybackThreadProc, this);
    }
    catch (const Exception& e) {
        FIsPlaying = false;
        throw;
    }
}

void TVirtualMarket::StopPlayback()
{
    if (!FIsPlaying) return;
    
    FIsPlaying = false;
    FIsPaused = false;
    
    if (FPlaybackThread.joinable()) {
        FPlaybackThread.join();
    }
    
    // 데이터 초기화
    std::lock_guard<std::mutex> lock(FDataMutex);
    while (!FDataEvents.empty()) {
        FDataEvents.pop();
    }
    FCurrentHoga.clear();
    FCurrentJeobsu.clear();
    FCurrentPrices.clear();
}

void TVirtualMarket::PausePlayback()
{
    std::lock_guard<std::mutex> lock(FPlaybackMutex);
    FIsPaused = true;
}

void TVirtualMarket::ResumePlayback()
{
    std::lock_guard<std::mutex> lock(FPlaybackMutex);
    FIsPaused = false;
}

void TVirtualMarket::SeekTo(TDateTime time)
{
    if (time < FStartDate || time > FEndDate) return;
    
    std::lock_guard<std::mutex> lock(FPlaybackMutex);
    FCurrentTime = time;
    
    // 해당 시점까지의 데이터로 현재 상태 업데이트
    std::lock_guard<std::mutex> dataLock(FDataMutex);
    FCurrentHoga.clear();
    FCurrentJeobsu.clear();
    FCurrentPrices.clear();
}

void TVirtualMarket::LoadMarketData()
{
    std::lock_guard<std::mutex> lock(FDataMutex);
    
    // 기존 데이터 클리어
    while (!FDataEvents.empty()) {
        FDataEvents.pop();
    }
    
    // 날짜 범위에 해당하는 모든 파일 로드
    TDateTime currentDate = FStartDate;
    while (currentDate <= FEndDate) {
        AnsiString dateStr = FormatDateTime("yyyymmdd", currentDate);
        
        for (const auto& code : FTargetCodes) {
            // 호가 데이터 로드
            AnsiString hogaFile = FDataFolder + "\\hoga\\hoga_" + code + "_" + dateStr + ".txt";
            if (FileExists(hogaFile)) {
                LoadHogaData(hogaFile, code);
            }
            
            // 체결 데이터 로드
            AnsiString jeobsuFile = FDataFolder + "\\jeobsu\\jeobsu_" + code + "_" + dateStr + ".txt";
            if (FileExists(jeobsuFile)) {
                LoadJeobsuData(jeobsuFile, code);
            }
        }
        
        currentDate += 1; // 하루 증가
    }
}

void TVirtualMarket::LoadHogaData(const AnsiString& fileName, const AnsiString& code)
{
    try {
        std::ifstream file(fileName.c_str());
        if (!file.is_open()) return;
        
        std::string line;
        bool isFirstLine = true;
        TDateTime baseDate = Date(); // 파일명에서 날짜 추출해야 함
        
        // 파일명에서 날짜 추출
        int pos = fileName.LastDelimiter("_");
        if (pos > 0) {
            AnsiString dateStr = fileName.SubString(pos + 1, 8);
            try {
                int year = StrToInt(dateStr.SubString(1, 4));
                int month = StrToInt(dateStr.SubString(5, 2));
                int day = StrToInt(dateStr.SubString(7, 2));
                baseDate = EncodeDate(year, month, day);
            }
            catch (...) {
                baseDate = Date();
            }
        }
        
        while (std::getline(file, line)) {
            if (isFirstLine) {
                isFirstLine = false;
                continue; // 헤더 라인 스킵
            }
            
            AnsiString lineStr = line.c_str();
            if (!lineStr.IsEmpty()) {
                THogaData hoga = ParseHogaLine(lineStr, code, baseDate);
                if (hoga.time >= FStartDate && hoga.time <= FEndDate) {
                    TMarketDataEvent event;
                    event.time = hoga.time;
                    event.dataType = "HOGA";
                    event.code = code;
                    event.hoga = hoga;
                    FDataEvents.push(event);
                }
            }
        }
    }
    catch (const Exception& e) {
        // 파일 로드 오류 처리
    }
}

void TVirtualMarket::LoadJeobsuData(const AnsiString& fileName, const AnsiString& code)
{
    try {
        std::ifstream file(fileName.c_str());
        if (!file.is_open()) return;
        
        std::string line;
        bool isFirstLine = true;
        TDateTime baseDate = Date();
        
        // 파일명에서 날짜 추출
        int pos = fileName.LastDelimiter("_");
        if (pos > 0) {
            AnsiString dateStr = fileName.SubString(pos + 1, 8);
            try {
                int year = StrToInt(dateStr.SubString(1, 4));
                int month = StrToInt(dateStr.SubString(5, 2));
                int day = StrToInt(dateStr.SubString(7, 2));
                baseDate = EncodeDate(year, month, day);
            }
            catch (...) {
                baseDate = Date();
            }
        }
        
        while (std::getline(file, line)) {
            if (isFirstLine) {
                isFirstLine = false;
                continue; // 헤더 라인 스킵
            }
            
            AnsiString lineStr = line.c_str();
            if (!lineStr.IsEmpty()) {
                TJeobsuData jeobsu = ParseJeobsuLine(lineStr, code, baseDate);
                if (jeobsu.time >= FStartDate && jeobsu.time <= FEndDate) {
                    TMarketDataEvent event;
                    event.time = jeobsu.time;
                    event.dataType = "JEOBSU";
                    event.code = code;
                    event.jeobsu = jeobsu;
                    FDataEvents.push(event);
                }
            }
        }
    }
    catch (const Exception& e) {
        // 파일 로드 오류 처리
    }
}

THogaData TVirtualMarket::ParseHogaLine(const AnsiString& line, const AnsiString& code, TDateTime baseDate)
{
    THogaData hoga;
    hoga.code = code;
    
    try {
        TStringList* fields = new TStringList();
        fields->Delimiter = ',';
        fields->DelimitedText = line;
        
        if (fields->Count >= 23) {
            // 시간 파싱
            AnsiString timeStr = fields->Strings[0];
            TDateTime time = StrToTimeDef(timeStr, 0);
            hoga.time = baseDate + time;
            
            // 호가 데이터 파싱
            hoga.bidPrice1 = StrToFloatDef(fields->Strings[2], 0);
            hoga.bidQty1 = StrToIntDef(fields->Strings[3], 0);
            hoga.bidPrice2 = StrToFloatDef(fields->Strings[4], 0);
            hoga.bidQty2 = StrToIntDef(fields->Strings[5], 0);
            hoga.bidPrice3 = StrToFloatDef(fields->Strings[6], 0);
            hoga.bidQty3 = StrToIntDef(fields->Strings[7], 0);
            hoga.bidPrice4 = StrToFloatDef(fields->Strings[8], 0);
            hoga.bidQty4 = StrToIntDef(fields->Strings[9], 0);
            hoga.bidPrice5 = StrToFloatDef(fields->Strings[10], 0);
            hoga.bidQty5 = StrToIntDef(fields->Strings[11], 0);
            
            hoga.askPrice1 = StrToFloatDef(fields->Strings[12], 0);
            hoga.askQty1 = StrToIntDef(fields->Strings[13], 0);
            hoga.askPrice2 = StrToFloatDef(fields->Strings[14], 0);
            hoga.askQty2 = StrToIntDef(fields->Strings[15], 0);
            hoga.askPrice3 = StrToFloatDef(fields->Strings[16], 0);
            hoga.askQty3 = StrToIntDef(fields->Strings[17], 0);
            hoga.askPrice4 = StrToFloatDef(fields->Strings[18], 0);
            hoga.askQty4 = StrToIntDef(fields->Strings[19], 0);
            hoga.askPrice5 = StrToFloatDef(fields->Strings[20], 0);
            hoga.askQty5 = StrToIntDef(fields->Strings[21], 0);
            
            hoga.totalBidQty = StrToIntDef(fields->Strings[22], 0);
            hoga.totalAskQty = StrToIntDef(fields->Strings[23], 0);
        }
        
        delete fields;
    }
    catch (...) {
        // 파싱 오류 시 기본값 유지
    }
    
    return hoga;
}

TJeobsuData TVirtualMarket::ParseJeobsuLine(const AnsiString& line, const AnsiString& code, TDateTime baseDate)
{
    TJeobsuData jeobsu;
    jeobsu.code = code;
    
    try {
        TStringList* fields = new TStringList();
        fields->Delimiter = ',';
        fields->DelimitedText = line;
        
        if (fields->Count >= 8) {
            // 시간 파싱
            AnsiString timeStr = fields->Strings[0];
            TDateTime time = StrToTimeDef(timeStr, 0);
            jeobsu.time = baseDate + time;
            
            // 체결 데이터 파싱
            jeobsu.price = StrToFloatDef(fields->Strings[2], 0);
            jeobsu.quantity = StrToIntDef(fields->Strings[3], 0);
            jeobsu.volume = StrToIntDef(fields->Strings[4], 0);
            jeobsu.change = StrToFloatDef(fields->Strings[5], 0);
            jeobsu.changeRate = StrToFloatDef(fields->Strings[6], 0);
        }
        
        delete fields;
    }
    catch (...) {
        // 파싱 오류 시 기본값 유지
    }
    
    return jeobsu;
}

void TVirtualMarket::PlaybackThreadProc()
{
    while (FIsPlaying) {
        {
            std::lock_guard<std::mutex> lock(FPlaybackMutex);
            if (FIsPaused) {
                Sleep(100);
                continue;
            }
        }
        
        std::lock_guard<std::mutex> dataLock(FDataMutex);
        
        if (FDataEvents.empty()) {
            // 재생 완료
            FIsPlaying = false;
            if (OnPlaybackFinished) {
                TThread::Synchronize(nullptr, [this]() {
                    OnPlaybackFinished(this);
                });
            }
            break;
        }
        
        TMarketDataEvent event = FDataEvents.top();
        
        // 현재 시간과 이벤트 시간 비교
        if (event.time <= FCurrentTime) {
            FDataEvents.pop();
            ProcessDataEvent(event);
        } else {
            // 다음 이벤트까지 대기
            double waitTime = (event.time - FCurrentTime) * 24 * 60 * 60 * 1000; // 밀리초 변환
            waitTime /= FPlaybackSpeed; // 재생 속도 적용
            
            if (waitTime > 0 && waitTime < 10000) { // 최대 10초 대기
                Sleep(static_cast<DWORD>(waitTime));
            }
            
            FCurrentTime = event.time;
        }
    }
}

void TVirtualMarket::ProcessDataEvent(const TMarketDataEvent& event)
{
    if (event.dataType == "HOGA") {
        FCurrentHoga[event.code] = event.hoga;
        if (OnHogaReceived) {
            TThread::Synchronize(nullptr, [this, event]() {
                OnHogaReceived(this, event.hoga);
            });
        }
    }
    else if (event.dataType == "JEOBSU") {
        FCurrentJeobsu[event.code] = event.jeobsu;
        FCurrentPrices[event.code] = event.jeobsu.price;
        if (OnJeobsuReceived) {
            TThread::Synchronize(nullptr, [this, event]() {
                OnJeobsuReceived(this, event.jeobsu);
            });
        }
    }
}

THogaData TVirtualMarket::GetCurrentHoga(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FDataMutex);
    auto it = FCurrentHoga.find(code);
    if (it != FCurrentHoga.end()) {
        return it->second;
    }
    return THogaData(); // 빈 데이터 반환
}

TJeobsuData TVirtualMarket::GetCurrentJeobsu(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FDataMutex);
    auto it = FCurrentJeobsu.find(code);
    if (it != FCurrentJeobsu.end()) {
        return it->second;
    }
    return TJeobsuData(); // 빈 데이터 반환
}

double TVirtualMarket::GetCurrentPrice(const AnsiString& code)
{
    std::lock_guard<std::mutex> lock(FDataMutex);
    auto it = FCurrentPrices.find(code);
    if (it != FCurrentPrices.end()) {
        return it->second;
    }
    return 0.0;
}

std::vector<AnsiString> TVirtualMarket::GetAvailableDates()
{
    std::vector<AnsiString> dates;
    
    try {
        TSearchRec sr;
        AnsiString searchPath = FDataFolder + "\\hoga\\*.txt";
        
        if (FindFirst(searchPath, faAnyFile, sr) == 0) {
            do {
                AnsiString fileName = sr.Name;
                // hoga_CODE_YYYYMMDD.txt 형식에서 날짜 추출
                int pos1 = fileName.LastDelimiter("_");
                int pos2 = fileName.LastDelimiter(".");
                if (pos1 > 0 && pos2 > pos1) {
                    AnsiString dateStr = fileName.SubString(pos1 + 1, pos2 - pos1 - 1);
                    if (dateStr.Length() == 8) {
                        dates.push_back(dateStr);
                    }
                }
            } while (FindNext(sr) == 0);
            FindClose(sr);
        }
    }
    catch (...) {
        // 검색 오류 처리
    }
    
    // 중복 제거 및 정렬
    std::sort(dates.begin(), dates.end());
    dates.erase(std::unique(dates.begin(), dates.end()), dates.end());
    
    return dates;
}

std::vector<AnsiString> TVirtualMarket::GetAvailableCodes(TDateTime date)
{
    std::vector<AnsiString> codes;
    
    try {
        AnsiString dateStr = FormatDateTime("yyyymmdd", date);
        TSearchRec sr;
        AnsiString searchPath = FDataFolder + "\\hoga\\hoga_*_" + dateStr + ".txt";
        
        if (FindFirst(searchPath, faAnyFile, sr) == 0) {
            do {
                AnsiString fileName = sr.Name;
                // hoga_CODE_YYYYMMDD.txt 형식에서 코드 추출
                AnsiString prefix = "hoga_";
                AnsiString suffix = "_" + dateStr + ".txt";
                
                if (fileName.Pos(prefix) == 1 && fileName.Pos(suffix) > 0) {
                    int startPos = prefix.Length() + 1;
                    int endPos = fileName.Pos(suffix) - 1;
                    AnsiString code = fileName.SubString(startPos, endPos - startPos + 1);
                    codes.push_back(code);
                }
            } while (FindNext(sr) == 0);
            FindClose(sr);
        }
    }
    catch (...) {
        // 검색 오류 처리
    }
    
    std::sort(codes.begin(), codes.end());
    
    return codes;
}