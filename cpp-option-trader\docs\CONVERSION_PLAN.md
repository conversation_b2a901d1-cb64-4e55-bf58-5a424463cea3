# OptionTrader Python → C++Builder 변환 계획

## 개요
이 문서는 Python 기반 OptionTrader 시스템을 Embarcadero C++Builder 11로 포팅하는 구체적인 작업 계획을 제시합니다.

## 1. 프로젝트 구조 및 진행 상황

### ✅ 완료된 작업
1. **프로젝트 구조 생성** - project_OptWTrader 폴더 및 하위 디렉토리 구성
2. **UI 분석 및 설계** - SBCPP.png 분석 후 VCL Form 설계 완료
3. **핵심 클래스 설계** - 주요 헤더 파일 생성 완료
4. **데이터 구조 정의** - DataTypes.h 완성
5. **전략 엔진 설계** - TOptionStrategy 클래스 완성

### 🚧 진행 중인 작업
- **XingAPI COM 래퍼** - TXingAPIWrapper.h 완성, 구현 필요

### ⏳ 예정된 작업
- **멀티스레딩 시스템** - 실시간 데이터 처리 스레드
- **설정 관리 시스템** - TIniFile 기반 설정 관리
- **로깅 시스템** - 비동기 로깅 시스템

## 2. 핵심 변환 매핑

### Python → C++Builder 클래스 매핑
| Python 클래스 | C++Builder 클래스 | 상태 | 설명 |
|---------------|------------------|------|------|
| IntegratedTradingSystem | TIntegratedTradingSystem | ✅ | 메인 시스템 관리자 |
| OrderManager | TOrderManager | ✅ | 주문 관리 및 추적 |
| Trader | TTrader | ✅ | 거래 실행 및 포지션 관리 |
| OptionStrategy | TOptionStrategy | ✅ | 전략 엔진 및 지표 계산 |
| XingAPI | TXingAPIWrapper | 🚧 | COM 인터페이스 래퍼 |
| DataLogger | TDataLogger | ⏳ | 데이터 로깅 시스템 |
| Receiver | TRealTimeDataThread | 🚧 | 실시간 데이터 수신 |

### 라이브러리 변환 매핑
| Python 라이브러리 | C++Builder 대체 | 변환 방법 |
|------------------|----------------|-----------|
| pandas | TClientDataSet/STL | 데이터프레임 → 데이터셋/컨테이너 |
| numpy | 직접 구현/Intel MKL | 배열 연산 함수 직접 구현 |
| talib | 직접 구현 | 기술적 지표 계산 함수 |
| PyQt5 | VCL/FMX | UI 프레임워크 |
| win32com | COM 인터페이스 | ActiveX/COM 컴포넌트 |
| threading | TThread | 멀티스레딩 클래스 |
| json | TJSONObject | JSON 처리 |
| configparser | TIniFile | 설정 파일 관리 |

## 3. 세부 구현 계획

### 3.1 XingAPI COM 인터페이스 (우선순위: 높음)

#### 구현 단계:
1. **COM 객체 초기화**
   ```cpp
   // XASession, XAQuery, XAReal COM 객체 생성
   FXASession = CreateOleObject("XA_Session.XASession");
   ```

2. **이벤트 핸들링**
   ```cpp
   // COM 이벤트를 VCL 이벤트로 변환
   void __fastcall OnXASessionEvent(TObject* Sender);
   ```

3. **데이터 파싱**
   ```cpp
   // COM Variant 데이터를 C++ 구조체로 변환
   THogaData ParseHogaData(const TXAReal& real);
   ```

#### 주요 기능:
- 서버 연결/로그인
- 실시간 호가/체결 데이터 수신
- 주문 전송/취소/정정
- 계좌 정보 조회

### 3.2 멀티스레딩 시스템 (우선순위: 높음)

#### 스레드 구조:
1. **TRealTimeDataThread**: 실시간 데이터 수신
2. **TOrderProcessThread**: 주문 처리
3. **TUIUpdateThread**: UI 업데이트

#### 구현 방법:
```cpp
class TRealTimeDataThread : public TThread {
    void __fastcall Execute() override;
    void ProcessDataQueue();
};
```

#### 스레드 간 통신:
- TCriticalSection으로 동기화
- TQueue로 메시지 전달
- Synchronize로 UI 업데이트

### 3.3 기술적 지표 계산 엔진

#### 구현할 지표:
1. **Stochastic Oscillator**
   ```cpp
   TStochasticData CalculateStochastic(const AnsiString& code);
   ```

2. **CCI (Commodity Channel Index)**
   ```cpp
   TCCIData CalculateCCI(const AnsiString& code);
   ```

3. **DMI/ADX**
   ```cpp
   TDMIData CalculateDMI(const AnsiString& code);
   ```

#### 데이터 관리:
- std::deque로 가격 데이터 링 버퍼
- 캐시 메커니즘으로 성능 최적화

### 3.4 주문 관리 시스템

#### 핵심 기능:
1. **1호가 추적 주문**
   - 15초 전 조건 확인
   - 1호가 지정가 주문
   - 최대 3회 정정
   - 봉완성 시 시장가 전환

2. **익절/손절 관리**
   - 체결 후 자동 익절 주문
   - 실시간 수익률 추적

#### 상태 관리:
```cpp
enum class TOrderState {
    WAITING, PRE_ORDER, TRACKING, 
    EXECUTED, CANCELLED, PROFIT_TRACKING
};
```

### 3.5 UI 시스템

#### VCL Form 구성:
1. **TMainForm**: 메인 거래 화면
   - 상단 수익 정보 패널
   - 보유 종목 그리드
   - 거래 내역 그리드
   - 우측 상세 정보 패널

2. **실시간 업데이트**
   ```cpp
   void __fastcall RefreshTimerTimer(TObject* Sender);
   void UpdateTotalInfo();
   void UpdateHoldingsGrid();
   ```

3. **색상 코딩**
   ```cpp
   TColor GetProfitColor(double profitRate);
   ```

## 4. 성능 최적화 전략

### 4.1 메모리 관리
- 스마트 포인터 활용 (std::shared_ptr, std::unique_ptr)
- 링 버퍼로 메모리 사용량 제한
- COM 객체 적절한 해제

### 4.2 실시간 처리 최적화
- 멀티스레딩으로 UI 블로킹 방지
- 데이터 큐잉으로 처리 순서 보장
- 비동기 로깅으로 I/O 최적화

### 4.3 데이터베이스 최적화
- FireDAC 연결 풀링
- 배치 처리로 DB 부하 감소
- 인덱스 최적화

## 5. 테스트 전략

### 5.1 단위 테스트
- 각 클래스별 기능 테스트
- 기술적 지표 계산 정확성 검증
- 주문 상태 전환 테스트

### 5.2 통합 테스트
- XingAPI 연동 테스트
- 실시간 데이터 처리 테스트
- 멀티스레딩 안정성 테스트

### 5.3 성능 테스트
- 대용량 데이터 처리 성능
- 메모리 누수 검사
- CPU 사용률 모니터링

## 6. 배포 및 운영

### 6.1 빌드 설정
- Release 모드 최적화
- 의존성 라이브러리 포함
- 설치 패키지 생성

### 6.2 운영 고려사항
- 로그 파일 관리
- 설정 백업/복원
- 자동 재시작 기능
- 모니터링 시스템

## 7. 위험 요소 및 대응 방안

### 7.1 기술적 위험
- **COM 인터페이스 호환성**: XingAPI 버전 변경 시 대응
- **멀티스레딩 복잡성**: 철저한 동기화 및 테스트
- **성능 저하**: 프로파일링 도구 활용

### 7.2 대응 방안
- 단계적 포팅으로 위험 분산
- 철저한 테스트 케이스 작성
- 롤백 계획 수립

## 8. 일정 및 마일스톤

### Phase 1 (1-2주): 기반 구조
- XingAPI COM 래퍼 완성
- 기본 멀티스레딩 구조
- 단순 UI 프로토타입

### Phase 2 (2-3주): 핵심 기능
- 주문 관리 시스템
- 기술적 지표 엔진
- 실시간 데이터 처리

### Phase 3 (1-2주): 고도화
- UI 완성 및 최적화
- 성능 튜닝
- 테스트 및 디버깅

### Phase 4 (1주): 배포 준비
- 문서화 완성
- 설치 패키지 제작
- 운영 매뉴얼 작성

## 결론

이 계획서는 Python OptionTrader를 C++Builder로 성공적으로 포팅하기 위한 구체적인 로드맵을 제시합니다. 단계적 접근과 철저한 테스트를 통해 안정적이고 고성능의 거래 시스템을 구축할 수 있을 것입니다.