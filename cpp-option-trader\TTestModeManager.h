#ifndef TTestModeManagerH
#define TTestModeManagerH

#include <System.Classes.hpp>
#include <memory>
#include <vector>
#include "DataTypes.h"
#include "TUtils.h"
#include "TVirtualMarket.h"
#include "TVirtualExecutionEngine.h"
#include "TMarketDataRecorder.h"
#include "TBacktestingFramework.h"

enum TTestMode {
    tmLive,        // 실시간 거래 모드
    tmReplay,      // 기록된 데이터 재생 모드
    tmBacktest,    // 백테스트 모드
    tmSimulation   // 시뮬레이션 모드
};

struct TTestModeConfig {
    TTestMode mode;
    bool recordData;           // 데이터 기록 여부
    bool useVirtualExecution;  // 가상 체결 사용 여부
    AnsiString dataFolder;     // 데이터 폴더 경로
    TDateTime replayStartDate; // 재생 시작일
    TDateTime replayEndDate;   // 재생 종료일
    double playbackSpeed;      // 재생 속도
    std::vector<AnsiString> targetCodes; // 대상 종목
};

class TTestModeManager : public TObject
{
private:
    std::unique_ptr<TUtils> FUtils;
    std::unique_ptr<TMarketDataRecorder> FDataRecorder;
    std::unique_ptr<TVirtualMarket> FVirtualMarket;
    std::unique_ptr<TVirtualExecutionEngine> FVirtualExecution;
    std::unique_ptr<TBacktestingFramework> FBacktestFramework;
    
    TTestModeConfig FConfig;
    TTestMode FCurrentMode;
    bool FIsInitialized;
    
    void InitializeLiveMode();
    void InitializeReplayMode();
    void InitializeBacktestMode();
    void InitializeSimulationMode();
    void CleanupCurrentMode();
    
public:
    __fastcall TTestModeManager();
    __fastcall ~TTestModeManager();
    
    // 이벤트 핸들러 (실제 시스템과 동일한 인터페이스)
    TOnHogaReceived OnHogaReceived;
    TOnJeobsuReceived OnJeobsuReceived;
    TOnOrderStatusChanged OnOrderStatusChanged;
    TOnOrderFilled OnOrderFilled;
    TOnPositionUpdated OnPositionUpdated;
    TOnErrorOccurred OnErrorOccurred;
    TNotifyEvent OnModeChanged;
    
    // 모드 관리
    void SetConfig(const TTestModeConfig& config);
    void SwitchMode(TTestMode newMode);
    TTestMode GetCurrentMode() const { return FCurrentMode; }
    bool IsVirtualMode() const;
    
    // 데이터 기록 (라이브 모드에서)
    void StartDataRecording();
    void StopDataRecording();
    void RecordHoga(const THogaData& hoga);
    void RecordJeobsu(const TJeobsuData& jeobsu);
    
    // 재생 제어 (재생/백테스트 모드에서)
    void StartPlayback();
    void StopPlayback();
    void PausePlayback();
    void ResumePlayback();
    void SeekTo(TDateTime time);
    void SetPlaybackSpeed(double speed);
    
    // 주문 처리 (모든 모드에서 통일된 인터페이스)
    AnsiString SubmitOrder(const TOrderInfo& order);
    bool CancelOrder(const AnsiString& orderNo);
    bool ModifyOrder(const AnsiString& orderNo, double newPrice, int newQuantity);
    
    // 시장 데이터 조회
    THogaData GetCurrentHoga(const AnsiString& code);
    TJeobsuData GetCurrentJeobsu(const AnsiString& code);
    double GetCurrentPrice(const AnsiString& code);
    
    // 백테스트 관련
    void SetBacktestParameters(const TBacktestParameters& params);
    void StartBacktest();
    TBacktestResult GetBacktestResult();
    
    // 상태 조회
    bool IsRecording() const;
    bool IsPlaying() const;
    double GetPlaybackProgress() const;
    TDateTime GetCurrentTime() const;
    
    // 데이터 관리
    std::vector<AnsiString> GetAvailableDates();
    std::vector<AnsiString> GetAvailableCodes(TDateTime date);
    void CleanupOldData(int daysToKeep);
    
    // 설정 관리
    void SaveConfig(const AnsiString& fileName);
    void LoadConfig(const AnsiString& fileName);
    TTestModeConfig GetConfig() const { return FConfig; }
};

#endif