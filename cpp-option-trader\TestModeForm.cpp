#include <vcl.h>
#pragma hdrstop
#include "TestModeForm.h"
#include <SysUtils.hpp>
#include <FileCtrl.hpp>

#pragma package(smart_init)
#pragma resource "*.dfm"

TTestModeForm* TestModeForm;

__fastcall TTestModeForm::TTestModeForm(TComponent* Owner)
    : TForm(Owner), FSelectedMode(tmLive)
{
}

void __fastcall TTestModeForm::FormCreate(TObject* Sender)
{
    try {
        FTestManager = std::make_unique<TTestModeManager>();
        FTestManager->OnModeChanged = OnModeChanged;
        FTestManager->OnHogaReceived = OnTestHogaReceived;
        FTestManager->OnJeobsuReceived = OnTestJeobsuReceived;
        
        // UI 초기화
        DatePickerStart->Date = Date() - 7;
        DatePickerEnd->Date = Date();
        EditSpeed->Text = "1.0";
        EditDataFolder->Text = ExtractFilePath(Application->ExeName) + "dataFolder";
        
        // 기본 종목 추가
        ListBoxCodes->Items->Add("201XXXX"); // 예시 옵션 코드
        
        // 그리드 초기화
        GridSummary->ColCount = 2;
        GridSummary->RowCount = 10;
        GridSummary->Cells[0][0] = "항목";
        GridSummary->Cells[1][0] = "값";
        
        GridTrades->ColCount = 7;
        GridTrades->RowCount = 1;
        GridTrades->Cells[0][0] = "시간";
        GridTrades->Cells[1][0] = "코드";
        GridTrades->Cells[2][0] = "구분";
        GridTrades->Cells[3][0] = "수량";
        GridTrades->Cells[4][0] = "가격";
        GridTrades->Cells[5][0] = "상태";
        GridTrades->Cells[6][0] = "손익";
        
        RadioButtonLive->Checked = true;
        UpdateModeUI();
        UpdateControls();
        
        TimerUpdate->Interval = 1000;
        TimerUpdate->Enabled = true;
        
        LogMessage("테스트 모드 관리자가 초기화되었습니다.");
    }
    catch (const Exception& e) {
        ShowMessage("초기화 오류: " + e.Message);
    }
}

void __fastcall TTestModeForm::FormDestroy(TObject* Sender)
{
    try {
        if (FTestManager) {
            FTestManager->StopPlayback();
            FTestManager->StopDataRecording();
        }
    }
    catch (...) {
        // 정리 중 오류 무시
    }
}

void __fastcall TTestModeForm::RadioButtonModeClick(TObject* Sender)
{
    if (RadioButtonLive->Checked) {
        FSelectedMode = tmLive;
    } else if (RadioButtonReplay->Checked) {
        FSelectedMode = tmReplay;
    } else if (RadioButtonBacktest->Checked) {
        FSelectedMode = tmBacktest;
    } else if (RadioButtonSimulation->Checked) {
        FSelectedMode = tmSimulation;
    }
    
    UpdateModeUI();
}

void __fastcall TTestModeForm::ButtonBrowseFolderClick(TObject* Sender)
{
    AnsiString selectedFolder;
    if (SelectDirectory("데이터 폴더 선택", "", selectedFolder)) {
        EditDataFolder->Text = selectedFolder;
    }
}

void __fastcall TTestModeForm::ButtonAddCodeClick(TObject* Sender)
{
    AnsiString newCode = EditNewCode->Text.Trim();
    if (!newCode.IsEmpty()) {
        if (ListBoxCodes->Items->IndexOf(newCode) == -1) {
            ListBoxCodes->Items->Add(newCode);
            EditNewCode->Text = "";
            LogMessage("종목 추가: " + newCode);
        } else {
            ShowMessage("이미 존재하는 종목입니다.");
        }
    }
}

void __fastcall TTestModeForm::ButtonRemoveCodeClick(TObject* Sender)
{
    int selectedIndex = ListBoxCodes->ItemIndex;
    if (selectedIndex >= 0) {
        AnsiString removedCode = ListBoxCodes->Items->Strings[selectedIndex];
        ListBoxCodes->Items->Delete(selectedIndex);
        LogMessage("종목 제거: " + removedCode);
    }
}

void __fastcall TTestModeForm::ButtonApplyConfigClick(TObject* Sender)
{
    try {
        TTestModeConfig config;
        config.mode = FSelectedMode;
        config.recordData = CheckBoxRecordData->Checked;
        config.useVirtualExecution = CheckBoxVirtualExecution->Checked;
        config.dataFolder = EditDataFolder->Text;
        config.replayStartDate = DatePickerStart->Date;
        config.replayEndDate = DatePickerEnd->Date;
        config.playbackSpeed = StrToFloatDef(EditSpeed->Text, 1.0);
        
        // 종목 리스트 설정
        config.targetCodes.clear();
        for (int i = 0; i < ListBoxCodes->Items->Count; i++) {
            config.targetCodes.push_back(ListBoxCodes->Items->Strings[i]);
        }
        
        FTestManager->SetConfig(config);
        FTestManager->SwitchMode(FSelectedMode);
        
        UpdateControls();
        LogMessage("설정이 적용되었습니다.");
    }
    catch (const Exception& e) {
        ShowMessage("설정 적용 오류: " + e.Message);
    }
}

void __fastcall TTestModeForm::ButtonStartClick(TObject* Sender)
{
    try {
        if (FSelectedMode == tmLive && CheckBoxRecordData->Checked) {
            FTestManager->StartDataRecording();
            LogMessage("데이터 기록을 시작했습니다.");
        } else if (FSelectedMode == tmReplay || FSelectedMode == tmSimulation) {
            FTestManager->StartPlayback();
            LogMessage("데이터 재생을 시작했습니다.");
        } else if (FSelectedMode == tmBacktest) {
            // 백테스트 파라미터 설정 (기본값)
            TBacktestParameters params;
            params.startDate = DatePickerStart->Date;
            params.endDate = DatePickerEnd->Date;
            params.targetCodes = FTestManager->GetConfig().targetCodes;
            params.initialCapital = 10000000; // 1천만원
            params.playbackSpeed = StrToFloatDef(EditSpeed->Text, 1.0);
            
            // 전략 파라미터 (기본값)
            params.stochK = 5;
            params.stochD = 3;
            params.stochSlowing = 3;
            params.cciPeriod = 14;
            params.dmiPeriod = 14;
            params.barInterval = 5;
            
            // 리스크 파라미터
            params.maxPosition = 10;
            params.stopLoss = 100000;
            params.takeProfit = 200000;
            params.maxDailyLoss = 500000;
            
            // 실행 파라미터
            params.slippageRate = 0.001;
            params.partialFillRate = 0.3;
            params.useRealisticExecution = true;
            
            FTestManager->SetBacktestParameters(params);
            FTestManager->StartBacktest();
            LogMessage("백테스트를 시작했습니다.");
        }
        
        UpdateControls();
    }
    catch (const Exception& e) {
        ShowMessage("시작 오류: " + e.Message);
    }
}

void __fastcall TTestModeForm::ButtonStopClick(TObject* Sender)
{
    try {
        FTestManager->StopPlayback();
        FTestManager->StopDataRecording();
        
        if (FSelectedMode == tmBacktest) {
            TBacktestResult result = FTestManager->GetBacktestResult();
            DisplayBacktestResult(result);
        }
        
        UpdateControls();
        LogMessage("작업을 중지했습니다.");
    }
    catch (const Exception& e) {
        ShowMessage("중지 오류: " + e.Message);
    }
}

void __fastcall TTestModeForm::ButtonPauseClick(TObject* Sender)
{
    try {
        if (ButtonPause->Caption == "일시정지") {
            FTestManager->PausePlayback();
            ButtonPause->Caption = "재개";
            LogMessage("재생을 일시정지했습니다.");
        } else {
            FTestManager->ResumePlayback();
            ButtonPause->Caption = "일시정지";
            LogMessage("재생을 재개했습니다.");
        }
    }
    catch (const Exception& e) {
        ShowMessage("일시정지/재개 오류: " + e.Message);
    }
}

void __fastcall TTestModeForm::TimerUpdateTimer(TObject* Sender)
{
    UpdateStatusUI();
}

void TTestModeForm::UpdateModeUI()
{
    // 모드에 따른 UI 활성화/비활성화
    GroupBoxPlayback->Enabled = (FSelectedMode != tmLive);
    CheckBoxRecordData->Enabled = (FSelectedMode == tmLive);
    CheckBoxVirtualExecution->Enabled = true;
    
    if (FSelectedMode == tmLive) {
        CheckBoxVirtualExecution->Checked = false;
    } else {
        CheckBoxVirtualExecution->Checked = true;
        CheckBoxVirtualExecution->Enabled = false;
    }
    
    PageControlResults->Visible = (FSelectedMode == tmBacktest);
}

void TTestModeForm::UpdateStatusUI()
{
    // 현재 모드 표시
    AnsiString modeStr;
    switch (FTestManager->GetCurrentMode()) {
        case tmLive: modeStr = "실시간"; break;
        case tmReplay: modeStr = "재생"; break;
        case tmBacktest: modeStr = "백테스트"; break;
        case tmSimulation: modeStr = "시뮬레이션"; break;
    }
    LabelCurrentMode->Caption = "현재 모드: " + modeStr;
    
    // 상태 표시
    AnsiString statusStr = "대기";
    if (FTestManager->IsRecording()) {
        statusStr = "데이터 기록 중";
    } else if (FTestManager->IsPlaying()) {
        statusStr = "재생 중";
    }
    LabelStatus->Caption = "상태: " + statusStr;
    
    // 진행률 표시
    double progress = FTestManager->GetPlaybackProgress();
    ProgressBarPlayback->Position = static_cast<int>(progress * 100);
    LabelProgress->Caption = Format("진행률: %.1f%%", ARRAYOFCONST((progress * 100)));
    
    // 현재 시간 표시
    TDateTime currentTime = FTestManager->GetCurrentTime();
    Caption = "테스트 모드 관리 - " + FormatDateTime("yyyy-mm-dd hh:nn:ss", currentTime);
}

void TTestModeForm::UpdateControls()
{
    bool isRunning = FTestManager->IsPlaying() || FTestManager->IsRecording();
    
    ButtonStart->Enabled = !isRunning;
    ButtonStop->Enabled = isRunning;
    ButtonPause->Enabled = isRunning && FTestManager->IsPlaying();
    
    GroupBoxMode->Enabled = !isRunning;
    GroupBoxData->Enabled = !isRunning;
    GroupBoxPlayback->Enabled = !isRunning;
    GroupBoxCodes->Enabled = !isRunning;
}

void TTestModeForm::LoadAvailableCodes()
{
    try {
        std::vector<AnsiString> dates = FTestManager->GetAvailableDates();
        if (!dates.empty()) {
            TDateTime latestDate = StrToDateDef(dates.back(), Date());
            std::vector<AnsiString> codes = FTestManager->GetAvailableCodes(latestDate);
            
            ListBoxCodes->Items->Clear();
            for (const auto& code : codes) {
                ListBoxCodes->Items->Add(code);
            }
        }
    }
    catch (const Exception& e) {
        LogMessage("종목 로드 오류: " + e.Message);
    }
}

void TTestModeForm::DisplayBacktestResult(const TBacktestResult& result)
{
    try {
        // 요약 탭 업데이트
        GridSummary->Cells[0][1] = "초기 자본";
        GridSummary->Cells[1][1] = FormatFloat("#,##0", result.initialCapital);
        
        GridSummary->Cells[0][2] = "최종 자본";
        GridSummary->Cells[1][2] = FormatFloat("#,##0", result.finalCapital);
        
        GridSummary->Cells[0][3] = "총 수익률";
        GridSummary->Cells[1][3] = FormatFloat("0.00%", result.totalReturn * 100);
        
        GridSummary->Cells[0][4] = "최대 드로우다운";
        GridSummary->Cells[1][4] = FormatFloat("0.00%", result.maxDrawdown * 100);
        
        GridSummary->Cells[0][5] = "샤프 비율";
        GridSummary->Cells[1][5] = FormatFloat("0.00", result.sharpeRatio);
        
        GridSummary->Cells[0][6] = "총 거래 수";
        GridSummary->Cells[1][6] = IntToStr(result.totalTrades);
        
        GridSummary->Cells[0][7] = "승률";
        GridSummary->Cells[1][7] = FormatFloat("0.00%", result.winRate * 100);
        
        GridSummary->Cells[0][8] = "손익비";
        GridSummary->Cells[1][8] = FormatFloat("0.00", result.profitFactor);
        
        // 거래 내역 탭 업데이트
        GridTrades->RowCount = result.tradeHistory.size() + 1;
        for (size_t i = 0; i < result.tradeHistory.size(); i++) {
            const TOrderInfo& trade = result.tradeHistory[i];
            GridTrades->Cells[0][i + 1] = FormatDateTime("mm-dd hh:nn", trade.time);
            GridTrades->Cells[1][i + 1] = trade.code;
            GridTrades->Cells[2][i + 1] = trade.side == osLong ? "매수" : "매도";
            GridTrades->Cells[3][i + 1] = IntToStr(trade.filledQty);
            GridTrades->Cells[4][i + 1] = FormatFloat("#,##0", trade.avgFillPrice);
            GridTrades->Cells[5][i + 1] = trade.status;
            GridTrades->Cells[6][i + 1] = ""; // 손익 계산 필요
        }
        
        LogMessage("백테스트 결과가 표시되었습니다.");
    }
    catch (const Exception& e) {
        LogMessage("결과 표시 오류: " + e.Message);
    }
}

void TTestModeForm::LogMessage(const AnsiString& message)
{
    AnsiString timestamp = FormatDateTime("hh:nn:ss", Now());
    AnsiString logEntry = "[" + timestamp + "] " + message;
    
    MemoLog->Lines->Add(logEntry);
    
    // 최대 1000줄 유지
    if (MemoLog->Lines->Count > 1000) {
        MemoLog->Lines->Delete(0);
    }
    
    // 맨 아래로 스크롤
    SendMessage(MemoLog->Handle, WM_VSCROLL, SB_BOTTOM, 0);
}

void TTestModeForm::OnModeChanged(TObject* Sender)
{
    LogMessage("모드가 변경되었습니다: " + IntToStr(static_cast<int>(FTestManager->GetCurrentMode())));
    UpdateStatusUI();
}

void TTestModeForm::OnTestHogaReceived(TObject* Sender, const THogaData& hoga)
{
    // 호가 데이터 수신 로그 (필요시)
}

void TTestModeForm::OnTestJeobsuReceived(TObject* Sender, const TJeobsuData& jeobsu)
{
    // 체결 데이터 수신 로그 (필요시)
}